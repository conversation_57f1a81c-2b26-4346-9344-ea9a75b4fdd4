import { NextRequest, NextResponse } from "next/server";
import { inngest, triggerConfig } from "@repo/engine";
import { getWorkflowEngineConfigById } from "@repo/db";

export async function GET(req: NextRequest){
    const id = req.nextUrl.searchParams.get("id") as string;
    const workflow = await getWorkflowEngineConfigById(id);

    const result = await inngest.send({ name: triggerConfig.event, data: workflow})

    return NextResponse.json({
        eventId: result?.ids?.length > 0 ? result.ids[0] as string : undefined,
    });
}