// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String
  avatar    String?  @default("user") // 头像名称，对应react-icons的图标名称
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model WorkflowConfig {
  id          String   @id @default(cuid())
  name        String
  version     String?
  isActive    Boolean
  nodesInfo   String?
  relation    String?
  createdTime DateTime @default(now())
  updatedTime DateTime @updatedAt
  createUser  String?

  // 关系：多对多
  agentWorkflows  AgentWorkflow[]

  @@map("workflow_config")
}

model ConnectConfig {
  id          String   @id @default(cuid())
  name        String
  ctype       String
  mtype       String? // 连接模型类型：database、llm、http等
  configinfo  String
  createdtime DateTime @default(now())
  updatedtime DateTime @updatedAt
  creator     String?

  // 关系：一个连接配置可以被多个智能体使用
  agents AiAgent[]

  @@map("connect_config")
}

model AiMcp {
  id        String   @id @default(cuid())
  name      String
  type      String
  mcpinfo   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系：多对多关系
  mcps AgentMcp[]

  @@map("ai_mcp")
}

model AiAgent {
  id          String   @id @default(cuid())
  name        String
  description String
  prompt      String? // 智能体提示语
  avatar      String?  @default("user") // 智能体头像，对应react-icons的图标名称
  modelId     String // 选择的具体模型ID，必填
  modelName   String? // 选择的模型名称，用于显示
  connectid   String // connect_config表中的id
  agentinfo   String? // agent配置 json格式
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createUser  String

  // 关系：智能体关联一个连接配置
  connectConfig   ConnectConfig @relation(fields: [connectid], references: [id])
  // 关系：多对多关系 - 一个智能体可以关联多个MCP
  agentMcps       AgentMcp[]
  // 关系：多多多关系 - 一个智能体可以关联多个工作路
  agentWorkflows  AgentWorkflow[]
  // 关系：1对多关系
  threads    AgentThread[]

  @@map("ai_agent")
}

// 智能体与MCP的关联表（实现方式1的改进版）
model AgentMcp {
  id        String   @id @default(cuid())
  agentId   String
  mcpId     String
  createdAt DateTime @default(now())

  // 关系
  agent AiAgent @relation(fields: [agentId], references: [id], onDelete: Cascade)
  mcp   AiMcp   @relation(fields: [mcpId], references: [id], onDelete: Cascade)

  // 确保同一个智能体不会重复关联同一个MCP
  @@unique([agentId, mcpId])
  @@map("agent_mcp")
}


// 智能体与工作流的关联表（实现方式1的改进版）
model AgentWorkflow {
  id          String   @id @default(cuid())
  agentId     String
  workflowId  String
  createdAt   DateTime @default(now())

  // 关系
  agent       AiAgent @relation(fields: [agentId], references: [id], onDelete: Cascade)
  workflow    WorkflowConfig   @relation(fields: [workflowId], references: [id], onDelete: Cascade)

  // 确保同一个智能体不会重复关联同一个MCP
  @@unique([agentId, workflowId])
  @@map("agent_workflow")
}

// 团队表
model Team {
  id        String   @id @default(cuid())
  name      String
  createdAt DateTime @default(now())

  @@unique([id])
  @@map("team")
}

// 团队成员表
model TeamMember {
  id        String   @id @default(cuid())
  teamId    String
  agentId   String
  isLeader  Boolean
  createdAt DateTime @default(now())

  @@unique([id])
  @@map("team_member")
}

// 智能体会话表
model AgentThread {
  id        String   @id @default(uuid())
  agentId   String
  userId    String
  metadata  String   @default("{}")
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  agent AiAgent   @relation(fields: [agentId], references: [id], onDelete: Cascade)

  messages AgentMessage[]

  @@map("agent_thread")
}

model AgentMessage {
  id         Int         @id @default(autoincrement())
  threadId   String
  messageType String
  agentName  String?
  content    String?
  data       String?
  raw        String?
  checksum   String
  createdAt  DateTime  @default(now())

  thread     AgentThread      @relation(fields: [threadId], references: [id], onDelete: Cascade)

  @@unique([threadId, checksum])
  @@map("agent_message")
}