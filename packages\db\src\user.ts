import { prisma } from "./client.js";
import bcrypt from "bcryptjs";
import type { User } from "@prisma/client";

// Types for user operations
export interface CreateUserInput {
  email: string;
  password: string;
  name: string;
  avatar?: string; // 头像名称字符串，如 'user', 'tie', 'robot' 等
}

export interface LoginInput {
  email: string;
  password: string;
}

// Error McpInterfaces.ts
export class UserExistsError extends Error {
  constructor(message = "这个邮箱已被注册") {
    super(message);
    this.name = "UserExistsError";
  }
}

export class UserNotFoundError extends Error {
  constructor(message = "User not found") {
    super(message);
    this.name = "UserNotFoundError";
  }
}

export class InvalidCredentialsError extends Error {
  constructor(message = "Invalid credentials") {
    super(message);
    this.name = "InvalidCredentialsError";
  }
}

// Hash password
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 10);
}

// Compare password with hash
export async function comparePasswords(
  password: string,
  hashedPassword: string
): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// Create a new user
export async function createUser(data: CreateUserInput): Promise<User> {
  // Check if user with this email already exists
  const existingUserByEmail = await prisma.user.findUnique({
    where: { email: data.email },
  });

  if (existingUserByEmail) {
    throw new UserExistsError('这个邮箱已被注册');
  }

  // Hash the password
  const hashedPassword = await hashPassword(data.password);

  // Create the user
  return prisma.user.create({
    data: {
      email: data.email,
      name: data.name,
      password: hashedPassword,
      avatar: data.avatar ?? "user", // 使用传入的avatar值，如果未提供则默认为"user"
    },
  });
}

// Get user by email
export async function getUserByEmail(email: string): Promise<User | null> {
  return prisma.user.findUnique({
    where: { email },
  });
}

// Get user by ID
export async function getUserById(id: string): Promise<User | null> {
  return prisma.user.findUnique({
    where: { id },
  });
}

// Login user
export async function loginUser(data: LoginInput): Promise<User> {
  // Find the user
  const user = await getUserByEmail(data.email);

  if (!user) {
    throw new UserNotFoundError();
  }

  // Verify password
  const isPasswordValid = await comparePasswords(data.password, user.password);

  if (!isPasswordValid) {
    throw new InvalidCredentialsError();
  }

  return user;
}

// Update user
export async function updateUser(
  id: string,
  data: Partial<Omit<User, "id" | "createdAt" | "updatedAt" | "password">>
): Promise<User> {
  return prisma.user.update({
    where: { id },
    data,
  });
}

// Update user password
export async function updateUserPassword(
  id: string,
  newPassword: string
): Promise<User> {
  const hashedPassword = await hashPassword(newPassword);

  return prisma.user.update({
    where: { id },
    data: { password: hashedPassword },
  });
}

// Delete user
export async function deleteUser(id: string): Promise<User> {
  return prisma.user.delete({
    where: { id },
  });
}
