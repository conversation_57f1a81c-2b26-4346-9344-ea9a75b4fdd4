import { NextRequest, NextResponse } from "next/server";
import { agent<PERSON>anager, mcpManager } from "@repo/engine";
import { prisma } from "@repo/db";

export async function GET(req: NextRequest) {
    try {
        // 检查agentManager状态
        const agents = agentManager.agents;
        const agentData = agents.map(agent => ({
            id: agent.id,
            name: agent.config.name,
            handler: agent.handler ? 'exists' : 'missing'
        }));

        // 检查数据库中的agents
        const dbAgents = await prisma.aiAgent.findMany({
            select: {
                id: true,
                name: true,
                connectid: true,
                modelId: true,
                connectConfig: {
                    select: {
                        id: true,
                        name: true,
                        mtype: true,
                        configinfo: true
                    }
                }
            }
        });

        // 检查MCP状态
        const mcpData = mcpManager.servers.map((mcp: any) => ({
            id: mcp.id,
            name: mcp.name,
            type: mcp.type
        }));

        // 检查数据库中的MCP
        const dbMcps = await prisma.aiMcp.findMany({
            select: {
                id: true,
                name: true,
                type: true
            }
        });

        return NextResponse.json({
            success: true,
            data: {
                agentManager: {
                    agentCount: agents.length,
                    agents: agentData,
                    version: agentManager.version
                },
                database: {
                    agents: dbAgents,
                    agentCount: dbAgents.length
                },
                mcpManager: {
                    mcpCount: mcpData.length,
                    mcps: mcpData
                },
                dbMcps: {
                    mcpCount: dbMcps.length,
                    mcps: dbMcps
                }
            }
        });

    } catch (error) {
        console.error('Debug API Error:', error);
        return NextResponse.json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined
        }, { status: 500 });
    }
} 