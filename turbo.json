{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["src/**", "public/**", "prisma/**", ".env*", "next.config.js", "tsconfig.json"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "lint": {"dependsOn": ["^lint"], "outputs": []}, "check-types": {"dependsOn": ["^check-types"], "outputs": []}, "dev": {"cache": false, "persistent": true, "dependsOn": ["^build"]}, "start": {"dependsOn": ["build"], "cache": false}, "clean": {"cache": false}, "format": {"outputs": []}, "db:generate": {"cache": false}, "db:push": {"cache": false}}}