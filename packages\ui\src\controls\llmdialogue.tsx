import React, { useState, useRef, useEffect } from 'react';
import styled, { keyframes, css } from 'styled-components';

// 打字机光标动画
const blink = keyframes`
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
`;

const DialogueContainer = styled.div`
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  height: 98%;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
  overflow: hidden;
`;

const DialogueHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  font-weight: 500;
  color: #333;
`;

const StatusIndicator = styled.div<{ status: 'idle' | 'loading' | 'error' }>`
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: ${props => {
    switch (props.status) {
      case 'loading': return '#1890ff';
      case 'error': return '#ff4d4f';
      default: return '#52c41a';
    }
  }};

  &::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
  }
`;

const DialogueMessages = styled.div`
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const Message = styled.div<{ role: 'user' | 'assistant' | 'system' }>`
  display: flex;
  flex-direction: column;
  align-items: ${props => props.role === 'user' ? 'flex-end' : 'flex-start'};
  gap: 4px;
`;

const MessageInfo = styled.div`
  font-size: 11px;
  color: #999;
  padding: 0 8px;
`;

// 新增样式组件用于区分推理和常规内容
const ReasoningContent = styled.span`
  color: #bfbfbf;
  font-size: 12px;
  line-height: 1.4;
  display: block;
  margin-bottom: 8px;
  
  &::before {
    content: '💭 ';
    color: #bfbfbf;
    font-size: 12px;
  }
`;

const RegularContent = styled.span`
  color: #000000;
  font-size: 13px;
  line-height: 1.5;
  display: block;
`;

// 修改MessageBubble以支持混合内容显示
const MessageContent = styled.div`
  word-wrap: break-word;
  white-space: pre-wrap;
`;

const MessageBubble = styled.div<{ role: 'user' | 'assistant' | 'system'; $isStreaming?: boolean; $isError?: boolean; $hasReasoning?: boolean }>`
  max-width: 70%;
  padding: 12px 16px;
  border-radius: ${props => {
    if (props.role === 'user') return '16px 16px 4px 16px';
    if (props.role === 'system') return '8px';
    return '16px 16px 16px 4px';
  }};
  background: ${props => {
    if (props.$isError) return '#fff2f0';
    if (props.role === 'user') return '#33C2EE';
    if (props.role === 'system') return '#f0f0f0';
    if (props.$hasReasoning) return '#f8f9fa';
    return 'white';
  }};
  color: ${props => {
    if (props.$isError) return '#ff4d4f';
    if (props.role === 'user') return 'white';
    return '#333';
  }};
  border: ${props => {
    if (props.$isError) return '1px solid #ffccc7';
    if (props.role === 'assistant') return '1px solid #e0e0e0';
    return 'none';
  }};
  word-wrap: break-word;
  white-space: pre-wrap;
  line-height: 1.5;
  position: relative;

  ${props => props.$isStreaming && css`
    &::after {
      content: '|';
      animation: ${blink} 1s infinite;
      font-weight: bold;
      margin-left: 2px;
    }
  `}
`;

const InputContainer = styled.div`
  padding: 16px;
  background: white;
  border-top: 1px solid #e0e0e0;
`;

const InputRow = styled.div`
  display: flex;
  gap: 8px;
  align-items: flex-end;
`;

const TextInput = styled.textarea`
  flex: 1;
  min-height: 40px;
  max-height: 120px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.4;
  resize: vertical;
  outline: none;
  font-family: inherit;

  &:focus {
    border-color: #33C2EE;
    box-shadow: 0 0 0 2px rgba(51, 194, 238, 0.1);
  }

  &:disabled {
    background: #f5f5f5;
    color: #999;
  }
`;

const SendButton = styled.button`
  padding: 12px 20px;
  background: #33C2EE;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  height: 40px;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: #2AA8CC;
  }

  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
`;

const ClearButton = styled.button`
  background: transparent;
  border: 1px solid #e0e0e0;
  color: #666;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #33C2EE;
    color: #33C2EE;
  }
`;

export interface DialogueMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface LLMDialogueProps {
  title?: string;
  placeholder?: string;
  disabled?: boolean;
  onSendMessage?: (message: string) => Promise<string>;
  onTest?: (config: Record<string, any>, message: string) => Promise<any>;
  //是否推理or常规nei
  onStreamTest?: (config: Record<string, any>, message: string, onChunk: (chunk: string, type?: 'reasoning' | 'content') => void) => Promise<any>;
  connectConfig?: Record<string, any>;
  maxMessages?: number;
  onClose?: () => void;
}

// 定义chunk类型
interface StreamChunk {
  content: string;
  type?: 'reasoning' | 'content';
}

export const LLMDialogue: React.FC<LLMDialogueProps> = ({
  title = "大模型对话测试",
  placeholder = "输入您的问题...",
  disabled = false,
  onSendMessage,
  onTest,
  onStreamTest,
  connectConfig,
  maxMessages = 50,
  onClose
}) => {
  const [messages, setMessages] = useState<DialogueMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState<'idle' | 'loading' | 'error'>('idle');
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null);
  const messageCounterRef = useRef(0);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const generateMessageId = () => {
    messageCounterRef.current += 1;
    return `${Date.now()}${messageCounterRef.current}${Math.random().toString(36).substr(2, 6)}`;
  };

  const addMessage = (role: DialogueMessage['role'], content: string, metadata?: Record<string, any>, messageId?: string) => {
    const message: DialogueMessage = {
      id: messageId || generateMessageId(),
      role,
      content,
      timestamp: new Date(),
      metadata
    };

    setMessages(prev => {
      const newMessages = [...prev, message];
      // 限制消息数量
      if (newMessages.length > maxMessages) {
        return newMessages.slice(-maxMessages);
      }
      return newMessages;
    });

    return message.id;
  };

  const updateMessage = (messageId: string, content: string, metadata?: Record<string, any>) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId 
        ? { ...msg, content, metadata: { ...msg.metadata, ...metadata } }
        : msg
    ));
  };

  const handleSend = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage = inputValue.trim();
    setInputValue('');
    setIsLoading(true);
    setStatus('loading');

    // 添加用户消息
    addMessage('user', userMessage);

    // 检查是否启用流式输出
    const useStream = connectConfig?.stream && onStreamTest;

    try {
      if (useStream) {
        // 流式输出处理
        console.log('🚀 启动流式对话...');
        
        // 创建助手消息占位符
        const assistantMessageId = addMessage('assistant', '', {
          isStreaming: true,
          model: connectConfig.model
        });
        setStreamingMessageId(assistantMessageId);

        let streamContent = '';
        let reasoningContent = '';
        let regularContent = '';
        let hasReasoning = false; // 追踪是否包含推理过程
        let isInReasoning = false; // 当前是否在推理过程中
        
        const result = await onStreamTest(connectConfig, userMessage, (chunk: string, type?: 'reasoning' | 'content') => {
          streamContent += chunk;
          
          // 根据传入的类型参数分别处理推理和常规内容
          if (type === 'reasoning') {
            reasoningContent += chunk;
            hasReasoning = true;
          } else if (type === 'content') {
            regularContent += chunk;
          } else {
            // 兼容性处理：如果没有类型信息，使用旧的分析逻辑
            const isLikelyReasoning = chunk.length < 50 && !chunk.includes('。') && !chunk.includes('!') && !chunk.includes('?');
            
            if (isLikelyReasoning || isInReasoning) {
              reasoningContent += chunk;
              hasReasoning = true;
              isInReasoning = true;
              
              // 如果遇到明显的结束标志，切换到常规内容
              if (chunk.includes('。') || chunk.includes('！') || chunk.includes('？') || chunk.includes('\n\n')) {
                isInReasoning = false;
              }
            } else {
              regularContent += chunk;
              isInReasoning = false;
            }
          }
          
          updateMessage(assistantMessageId, streamContent, {
            isStreaming: true,
            model: connectConfig.model,
            hasReasoning: hasReasoning,
            reasoningContent,
            regularContent
          });
        });

        // 流式完成后更新最终状态
        if (result.success) {
          updateMessage(assistantMessageId, streamContent || result.response, {
            isStreaming: false,
            latency: result.latency,
            model: connectConfig.model,
            tokens: result.details?.tokens,
            streamComplete: true,
            hasReasoning: hasReasoning
          });
        } else {
          // 流式失败，显示错误
          updateMessage(assistantMessageId, `流式输出失败: ${result.message}`, {
            isStreaming: false,
            isError: true
          });
        }
        
        setStreamingMessageId(null);
      } else {
        // 传统非流式处理
        let response: string;

        if (onTest && connectConfig) {
          // 使用连接测试接口
          const result = await onTest(connectConfig, userMessage);
          
          if (result.success) {
            response = result.response || result.message || '模型响应成功，但未返回内容';
            addMessage('assistant', response, {
              latency: result.latency,
              model: connectConfig.model,
              tokens: result.details?.tokens,
              isStreaming: false
            });
          } else {
            addMessage('system', `测试失败: ${result.message || '未知错误'}`);
            setStatus('error');
            return;
          }
        } else if (onSendMessage) {
          // 使用自定义消息处理器
          response = await onSendMessage(userMessage);
          addMessage('assistant', response);
        } else {
          // 默认响应
          addMessage('system', '未配置消息处理器，无法发送消息');
          setStatus('error');
          return;
        }
      }

      setStatus('idle');
    } catch (error) {
      console.error('发送消息失败:', error);
      
      if (streamingMessageId) {
        updateMessage(streamingMessageId, `发送失败: ${error instanceof Error ? error.message : '未知错误'}`, {
          isStreaming: false,
          isError: true
        });
        setStreamingMessageId(null);
      } else {
        addMessage('system', `发送失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
      
      setStatus('error');
    } finally {
      setIsLoading(false);
      // 重新聚焦输入框
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleClear = () => {
    setMessages([]);
    setStatus('idle');
  };

  const getStatusText = () => {
    switch (status) {
      case 'loading': return '正在思考...';
      case 'error': return '连接异常';
      default: return '就绪';
    }
  };

  // 渲染混合内容的函数
  const renderMixedContent = (content: string, metadata?: Record<string, any>) => {
    if (!content) return null;
    
    // 如果有分离的推理和常规内容，优先使用
    if (metadata?.reasoningContent || metadata?.regularContent) {
      const elements: React.ReactElement[] = [];
      
      // 先渲染推理内容
      if (metadata.reasoningContent) {
        elements.push(
          <ReasoningContent key="reasoning">
            {metadata.reasoningContent}
          </ReasoningContent>
        );
      }
      
      // 再渲染常规内容
      if (metadata.regularContent) {
        elements.push(
          <RegularContent key="content">
            {metadata.regularContent}
          </RegularContent>
        );
      }
      
      return elements.length > 0 ? elements : <RegularContent>{content}</RegularContent>;
    }
    
    // 兼容性处理：如果没有分离的内容，使用启发式分析
    const paragraphs = content.split(/\n{2,}/)
      .map(p => p?.trim())
      .filter(p => p && p.length > 0);
    
    const elements: React.ReactElement[] = [];
    
    paragraphs.forEach((paragraph, i) => {
      if (!paragraph) return;
      
      // 检测是否是推理过程
      const isReasoning = (
        paragraph.length < 20 ||
        /^[，。！？、：；""''（）]+$/.test(paragraph) ||
        /^[一-龟]{1,3}[，。]?$/.test(paragraph) ||
        /^(嗯|啊|哦|是|对|好|那|这|让我|首先|然后|接下来|总之|所以)[，。]?$/.test(paragraph) ||
        paragraph.includes('思考') || paragraph.includes('考虑') || paragraph.includes('分析')
      );
      
      if (isReasoning) {
        elements.push(
          <ReasoningContent key={`reasoning-${i}`}>
            {paragraph}
          </ReasoningContent>
        );
      } else {
        elements.push(
          <RegularContent key={`content-${i}`}>
            {paragraph}
          </RegularContent>
        );
      }
    });
    
    return elements.length > 0 ? elements : <RegularContent>{content}</RegularContent>;
  };

  return (
    <DialogueContainer>
      <DialogueHeader>
        <span>{title}</span>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <StatusIndicator status={status}>
            {getStatusText()}
          </StatusIndicator>
          {messages.length > 0 && (
            <ClearButton onClick={handleClear}>
              清空对话
            </ClearButton>
          )}
          {onClose && (
            <ClearButton onClick={onClose}>
              关闭对话
            </ClearButton>
          )}
        </div>
      </DialogueHeader>

      <DialogueMessages>
        {messages.length === 0 && (
          <MessageBubble role="system">
            开始与大模型对话吧！输入您的问题来测试连接是否正常工作。
          </MessageBubble>
        )}
        
        {messages.map((message) => (
          <Message key={message.id} role={message.role}>
            <MessageInfo>
              {message.role === 'user' ? '您' : message.role === 'assistant' ? '助手' : '系统'} · 
              {message.timestamp.toLocaleTimeString()}
              {message.metadata?.latency && ` · ${message.metadata.latency}ms`}
              {message.metadata?.model && ` · ${message.metadata.model}`}
              {message.metadata?.tokens && ` · ${message.metadata.tokens} tokens`}
              {message.metadata?.isStreaming && ' · 流式输出中...'}
              {message.metadata?.streamComplete && ' · 流式完成'}
            </MessageInfo>
            <MessageBubble 
              role={message.role} 
              $isStreaming={message.metadata?.isStreaming}
              $isError={message.metadata?.isError}
              $hasReasoning={message.metadata?.hasReasoning}
            >
              <MessageContent>
                {renderMixedContent(message.content || (message.metadata?.isStreaming ? '正在生成回复...' : ''), message.metadata)}
              </MessageContent>
            </MessageBubble>
          </Message>
        ))}
        <div ref={messagesEndRef} />
      </DialogueMessages>

      <InputContainer>
        <InputRow>
          <TextInput
            ref={inputRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            disabled={disabled || isLoading}
            maxLength={2000}
          />
          <SendButton
            onClick={handleSend}
            disabled={disabled || isLoading || !inputValue.trim()}
          >
            {isLoading ? '发送中...' : '发送'}
          </SendButton>
        </InputRow>
      </InputContainer>
    </DialogueContainer>
  );
};

export default LLMDialogue; 