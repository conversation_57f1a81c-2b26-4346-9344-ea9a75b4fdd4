"use client";

import React from 'react';
import styled from 'styled-components';
import {
  Header,
  Title,
  SubTitle,
  ContentArea,
  ThemeSwitch,
  PerformanceProvider,
  usePerformance,
  PerformanceMonitor,
  type PerformanceLevel
} from '@repo/ui/main';

const SettingsContainer = styled.div`
  padding: 20px;
`;

const SettingsCard = styled.div`
  padding: 16px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 8px;
  margin-bottom: 16px;
  background-color: ${({ theme }) => theme.colors.cardBg};
`;

const CardTitle = styled.h3`
  margin: 0 0 8px 0;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const CardDescription = styled.p`
  margin: 0;
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: 14px;
`;

// 性能设置组件
const PerformanceSettings: React.FC = () => {
  const {
    animationLevel,
    particlesEnabled,
    backgroundAnimationEnabled,
    blurEffectsEnabled,
    setAnimationLevel,
    setParticlesEnabled,
    setBackgroundAnimationEnabled,
    setBlurEffectsEnabled,
    autoDetectPerformance
  } = usePerformance();

  const performanceLevels: { value: PerformanceLevel; label: string; description: string; component: string }[] = [
    {
      value: 'low',
      label: '静态SVG粒子',
      description: '静态SVG粒子，极低GPU消耗 ⭐推荐',
      component: 'UltraLowGPUParticles'
    },
    {
      value: 'disabled',
      label: '完全禁用',
      description: '关闭所有视觉效果，最低资源消耗',
      component: 'None'
    }
  ];

  return (
    <div style={{ 
      padding: '20px', 
      background: 'rgba(15, 23, 42, 0.3)',
      backdropFilter: 'blur(10px)',
      borderRadius: '12px',
      margin: '20px',
      color: 'white'
    }}>
      <h2 style={{ marginBottom: '20px', color: '#3b82f6' }}>⚡ 性能设置</h2>
      
      {/* 动画级别设置 */}
      <div style={{ marginBottom: '30px' }}>
        <h3 style={{ marginBottom: '15px' }}>动画质量级别</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
          {performanceLevels.map(level => (
            <label key={level.value} style={{
              display: 'flex',
              alignItems: 'center',
              padding: '12px',
              background: animationLevel === level.value ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.1)',
              borderRadius: '8px',
              cursor: 'pointer',
              border: animationLevel === level.value ? '2px solid #3b82f6' : '2px solid transparent',
              transition: 'all 0.3s ease'
            }}>
              <input
                type="radio"
                name="animationLevel"
                value={level.value}
                checked={animationLevel === level.value}
                onChange={(e) => setAnimationLevel(e.target.value as PerformanceLevel)}
                style={{ marginRight: '10px' }}
              />
              <div>
                <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{level.label}</div>
                <div style={{ fontSize: '12px', opacity: 0.8 }}>{level.description}</div>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* 详细设置 */}
      <div style={{ marginBottom: '30px' }}>
        <h3 style={{ marginBottom: '15px' }}>详细动画控制</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
          <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
            <input
              type="checkbox"
              checked={particlesEnabled}
              onChange={(e) => setParticlesEnabled(e.target.checked)}
              style={{ marginRight: '10px', transform: 'scale(1.2)' }}
            />
            <div>
              <div style={{ fontWeight: 'bold' }}>液态粒子效果</div>
              <div style={{ fontSize: '12px', opacity: 0.8 }}>
                背景浮动粒子动画 {particlesEnabled ? '🟢' : '🔴'}
              </div>
            </div>
          </label>

          <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
            <input
              type="checkbox"
              checked={backgroundAnimationEnabled}
              onChange={(e) => setBackgroundAnimationEnabled(e.target.checked)}
              style={{ marginRight: '10px', transform: 'scale(1.2)' }}
            />
            <div>
              <div style={{ fontWeight: 'bold' }}>背景动画</div>
              <div style={{ fontSize: '12px', opacity: 0.8 }}>
                液态背景流动效果 {backgroundAnimationEnabled ? '🟢' : '🔴'}
              </div>
            </div>
          </label>

          <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
            <input
              type="checkbox"
              checked={blurEffectsEnabled}
              onChange={(e) => setBlurEffectsEnabled(e.target.checked)}
              style={{ marginRight: '10px', transform: 'scale(1.2)' }}
            />
            <div>
              <div style={{ fontWeight: 'bold' }}>模糊特效</div>
              <div style={{ fontSize: '12px', opacity: 0.8 }}>
                毛玻璃和模糊滤镜效果 {blurEffectsEnabled ? '🟢' : '🔴'}
              </div>
            </div>
          </label>
        </div>
      </div>

      {/* 自动检测按钮 */}
      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={autoDetectPerformance}
          style={{
            padding: '12px 20px',
            background: 'linear-gradient(135deg, #3b82f6, #8b5cf6)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontWeight: 'bold',
            transition: 'all 0.3s ease'
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.transform = 'scale(1.05)';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.transform = 'scale(1)';
          }}
        >
          🔍 自动检测最佳性能设置
        </button>
      </div>

      {/* GPU消耗对比 */}
      <div style={{ marginBottom: '30px' }}>
        <h3 style={{ marginBottom: '15px', color: '#3b82f6' }}>📊 性能消耗对比</h3>
        <div style={{ display: 'grid', gap: '10px' }}>
          <div style={{ display: 'flex', alignItems: 'center', padding: '10px', background: 'rgba(34, 197, 94, 0.1)', borderRadius: '6px' }}>
            <div style={{ width: '120px', fontWeight: 'bold' }}>静态SVG:</div>
            <div style={{ width: '60px', height: '8px', background: '#22c55e', borderRadius: '4px' }}></div>
            <div style={{ marginLeft: '10px', fontSize: '12px' }}>~10% GPU使用 ⭐推荐</div>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', padding: '10px', background: 'rgba(75, 85, 99, 0.1)', borderRadius: '6px' }}>
            <div style={{ width: '120px', fontWeight: 'bold' }}>完全禁用:</div>
            <div style={{ width: '20px', height: '8px', background: '#6b7280', borderRadius: '4px' }}></div>
            <div style={{ marginLeft: '10px', fontSize: '12px' }}>~2% GPU使用</div>
          </div>
        </div>
      </div>

      {/* 性能提示 */}
      <div style={{
        padding: '15px',
        background: 'rgba(34, 197, 94, 0.1)',
        borderRadius: '8px',
        borderLeft: '4px solid #22c55e'
      }}>
        <h4 style={{ margin: '0 0 8px 0', color: '#22c55e' }}>💡 使用建议</h4>
        <ul style={{ margin: 0, paddingLeft: '20px', fontSize: '14px' }}>
          <li><strong>推荐：</strong> 使用"静态SVG粒子"，低资源消耗且保持视觉效果</li>
          <li>静态SVG采用高效克隆技术，适合所有设备类型</li>
          <li>如果追求极致性能或设备配置较低，可选择"完全禁用"</li>
          <li>两种模式都能提供流畅的用户体验</li>
        </ul>
      </div>

      {/* 开发环境显示性能监控 */}
      {process.env.NODE_ENV === 'development' && <PerformanceMonitor />}
    </div>
  );
};

export default function SystemPage() {
  return (
    <PerformanceProvider>
      <div style={{ 
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)',
        padding: '20px'
      }}>
        <div style={{ maxWidth: '800px', margin: '0 auto' }}>
          <h1 style={{ 
            color: 'white', 
            textAlign: 'center', 
            marginBottom: '30px',
            fontSize: '2.5rem',
            background: 'linear-gradient(135deg, #3b82f6, #8b5cf6)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}>
            系统设置
          </h1>
          
          <PerformanceSettings />
          
          <SettingsContainer>
            <ThemeSwitch />
            
            <SettingsCard>
              <CardTitle>其他设置</CardTitle>
              <CardDescription>
                更多系统配置选项即将推出...
              </CardDescription>
            </SettingsCard>
            
            <SettingsCard>
              <CardTitle>主题预览</CardTitle>
              <CardDescription>
                当前主题会自动应用到整个应用界面，包括侧边栏、按钮、卡片等所有组件。
              </CardDescription>
            </SettingsCard>
          </SettingsContainer>
          
          {/* 其他设置区域可以在这里添加 */}
          <div style={{
            padding: '20px',
            background: 'rgba(15, 23, 42, 0.3)',
            backdropFilter: 'blur(10px)',
            borderRadius: '12px',
            margin: '20px',
            color: 'white',
            textAlign: 'center'
          }}>
            <h3 style={{ color: '#3b82f6' }}>更多设置即将推出</h3>
            <p style={{ opacity: 0.8 }}>主题设置、语言设置、通知设置等功能正在开发中...</p>
          </div>
        </div>
      </div>
    </PerformanceProvider>
  );
}