import { ILLMConnect, ILLMOverview, ConnectTestResult } from '@repo/common';
import { 
    createApiKeyField, 
    createBaseUrlField, 
    testLLMConnection
} from '../utils/llm-fields';

const ConnectConfig: ILLMOverview = {
    id: 'openai',
    name: 'OpenAI',
    type: 'llm' as const,
    provider: 'openai',
    icon: 'openai.svg',
    tags: ["international"],
    description: 'OpenAI GPT模型连接',
    version: '1.0.0',
    api: { url: 'https://api.openai.com/v1', suffix: '/chat/completions' },
    driver: 'openai',
    about: {
        apiHost: 'https://api.openai.com',
        docUrl: 'https://platform.openai.com/docs',
        modelUrl: 'https://platform.openai.com/docs/models',
        getKeyUrl: 'https://platform.openai.com/api-keys'
    }
};

export const OpenAIConnect: ILLMConnect = {
    overview: ConnectConfig,
    detail: {
        supportedModels: [
            {id: 'gpt-4o', name: 'gpt-4o',group: 'OpenAI'},
            {id: 'gpt-4o-mini', name: 'gpt-4o-mini',group: 'OpenAI'},
            {id: 'gpt-4-turbo', name: 'gpt-4-turbo',group: 'OpenAI'},
            {id: 'gpt-4', name: 'gpt-4',group: 'OpenAI'},
            {id: 'gpt-3.5-turbo', name: 'gpt-3.5-turbo',group: 'OpenAI'},
            {id: 'text-embedding-3-large', name: 'text-embedding-3-large',group: 'OpenAI'},
            {id: 'text-embedding-3-small', name: 'text-embedding-3-small',group: 'OpenAI'},
            {id: 'text-embedding-ada-002', name: 'text-embedding-ada-002',group: 'OpenAI'}
        ],
        fields: [
            createApiKeyField('sk-...'),
            createBaseUrlField(ConnectConfig.api.url),
        ],
        validateConnection: true,
        connectionTimeout: 10
    },

    async test(config: Record<string, any>, message?: string): Promise<ConnectTestResult> {
        return testLLMConnection(
            ConnectConfig.id,
            config,
            ConnectConfig.api.url+ConnectConfig.api.suffix||'',
            message
        );
    }
}; 