import React from 'react';
import { SearchAndFilterProps } from './types';
import {
  SearchAndFilterContainer,
  SearchInput,
  FilterContainer,
  SortSelect,
} from './styles';

export const SearchAndFilter: React.FC<SearchAndFilterProps> = ({
  searchTerm,
  onSearchChange,
  sortBy,
  onSortChange,
}) => {
  return (
    <SearchAndFilterContainer>
      <SearchInput
        type="text"
        placeholder="🔍 Search"
        value={searchTerm}
        onChange={(e) => onSearchChange(e.target.value)}
      />
      <FilterContainer>
        <SortSelect value={sortBy} onChange={(e) => onSortChange(e.target.value)}>
          <option value="last-updated">Sort by last updated</option>
          <option value="name">Sort by name</option>
        </SortSelect>
        <button style={{ background: 'none', border: 'none', padding: '8px' }}>
          🔽
        </button>
        <button style={{ background: 'none', border: 'none', padding: '8px' }}>
          📋
        </button>
      </FilterContainer>
    </SearchAndFilterContainer>
  );
}; 