import { injectable, multiInject, optional } from 'inversify';
import { INode, INodeBasic,INodeDetail } from './NodeInterfaces';
import { Symbols } from './Symbols';

@injectable()
export class NodeRegistry {
  private nodes: Map<string, INode> = new Map();
  private nodesByCategory: Map<string, INode[]> = new Map();

  constructor(
    @optional() @multiInject(Symbols.NodeType) nodeTypes: INode[]
  ) {
    nodeTypes.forEach(nodeType => this.registerNode(nodeType));
  }

  registerNode(nodeType: INode): void {
    // Validate node structure
    if (!nodeType || typeof nodeType !== 'object') {
      console.warn('Invalid node type provided to registerNode:', nodeType);
      return;
    }
  
    if (!nodeType.node || !nodeType.node.kind) {
      console.warn('Node is missing required "node" or "node.kind" property:', nodeType);
      return;
    }
  
    const kind = nodeType.node.kind;
    this.nodes.set(kind, nodeType);
    
    // Register node by category
    if (nodeType.node?.categories?.length) {
      nodeType.node.categories.forEach(categoryId => {
        if (!this.nodesByCategory.has(categoryId)) {
          this.nodesByCategory.set(categoryId, []);
        }
        this.nodesByCategory.get(categoryId)?.push(nodeType);
      });
    }
  }

  getNodeByKind(kind: string): INode | undefined {
    return this.nodes.get(kind);
  }

  getAllNodes(): INode[] {
    return Array.from(this.nodes.values());
  }

  getAllNodeDetail(): INodeDetail[] {
    return this.getAllNodes().map(node => node.detail);
  }
  
  getNodesByCategory(categoryId: string): INode[] {
    return this.nodesByCategory.get(categoryId) || [];
  }
  
  getNodesByCategoryNotWithDetail(): Record<string, INodeBasic[]> {
    const result: Record<string, INodeBasic[]> = {};
    
    this.nodesByCategory.forEach((nodes, categoryId) => {
      result[categoryId] = nodes.map(node => node.node);
    });
    
    return result;
  }
  
  getNodesByCategoryWithDescriptions(): Record<string, INodeDetail[]> {
    const result: Record<string, INodeDetail[]> = {};
    
    this.nodesByCategory.forEach((nodes, categoryId) => {
      result[categoryId] = nodes.map(node => node.detail);
    });
    
    return result;
  }
}