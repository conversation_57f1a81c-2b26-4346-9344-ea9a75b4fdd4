import React from 'react';
import { WorkflowCardProps } from './types';
import { formatDate } from './utils';
import { FaUserAlt } from "react-icons/fa";
// 导入UI组件样式
import {
  GlassListCards,
  GlassToggleSwitch,
  GlassStatusBadge
} from './ui-components';

export const WorkflowCard: React.FC<WorkflowCardProps> = ({
  workflow,
  onWorkflowClick,
  onToggleWorkflow,
}) => {
  return (
    <GlassListCards 
      onClick={() => onWorkflowClick(workflow.id)}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
        <h3 style={{ 
          fontSize: '16px', 
          fontWeight: '600', 
          margin: '0',
          color: 'inherit'
        }}>
          {workflow.name}
        </h3>
        <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '6px', fontSize: '12px', color: '#C0C0C0' }}>
            <FaUserAlt size={12} />
            Personal
          </div>
          <GlassStatusBadge $active={workflow.isActive}>
            {workflow.isActive ? '启用' : '未启'}
          </GlassStatusBadge>
          <GlassToggleSwitch 
            $active={workflow.isActive} 
            onClick={(e) => {
              e.stopPropagation();
              onToggleWorkflow(workflow.id, workflow.isActive);
            }}
          />
          <button 
            onClick={(e) => e.stopPropagation()}
            style={{
              background: 'none',
              border: 'none',
              color: '#94a3b8',
              fontSize: '18px',
              cursor: 'pointer',
              padding: '4px 8px'
            }}
          >
            ⋯
          </button>
        </div>
      </div>
      <div style={{ 
        color: '#C0C0C0', 
        fontSize: '13px',
        margin: '0'
      }}>
        Last updated {formatDate(workflow.updatedTime)} | Created {formatDate(workflow.createdTime)}
      </div>
    </GlassListCards>
  );
}; 