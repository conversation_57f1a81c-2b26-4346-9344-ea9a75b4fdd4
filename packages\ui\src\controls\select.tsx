"use client";

import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { CONTROL_STYLES } from './common-styles';

interface SelectOption {
  value: string | number;
  label: string;
}

interface SelectProps {
  options: SelectOption[];
  value?: string | number;
  onChange?: (value: string | number) => void;
  placeholder?: string;
  style?: React.CSSProperties;
}

const SelectContainer = styled.div`
  position: relative;
  width: 200px;
`;

const SelectInput = styled.div`
  width: 100%;
  padding: ${CONTROL_STYLES.spacing.sm} ${CONTROL_STYLES.spacing.md};
  border: 1px solid ${CONTROL_STYLES.colors.border};
  border-radius: ${CONTROL_STYLES.borderRadius.default};
  font-size: ${CONTROL_STYLES.fontSize.default};
  color: ${CONTROL_STYLES.colors.text};
  background: ${CONTROL_STYLES.colors.background};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: ${CONTROL_STYLES.transitions.normal};
  
  &:hover {
    border-color: ${CONTROL_STYLES.colors.borderHover};
  }

  &:focus {
    border-color: ${CONTROL_STYLES.colors.borderFocus};
    box-shadow: ${CONTROL_STYLES.shadows.focus};
    outline: none;
  }

  .placeholder {
    color: ${CONTROL_STYLES.colors.placeholder};
    font-size: ${CONTROL_STYLES.fontSize.default};
  }
`;

const DropdownContainer = styled.div<{ $isOpen: boolean }>`
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: ${CONTROL_STYLES.colors.background};
  border: 1px solid ${CONTROL_STYLES.colors.border};
  border-radius: ${CONTROL_STYLES.borderRadius.default};
  margin-top: 2px;
  max-height: 200px;
  overflow-y: auto;
  display: ${props => props.$isOpen ? 'block' : 'none'};
  z-index: 1000;
  box-shadow: ${CONTROL_STYLES.shadows.dropdown};
`;

const SearchInput = styled.input`
  width: 100%;
  padding: ${CONTROL_STYLES.spacing.sm};
  border: none;
  border-bottom: 1px solid ${CONTROL_STYLES.colors.border};
  outline: none;
  font-size: ${CONTROL_STYLES.fontSize.default};
  color: ${CONTROL_STYLES.colors.text};

  &::placeholder {
    color: ${CONTROL_STYLES.colors.placeholder};
    font-size: ${CONTROL_STYLES.fontSize.default};
  }
`;

const Option = styled.div`
  padding: ${CONTROL_STYLES.spacing.sm} ${CONTROL_STYLES.spacing.md};
  cursor: pointer;
  font-size: ${CONTROL_STYLES.fontSize.default};
  color: ${CONTROL_STYLES.colors.text};
  transition: ${CONTROL_STYLES.transitions.fast};
  
  &:hover {
    background-color: #f0f0f0;
  }
`;

export const Select: React.FC<SelectProps> = ({
  options,
  value,
  onChange,
  placeholder = '请选择',
  style
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchText, setSearchText] = useState('');
  const containerRef = useRef<HTMLDivElement>(null);
  
  // 当前选中的选项
  const selectedOption = options.find(opt => opt.value === value);
  
  // 过滤后的选项
  const filteredOptions = options.filter(opt => 
    opt.label.toLowerCase().includes(searchText.toLowerCase())
  );

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSelect = (option: SelectOption) => {
    onChange?.(option.value);
    setIsOpen(false);
    setSearchText('');
  };

  return (
    <SelectContainer ref={containerRef} style={style}>
      <SelectInput onClick={() => setIsOpen(!isOpen)} tabIndex={0}>
        {selectedOption ? (
          <span>{selectedOption.label}</span>
        ) : (
          <span className="placeholder">{placeholder}</span>
        )}
        <span>{isOpen ? '▲' : '▼'}</span>
      </SelectInput>
      
      <DropdownContainer $isOpen={isOpen}>
        <SearchInput
          value={searchText}
          onChange={e => setSearchText(e.target.value)}
          placeholder="搜索..."
          onClick={e => e.stopPropagation()}
        />
        {filteredOptions.map(option => (
          <Option
            key={option.value}
            onClick={() => handleSelect(option)}
          >
            {option.label}
          </Option>
        ))}
      </DropdownContainer>
    </SelectContainer>
  );
};
