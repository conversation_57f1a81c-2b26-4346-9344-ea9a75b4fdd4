import { NodeRegistry } from './NodeRegistry';
import { INode } from './NodeInterfaces';

/**
 * 抽象节点加载器基类
 * 定义节点加载的通用接口，可以被不同的具体实现继承
 */
export abstract class AbstractNodeLoader {
  protected nodeRegistry: NodeRegistry;

  constructor(nodeRegistry: NodeRegistry) {
    this.nodeRegistry = nodeRegistry;
  }

  /**
   * 加载所有节点的抽象方法
   * 具体实现由子类提供
   */
  abstract loadNodes(): Promise<void>;

  /**
   * 注册单个节点到注册表
   * @param nodeInstance 节点实例
   */
  protected registerNode(nodeInstance: INode): void {
    this.nodeRegistry.registerNode(nodeInstance);
  }

  /**
   * 批量注册节点
   * @param nodes 节点实例数组
   */
  protected registerNodes(nodes: INode[]): void {
    nodes.forEach(node => this.registerNode(node));
  }

  /**
   * 获取节点注册表实例
   */
  getNodeRegistry(): NodeRegistry {
    return this.nodeRegistry;
  }
}

/**
 * 节点加载器工厂接口
 */
export interface INodeLoaderFactory {
  createLoader(): AbstractNodeLoader;
}

/**
 * 节点加载器配置接口
 */
export interface INodeLoaderConfig {
  nodesPaths: string[];
  filePattern?: string;
  ignorePatterns?: string[];
} 