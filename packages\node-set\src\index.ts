import 'reflect-metadata';
export * from './container/NodeLoader';
export * from './categories';

import { NodeLoader } from './container/NodeLoader';

// 提供一个便捷的方法来初始化和获取所有节点
export async function initializeNodes() {
  console.log('Creating NodeLoader instance...');
  const loader = new NodeLoader();
  console.log('NodeLoader instance created');
  await loader.loadNodes();
  return loader.getNodeRegistry();
}