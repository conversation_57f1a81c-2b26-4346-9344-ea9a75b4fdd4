// Type definitions for Node components

import { Node } from 'reactflow';
import { TestResult, NodeTestResults, NodeParameter } from '@repo/common';

// Re-export for backward compatibility
export type { TestResult, NodeTestResults, NodeParameter };

// Toast notification McpInterfaces.ts
export type NodeToastType = 'error' | 'warning';
export type ShowToastFn = (type: NodeToastType, title: string, message: string) => void;

// Core interfaces
export interface NodeSettingsProps {
  node: Node;
  parameters: NodeParameter[];
  savedValues?: Record<string, any>;
  onClose: () => void;
  onSave: (nodeData: Node) => void;
  onNodeIdChange?: (oldId: string, newId: string) => void;
  previousNodeOutput?: string;
  onTest?: (nodeValues: Record<string, any>) => void;
  onTestPreviousNode?: (nodeValues: Record<string, any>, targetNodeId: string) => void;
  onSaveMockData?: (mockTestResult: any) => void;
  testOutput?: string;
  nodeWidth?: number;
  lastTestResult?: TestResult;
  testHistory?: TestResult[];
  previousNodeIds?: string[];
  onPreviousNodeChange?: (nodeId: string) => void;
  selectedPreviousNodeId?: string;
  nodesTestResultsMap?: Record<string, any>;
  getLatestNodesTestResultsMap?: () => Record<string, any>;
  nodesDetailsMap?: Record<string, any>;
  showToast?: ShowToastFn;
}

export interface LeftPanelProps {
  width: number;
  previousNodeOutput?: string;
  nodeId?: string;
  previousNodeIds?: string[];
  onPreviousNodeChange?: (nodeId: string) => void;
  selectedPreviousNodeId?: string;
  onTest?: (nodeValues: Record<string, any>, nodeId: string) => void;
  nodesTestResultsMap?: Record<string, any>;
  getLatestNodesTestResultsMap?: () => Record<string, any>;
  onDisplayTestResult?: (testResult: TestResult | null) => void;
  nodesDetailsMap?: Record<string, any>;
  showToast?: ShowToastFn;
  showSettings?: boolean;
  overlayLeftOffset?: number;
  nodeWidth?: number;
  containerRef?: React.RefObject<HTMLDivElement>;
}

export interface RightPanelProps {
  width: number;
  onTest?: (nodeValues: Record<string, any>) => void;
  testOutput?: string;
  nodeWidth?: number;
  showSettings?: boolean;
  onMockDataChange?: (mockData: any) => void;
  onSaveMockData?: (mockData: any) => void;
  lastTestResult?: TestResult;
  testHistory?: TestResult[];
  overlayLeftOffset?: number;
  containerRef?: React.RefObject<HTMLDivElement>;
}

export interface NodeDetailsViewProps extends NodeSettingsProps {
  // Extends NodeSettingsProps, no additional properties needed
}

// Hook return McpInterfaces.ts
export interface UseTestResultPollingReturn {
  localTestResult: TestResult | null;
  isExecuting: boolean;
  executeNode: (nodeId: string) => Promise<void>;
  setLocalTestResult: (result: TestResult | null) => void;
}

// Internal state McpInterfaces.ts
export interface NodeFormState {
  activeTab: 'parameters' | 'settings';
  nodeId: string;
  nodeIcon: string;
  showSettings: boolean;
  leftWidth: number;
  isDragging: boolean;
  overlayLeftOffset: number;
  isExpanded: boolean;
  displayedTestResult: TestResult | null;
  displayedTestHistory: TestResult[];
  isEditingTitle: boolean;
  tempNodeId: string;
}

// Utility McpInterfaces.ts
export type NodeValues = Record<string, any>;
export type NodesDetailsMap = Record<string, any>;
export type NodesTestResultsMap = Record<string, any>;

// Event handler McpInterfaces.ts
export type ValueChangeHandler = (name: string, value: any) => void;
export type NodeIdChangeHandler = (newId: string) => void;
export type MouseEventHandler = (e: React.MouseEvent) => void;
export type KeyboardEventHandler = (e: React.KeyboardEvent<HTMLInputElement>) => void;
export type ChangeEventHandler = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;