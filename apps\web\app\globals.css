:root {
  --background: #ffffff;
  --foreground: #171717;
  --font-geist-sans: "PingFang SC", "Microsoft YaHei", "Hiragino Sans GB", 
    "WenQuanYi Micro Hei", "Noto Sans CJK SC", "Source Han Sans CN",
    -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  --font-geist-mono: "PingFang SC", "Microsoft YaHei", "Hiragino Sans GB", 
    "WenQuanYi Micro Hei", "Noto Sans CJK SC", "Source Han Sans CN",
    Consolas, Monaco, "Courier New", monospace;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --font-geist-sans: "PingFang SC", "Microsoft YaHei", "Hiragino Sans GB", 
      "WenQuanYi Micro Hei", "Noto Sans CJK SC", "Source Han Sans CN",
      -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    --font-geist-mono: "PingFang SC", "Microsoft YaHei", "Hiragino Sans GB", 
      "WenQuanYi Micro Hei", "Noto Sans CJK SC", "Source Han Sans CN",
      Consolas, Monaco, "Courier New", monospace;
  }
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 
    "PingFang SC", "Microsoft YaHei", "Hiragino Sans GB", 
    "WenQuanYi Micro Hei", "Noto Sans CJK SC", "Source Han Sans CN",
    -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif !important;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  font-family: inherit;
}

html {
  font-family: 
    "PingFang SC", "Microsoft YaHei", "Hiragino Sans GB", 
    "WenQuanYi Micro Hei", "Noto Sans CJK SC", "Source Han Sans CN",
    -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif !important;
}

a {
  color: inherit;
  text-decoration: none;
}

.imgDark {
  display: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }

  .imgLight {
    display: none;
  }
  .imgDark {
    display: unset;
  }
}
