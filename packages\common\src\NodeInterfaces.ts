export type CodeAutocompleteTypes = 'function' | 'functionItem';

export type GenericValue = string | object | number | boolean | undefined | null;

export type EditorType = 'codeNodeEditor' | 'jsEditor' | 'htmlEditor' | 'sqlEditor' | 'cssEditor';

export const LOG_LEVELS = ['silent', 'error', 'warn', 'info', 'debug'] as const;

export type Themed<T> = T | { light: T; dark: T };
export type IconName = `${string}.png` | `${string}.svg`;
export type Icon = Themed<IconName>;


export type LogLevel = (typeof LOG_LEVELS)[number];
export type LogMetadata = {
    [key: string]: unknown;
    scopes?: string;//LogScope[];
    file?: string;
    function?: string;
};

export type Logger = Record<
    Exclude<LogLevel, 'silent'>,
    (message: string, metadata?: LogMetadata) => void
>;
export type LogLocationMetadata = Pick<LogMetadata, 'file' | 'function'>;

export interface IDataObject {
    [key: string]: GenericValue | IDataObject | GenericValue[] | IDataObject[];
}

export interface ResourceMapperTypeOptionsBase {
    mode: 'add' | 'update' | 'upsert' | 'map';
    valuesLabel?: string;
    fieldWords?: {
        singular: string;
        plural: string;
    };
    addAllFields?: boolean;
    noFieldsError?: string;
    multiKeyMatch?: boolean;
    supportAutoMap?: boolean;
    matchingFieldsLabels?: {
        title?: string;
        description?: string;
        hint?: string;
    };
    showTypeConversionOptions?: boolean;
}

// Enforce at least one of resourceMapperMethod or localResourceMapperMethod
export type ResourceMapperTypeOptionsLocal = {
    resourceMapperMethod: string;
    localResourceMapperMethod?: never; // Explicitly disallows this property
};

export type ResourceMapperTypeOptionsExternal = {
    localResourceMapperMethod: string;
    resourceMapperMethod?: never; // Explicitly disallows this property
};

export interface IRequestOptionsSimplifiedAuth {
    auth?: {
        username: string;
        password: string;
        sendImmediately?: boolean;
    };
    body?: IDataObject;
    headers?: IDataObject;
    qs?: IDataObject;
    url?: string;
    skipSslCertificateValidation?: boolean | string;
}

export interface IN8nRequestOperationPaginationBase {
    type: string;
    properties: {
        [key: string]: unknown;
    };
}

export interface IN8nRequestOperationPaginationGeneric extends IN8nRequestOperationPaginationBase {
    type: 'generic';
    properties: {
        continue: boolean | string;
        request: IRequestOptionsSimplifiedAuth;
    };
}

export interface IN8nRequestOperationPaginationOffset extends IN8nRequestOperationPaginationBase {
    type: 'offset';
    properties: {
        limitParameter: string;
        offsetParameter: string;
        pageSize: number;
        rootProperty?: string; // Optional Path to option array
        type: 'body' | 'query';
    };
}

// The encrypted credentials which the nodes can access
export type CredentialInformation =
    | string
    | string[]
    | number
    | boolean
    | IDataObject
    | IDataObject[];

export type WorkflowExecuteMode =
    | 'cli'
    | 'error'
    | 'integrated'
    | 'internal'
    | 'manual'
    | 'retry'
    | 'trigger'
    | 'webhook'
    | 'evaluation';

export type WorkflowActivateMode =
    | 'init'
    | 'create' // unused
    | 'update'
    | 'activate'
    | 'manual' // unused
    | 'leadershipChange';


export interface ICredentialDataDecryptedObject {
    [key: string]: CredentialInformation;
}

export interface IWorkflowMetadata {
    id?: string;
    name?: string;
    active: boolean;
}

export type BinaryFileType = 'text' | 'json' | 'image' | 'audio' | 'video' | 'pdf' | 'html';
export interface IBinaryData {
    [key: string]: string | number | undefined;
    data: string;
    mimeType: string;
    fileType?: BinaryFileType;
    fileName?: string;
    directory?: string;
    fileExtension?: string;
    fileSize?: string; // TODO: change this to number and store the actual value
    id?: string;
}

export interface ISourceData {
    previousNode: string;
    previousNodeOutput?: number; // If undefined "0" gets used
    previousNodeRun?: number; // If undefined "0" gets used
}

export type NodeTypeAndVersion = {
    name: string;
    type: string;
    typeVersion: number;
    disabled: boolean;
    parameters?: INodeParameters;
};

export interface IPinData {
    [nodeName: string]: INodeExecutionData[];
}

export interface INodes {
    [key: string]: INode;
}

export interface IObservableObject {
    [key: string]: any;
    __dataChanged: boolean;
}

export interface IBinaryKeyData {
    [key: string]: IBinaryData;
}

export interface IPairedItemData {
    item: number;
    input?: number; // If undefined "0" gets used
    sourceOverwrite?: ISourceData;
}

export interface RelatedExecution {
    executionId: string;
    workflowId: string;
}


export interface INodeExecutionData {
    [key: string]:
    | IDataObject
    | IBinaryKeyData
    | IPairedItemData
    | IPairedItemData[]
    //| NodeApiError
    //| NodeOperationError
    | number
    | undefined;
    json: IDataObject;
    binary?: IBinaryKeyData;
    //error?: NodeApiError | NodeOperationError;
    pairedItem?: IPairedItemData | IPairedItemData[] | number;
    metadata?: {
        subExecution: RelatedExecution;
    };
}

export interface FunctionsBase {
    logger: Logger;
    getCredentials<T extends object = ICredentialDataDecryptedObject>(
        type: string,
        itemIndex?: number,
    ): Promise<T>;
    getCredentialsProperties(type: string): INodeFields[];
    getExecutionId(): string;
    getNode(): INode;
    getWorkflow(): IWorkflowMetadata;
    getWorkflowStaticData(type: string): IDataObject;
    getTimezone(): string;
    getRestApiUrl(): string;
    getInstanceBaseUrl(): string;
    getInstanceId(): string;
    getChildNodes(
        nodeName: string,
        options?: { includeNodeParameters?: boolean },
    ): NodeTypeAndVersion[];
    getParentNodes(nodeName: string): NodeTypeAndVersion[];
    getKnownNodeTypes(): IDataObject;
    getMode?: () => WorkflowExecuteMode;
    getActivationMode?: () => WorkflowActivateMode;

    /** @deprecated */
    prepareOutputData(outputData: INodeExecutionData[]): Promise<INodeExecutionData[][]>;
}



type FunctionsBaseWithRequiredKeys<Keys extends keyof FunctionsBase> = FunctionsBase & {
    [K in Keys]: NonNullable<FunctionsBase[K]>;
};

export interface ITaskSubRunMetadata {
    node: string;
    runIndex: number;
}


export interface ITaskMetadata {
    subRun?: ITaskSubRunMetadata[];
    parentExecution?: RelatedExecution;
    subExecution?: RelatedExecution;
    subExecutionsCount?: number;
}

export type ContextType = 'flow' | 'node';
export type IContextObject = {
    [key: string]: any;
};

export interface ITaskDataConnections {
    // Key for each input type and because there can be multiple inputs of the same type it is an array
    // null is also allowed because if we still need data for a later while executing the workflow set temporary to null
    // the nodes get as input TaskDataConnections which is identical to this one except that no null is allowed.
    [key: string]: Array<INodeExecutionData[] | null>;
}

export interface ITaskDataConnectionsSource {
    // Key for each input type and because there can be multiple inputs of the same type it is an array
    // null is also allowed because if we still need data for a later while executing the workflow set temporary to null
    // the nodes get as input TaskDataConnections which is identical to this one except that no null is allowed.
    [key: string]: Array<ISourceData | null>;
}


export interface IExecuteData {
    data: ITaskDataConnections;
    metadata?: ITaskMetadata;
    node: INode;
    source: ITaskDataConnectionsSource | null;
}

export interface ProxyInput {
    all: () => INodeExecutionData[];
    context: any;
    first: () => INodeExecutionData | undefined;
    item: INodeExecutionData | undefined;
    last: () => INodeExecutionData | undefined;
    params?: INodeParameters;
}

export interface IWorkflowDataProxyData {
    [key: string]: any;
    $binary: INodeExecutionData['binary'];
    $data: any;
    $env: any;
    $evaluateExpression: (expression: string, itemIndex?: number) => NodeParameterValueType;
    $item: (itemIndex: number, runIndex?: number) => IWorkflowDataProxyData;
    $items: (nodeName?: string, outputIndex?: number, runIndex?: number) => INodeExecutionData[];
    $json: INodeExecutionData['json'];
    $node: any;
    $parameter: INodeParameters;
    $position: number;
    $workflow: any;
    $: any;
    $input: ProxyInput;
    $thisItem: any;
    $thisRunIndex: number;
    $thisItemIndex: number;
    $now: any;
    $today: any;
    $getPairedItem: (
        destinationNodeName: string,
        incomingSourceData: ISourceData | null,
        pairedItem: IPairedItemData,
    ) => INodeExecutionData | null;
    constructor: any;
}

export type AiEvent =
    | 'ai-messages-retrieved-from-memory'
    | 'ai-message-added-to-memory'
    | 'ai-output-parsed'
    | 'ai-documents-retrieved'
    | 'ai-document-embedded'
    | 'ai-query-embedded'
    | 'ai-document-processed'
    | 'ai-text-split'
    | 'ai-tool-called'
    | 'ai-vector-store-searched'
    | 'ai-llm-generated-output'
    | 'ai-llm-errored'
    | 'ai-vector-store-populated'
    | 'ai-vector-store-updated';

type AiEventPayload = {
    msg: string;
    workflowName: string;
    executionId: string;
    nodeName: string;
    workflowId?: string;
    nodeType?: string;
};

type BaseExecutionFunctions = FunctionsBaseWithRequiredKeys<'getMode'> & {
    continueOnFail(): boolean;
    setMetadata(metadata: ITaskMetadata): void;
    evaluateExpression(expression: string, itemIndex: number): NodeParameterValueType;
    getContext(type: ContextType): IContextObject;
    getExecuteData(): IExecuteData;
    getWorkflowDataProxy(itemIndex: number): IWorkflowDataProxyData;
    //getInputSourceData(inputIndex?: number, connectionType?: NodeConnectionType): ISourceData;
    getExecutionCancelSignal(): AbortSignal | undefined;
    onExecutionCancellation(handler: () => unknown): void;
    logAiEvent(eventName: AiEvent, msg?: string | undefined): void;
};

export type EnsureTypeOptions = 'string' | 'number' | 'boolean' | 'object' | 'array' | 'json';
export interface IGetNodeParameterOptions {
    contextNode?: INode;
    // make sure that returned value would be of specified type, converts it if needed
    ensureType?: EnsureTypeOptions;
    // extract value from regex, works only when parameter type is resourceLocator
    extractValue?: boolean;
    // get raw value of parameter with unresolved expressions
    rawExpressions?: boolean;
}

export type IHttpRequestMethods = 'DELETE' | 'GET' | 'HEAD' | 'PATCH' | 'POST' | 'PUT';

export interface IHttpRequestOptions {
    url: string;
    baseURL?: string;
    headers?: IDataObject;
    method?: IHttpRequestMethods;
    body?: FormData | GenericValue | GenericValue[] | Buffer | URLSearchParams;
    qs?: IDataObject;
    arrayFormat?: 'indices' | 'brackets' | 'repeat' | 'comma';
    auth?: {
        username: string;
        password: string;
        sendImmediately?: boolean;
    };
    disableFollowRedirect?: boolean;
    encoding?: 'arraybuffer' | 'blob' | 'document' | 'json' | 'text' | 'stream';
    skipSslCertificateValidation?: boolean;
    returnFullResponse?: boolean;
    ignoreHttpStatusErrors?: boolean;
    proxy?: {
        host: string;
        port: number;
        auth?: {
            username: string;
            password: string;
        };
        protocol?: string;
    };
    timeout?: number;
    json?: boolean;
    abortSignal?: string;//GenericAbortSignal;
}

export type SQLDialect =
    | 'StandardSQL'
    | 'PostgreSQL'
    | 'MySQL'
    | 'MariaSQL'
    | 'MSSQL'
    | 'SQLite'
    | 'Cassandra'
    | 'PLSQL';

/**
 * 所有连接点类型集合
 */
export const NodeLink = {
    Data: {desc:'' ,subflow:false},
    Done: {desc:'完成' ,subflow:false},
    Loop: {desc:'循环' ,subflow:true},
    Error: {desc:'错误' ,subflow:false},
    Signal: {desc:'信号' ,subflow:false}
} as const;

export type NodeLinkType =
    (typeof NodeLink)[keyof typeof NodeLink];

export interface INodePropertyOptions {
    name: string;
    value: string | number | boolean;
    action?: string;
    description?: string;
    routing?: INodePropertyRouting;
    //outputConnectionType?: NodeConnectionType;
}

export type NodePropertyTypes =
    | 'string'
    | 'number'
    | 'boolean'
    | 'collection'
    | 'fixedCollection'
    | 'multiOptions'
    | 'options'
    | 'color'
    | 'dateTime'
    | 'json'
    | 'notice'
    | 'credentialsSelect'
    | 'resourceLocator'
    | 'curlImport'
    | 'resourceMapper'
    | 'filter'
    | 'assignmentCollection'
    | 'custom';

export type NodePropertyAction = {
    type: 'askAiCodeGeneration';
    handler?: string;
    target?: string;
};

export type NodeParameterValue = string | number | boolean | undefined | null;

export type ResourceLocatorModes = 'id' | 'url' | 'list' | string;
export interface IResourceLocatorResult {
    name: string;
    value: string;
    url?: string;
}

export interface INodeParameterResourceLocator {
    __rl: true;
    mode: ResourceLocatorModes;
    value: NodeParameterValue;
    cachedResultName?: string;
    cachedResultUrl?: string;
    __regex?: string;
}

export type FilterValue = {
    // options: FilterOptionsValue;
    // conditions: FilterConditionValue[];
    // combinator: FilterTypeCombinator;
};


export type ResourceMapperValue = {
    mappingMode: string;
    value: { [key: string]: string | number | boolean | null } | null;
    matchingColumns: string[];
    //schema: ResourceMapperField[];
    attemptToConvertTypes: boolean;
    convertFieldsToString: boolean;
};

export type AssignmentCollectionValue = {
    //assignments: AssignmentValue[];
};

export type NodeParameterValueType =
    // TODO: Later also has to be possible to add multiple ones with the name name. So array has to be possible
    | NodeParameterValue
    | INodeParameters
    | INodeParameterResourceLocator
    | ResourceMapperValue
    | FilterValue
    | AssignmentCollectionValue
    | NodeParameterValue[]
    | INodeParameters[]
    | INodeParameterResourceLocator[]
    | ResourceMapperValue[];

export type OnError = 'continueErrorOutput' | 'continueRegularOutput' | 'stopWorkflow';
export interface INodeParameters {
    [key: string]: NodeParameterValueType;
}
export interface INodeCredentials {
    [key: string]: INodeCredentialsDetails;
}
export interface INodeCredentialsDetails {
    id: string | null;
    name: string;
}

export interface INode {
    node: INodeBasic;
    detail: INodeDetail;
    execute?(opts: IExecuteOptions): Promise<any>;
    first?(opts: IEnumeratorOptions): Promise<IEnumeratorData>;
    next?(opts: IEnumeratorOptions): Promise<IEnumeratorData>;
}

export interface INodeBasic {
    kind: string;
    name: string;
    categories: CategoryType[];
    version: number;
    event?: string;
    // type: string;
    position?: [number, number];
    description: string;
    icon: Icon;
    nodeWidth?: number;
    executeMode?: ExecuteType;
    link?: {
        inputs?: NodeLinkType[];
        outputs?: NodeLinkType[];
    }
    // inputs: Array<NodeConnectionType | INodeInputConfiguration> | ExpressionString;
    // outputs: Array<NodeConnectionType | INodeOutputConfiguration> | ExpressionString;
    // disabled?: boolean;
    // notes?: string;
    // notesInFlow?: boolean;
    // retryOnFail?: boolean;
    // maxTries?: number;
    // waitBetweenTries?: number;
    // alwaysOutputData?: boolean;
    // executeOnce?: boolean;
    // onError?: OnError;
    // continueOnFail?: boolean;
    // parameters?: INodeParameters;//这个是什么玩意
    // credentials?: INodeCredentials;
    // webhookId?: string;
    // extendsCredential?: string;
}

export type ResourceMapperTypeOptions = ResourceMapperTypeOptionsBase &
    (ResourceMapperTypeOptionsLocal | ResourceMapperTypeOptionsExternal);

export type FilterTypeOptions = {
    version: 1 | 2 | {}; // required so nodes are pinned on a version
    caseSensitive?: boolean | string; // default = true
    leftValue?: string; // when set, user can't edit left side of condition
    //allowedCombinators?: NonEmptyArray<FilterTypeCombinator>; // default = ['and', 'or']
    maxConditions?: number; // default = 10
    typeValidation?: 'strict' | 'loose' | {}; // default = strict, `| {}` is a TypeScript trick to allow custom strings (expressions), but still give autocomplete
};

export type AssignmentTypeOptions = Partial<{
    hideType?: boolean; // visible by default
    //defaultType?: FieldType | 'string';
    disableType?: boolean; // visible by default
}>;


export interface INodePropertyTypeOptions {
    // Supported by: button
    buttonConfig?: {
        action?: string | NodePropertyAction;
        label?: string; // otherwise "displayName" is used
        hasInputField?: boolean;
        inputFieldMaxLength?: number; // Supported if hasInputField is true
    };
    containerClass?: string; // Supported by: notice
    alwaysOpenEditWindow?: boolean; // Supported by: json
    codeAutocomplete?: CodeAutocompleteTypes; // Supported by: string
    editor?: EditorType; // Supported by: string
    editorIsReadOnly?: boolean; // Supported by: string
    sqlDialect?: SQLDialect; // Supported by: sqlEditor
    loadOptionsDependsOn?: string[]; // Supported by: options
    loadOptionsMethod?: string; // Supported by: options
    //loadOptions?: ILoadOptions; // Supported by: options
    maxValue?: number; // Supported by: number
    minValue?: number; // Supported by: number
    multipleValues?: boolean; // Supported by: <All>
    multipleValueButtonText?: string; // Supported when "multipleValues" set to true
    numberPrecision?: number; // Supported by: number
    password?: boolean; // Supported by: string
    rows?: number; // Supported by: string
    showAlpha?: boolean; // Supported by: color
    sortable?: boolean; // Supported when "multipleValues" set to true
    expirable?: boolean; // Supported by: hidden (only in the credentials)
    resourceMapper?: ResourceMapperTypeOptions;
    filter?: FilterTypeOptions;
    assignment?: AssignmentTypeOptions;
    minRequiredFields?: number; // Supported by: fixedCollection
    maxAllowedFields?: number; // Supported by: fixedCollection
    [key: string]: any;
}

// export type NodeParameterValue = string | number | boolean | object | null;

// export interface IDisplayOptions {
//     hide?: {
//         [key: string]: Array<NodeParameterValue | DisplayCondition> | undefined;
//     };
//     show?: {
//         '@version'?: Array<number | DisplayCondition>;
//         '@tool'?: boolean[];
//         [key: string]: Array<NodeParameterValue | DisplayCondition> | undefined;
//     };

//     hideOnCloud?: boolean;
// }

// export interface INodePropertyCollection {
//     displayName: string;
//     name: string;
//     values: INodeProperties[];
// }

// export interface INodePropertyMode {
//     displayName: string;
//     name: string;
//     type: 'string' | 'list';
//     hint?: string;
//     validation?: Array<
//         INodePropertyModeValidation | { (this: IExecuteSingleFunctions, value: string): void }
//     >;
//     placeholder?: string;
//     url?: string;
//     extractValue?: INodePropertyValueExtractor;
//     initType?: string;
//     entryTypes?: {
//         [name: string]: {
//             selectable?: boolean;
//             hidden?: boolean;
//             queryable?: boolean;
//             data?: {
//                 request?: IHttpRequestOptions;
//                 output?: INodeRequestOutput;
//             };
//         };
//     };
//     search?: INodePropertyRouting;
//     typeOptions?: INodePropertyModeTypeOptions;
// }

// export type NodeParameterValue = string | number | boolean | undefined | null;

export interface IDisplayOptions {
    hide?: {
        //[key: string]: Array<NodeParameterValue | DisplayCondition> | undefined;
    };
    show?: {
        // '@version'?: Array<number | DisplayCondition>;
        // '@tool'?: boolean[];
        // [key: string]: Array<NodeParameterValue | DisplayCondition> | undefined;
    };

    hideOnCloud?: boolean;
}

export interface INodePropertyCollection {
    displayName: string;
    name: string;
    values: INodeFields[];
}
export interface INodePropertyRouting {
    // operations?: IN8nRequestOperations; // Should be changed, does not sound right
    // output?: INodeRequestOutput;
    // request?: DeclarativeRestApiSettings.HttpRequestOptions;
    // send?: INodeRequestSend;
}

export type FieldTypeMap = {
    // eslint-disable-next-line id-denylist
    boolean: boolean;
    // eslint-disable-next-line id-denylist
    number: number;
    // eslint-disable-next-line id-denylist
    string: string;
    'string-alphanumeric': string;
    dateTime: string;
    time: string;
    array: unknown[];
    object: object;
    options: any;
    url: string;
    jwt: string;
    // 'form-fields': ""; FormFieldsParameter;
};

export type FieldType = keyof FieldTypeMap;

export interface IExecuteSingleFunctions extends BaseExecutionFunctions {
    //getInputData(inputIndex?: number, connectionType?: NodeConnectionType): INodeExecutionData;
    getItemIndex(): number;
    getNodeParameter(
        parameterName: string,
        fallbackValue?: any,
        options?: IGetNodeParameterOptions,
    ): NodeParameterValueType | object;

    // helpers: RequestHelperFunctions &
    // 	BaseHelperFunctions &
    // 	BinaryHelperFunctions & {
    // 		assertBinaryData(propertyName: string, inputIndex?: number): IBinaryData;
    // 		getBinaryDataBuffer(propertyName: string, inputIndex?: number): Promise<Buffer>;
    // 		detectBinaryEncoding(buffer: Buffer): string;
    // 	};
}

export interface INodeRequestOutput {
    maxResults?: number | string;
    postReceive?: PostReceiveAction[];
}

export type PostReceiveAction =
    | ((
        this: IExecuteSingleFunctions,
        items: INodeExecutionData[],
        //response: IN8nHttpFullResponse,
    ) => Promise<INodeExecutionData[]>)
// | IPostReceiveBinaryData
// | IPostReceiveFilter
// | IPostReceiveLimit
// | IPostReceiveRootProperty
// | IPostReceiveSet
// | IPostReceiveSetKeyValue
// | IPostReceiveSort;



export type NodeGroupType = 'input' | 'output' | 'organization' | 'schedule' | 'transform' | 'trigger';

export type CategoryType = 'trigger' | 'general' | 'AI' | 'process' | 'application';

export type ExecuteType = 'once' | 'each';

export interface IEnumeratorOptions extends IExecuteOptions {
    index?: number;
}

export interface IEnumeratorData {
    current?: number;
    data?: any;
    eof: boolean;
}

export interface ICategory {
    id: CategoryType;           // 分类唯一标识符
    name: string;         // 分类名称
    description: string;  // 分类描述
    icon: string;         // 分类图标（可以是图标名称或URL）
}

export interface INodeTypeBaseDescription {
    displayName: string;
    name: string;
    icon?: Icon;
    group: NodeGroupType[];
    description: string;
    documentationUrl?: string;
    subtitle?: string;
    defaultVersion?: number;
    parameterPane?: 'wide';
}

export interface INodeInputFilter {
    Nodes: string[]; // Allowed nodes
}

export interface INodeInputConfiguration {
    category?: string;
    displayName?: string;
    required?: boolean;
    // type: NodeConnectionType;
    filter?: INodeInputFilter;
    maxConnections?: number;
}

export interface INodeOutputConfiguration {
    category?: 'error';
    displayName?: string;
    maxConnections?: number;
    required?: boolean;
    // type: NodeConnectionType;
}

export type ExpressionString = `=${string}`;

export interface INodeDetail {//extends INodeTypeBaseDescription {
    fields: INodeFields[];
}


export interface INodeFields {
    displayName: string;
    name: string;
    type: NodePropertyTypes;
    typeOptions?: INodePropertyTypeOptions;
    default: NodeParameterValueType;
    description?: string;
    hint?: string;
    disabledOptions?: IDisplayOptions;
    displayOptions?: IDisplayOptions;
    options?: Array<INodePropertyOptions | INodeFields | INodePropertyCollection>;
    placeholder?: string;
    isNodeSetting?: boolean;
    noDataExpression?: boolean;
    required?: boolean;
    routing?: INodePropertyRouting;
    credentialTypes?: Array<
        'extends:oAuth2Api' | 'extends:oAuth1Api' | 'has:authenticate' | 'has:genericAuth'
    >;
    //extractValue?: INodePropertyValueExtractor;
    modes?: INodePropertyMode[];
    requiresDataPath?: 'single' | 'multiple';
    doNotInherit?: boolean;
    // set expected type for the value which would be used for validation and type casting
    validateType?: FieldType;
    controlType?: string;
    // works only if validateType is set
    // allows to skip validation during execution or set custom validation/casting logic inside node
    // inline error messages would still be shown in UI
    ignoreValidationDuringExecution?: boolean;
}

export interface INodePropertyModeValidation {
    type: string;
    properties: {};
}

export interface INodePropertyModeTypeOptions {
    searchListMethod?: string; // Supported by: options
    searchFilterRequired?: boolean;
    searchable?: boolean;
}

/**
 * Base description for node McpInterfaces.ts
 */
// export interface INodeTypeBaseDescription {
//   displayName: string;
//   name: string;
//   group: string[];
//   version: number;
//   description: string;
//   defaults?: {
//     name?: string;
//     color?: string;
//   };
//   inputs: string[];
//   outputs: string[];
//   icon?: string;
//   codable?: boolean;
//   hidden?: boolean;
// }

/**
 * Interface for versioned node McpInterfaces.ts
 */
export interface IVersionedNodeType {
    description: INodeTypeBaseDescription;
    execute(...args: any[]): Promise<any>;
}

export interface INodePropertyMode {
    displayName: string;
    name: string;
    type: 'string' | 'list';
    hint?: string;
    validation?: Array<
        INodePropertyModeValidation | { (this: IExecuteSingleFunctions, value: string): void }
    >;
    placeholder?: string;
    url?: string;
    //extractValue?: INodePropertyValueExtractor;
    initType?: string;
    entryTypes?: {
        [name: string]: {
            selectable?: boolean;
            hidden?: boolean;
            queryable?: boolean;
            data?: {
                request?: IHttpRequestOptions;
                output?: INodeRequestOutput;
            };
        };
    };
    search?: INodePropertyRouting;
    typeOptions?: INodePropertyModeTypeOptions;
}

export interface IExecuteOptions {
    /**
     * The ID of the action within the workflow instance.  This is used as a reference and must
     * be unique within the Instance itself.
     *
     */
    id: string;

    /**
     * The action kind, used to look up the EngineAction definition.
     *
     */
    kind: string;

    name?: string;
    description?: string;

    /**
     * Inputs is a list of configured inputs for the EngineAction.
     *
     * The record key is the key of the EngineAction inoput name, and
     * the value is the variable's value.
     *
     * This will be type checked to match the EngineAction type before
     * save and before execution.
     *
     * Ref inputs for interpolation are "!ref($.<path>)",
     * eg. "!ref($.event.data.email)"
     */
    inputs?: Record<string, any>;
    state?: Map<string, any>;
}