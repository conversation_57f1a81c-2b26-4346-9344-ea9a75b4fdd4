import { IDatabaseConnect, ConnectTestResult } from '@repo/common';

/**
 * PostgreSQL 数据库连接定义
 */
export const PostgreSQLConnect: IDatabaseConnect = {
    overview: {
        id: 'postgresql',
        name: 'PostgreSQL',
        type: 'rd-db' as const,
        provider: 'postgresql' as const,
        icon: 'postgresql.svg',
        description: 'PostgreSQL关系型数据库连接',
        version: '1.0.0',
    },

    detail: {
        defaultPort: 5432,
        supportedFeatures: [
            'transactions' as const,
            'stored_procedures' as const,
            'views' as const,
            'triggers' as const,
            'full_text_search' as const,
            'json_support' as const,
            'array_support' as const
        ],
        fields: [
            {
                displayName: '主机地址',
                name: 'host',
                type: 'string' as const,
                default: 'localhost',
                description: 'PostgreSQL服务器的主机地址',
                placeholder: 'localhost 或 IP地址',
                required: true,
                testConnection: true
            },
            {
                displayName: '端口',
                name: 'port',
                type: 'number' as const,
                default: 5432,
                description: 'PostgreSQL服务器端口号',
                typeOptions: {
                    minValue: 1,
                    maxValue: 65535
                },
                required: true,
                testConnection: true
            },
            {
                displayName: '用户名',
                name: 'username',
                type: 'string' as const,
                default: '',
                description: '数据库用户名',
                required: true,
                testConnection: true
            },
            {
                displayName: '密码',
                name: 'password',
                type: 'string' as const,
                default: '',
                description: '数据库密码',
                typeOptions: {
                    password: true
                },
                isSecure: true,
                testConnection: true
            },
            {
                displayName: '数据库名',
                name: 'database',
                type: 'string' as const,
                default: '',
                description: '要连接的数据库名称',
                required: true,
                testConnection: true
            },
            {
                displayName: 'Schema',
                name: 'schema',
                type: 'string' as const,
                default: 'public',
                description: '默认Schema名称'
            },
            {
                displayName: '启用SSL',
                name: 'ssl',
                type: 'boolean' as const,
                default: false,
                description: '是否启用SSL连接'
            },
            {
                displayName: '连接超时(秒)',
                name: 'connectionTimeout',
                type: 'number' as const,
                default: 10,
                description: '连接超时时间，单位：秒',
                typeOptions: {
                    minValue: 1,
                    maxValue: 300
                }
            },
            {
                displayName: '连接池大小',
                name: 'connectionLimit',
                type: 'number' as const,
                default: 10,
                description: '最大连接数',
                typeOptions: {
                    minValue: 1,
                    maxValue: 100
                }
            }
        ],
        validateConnection: true,
        connectionTimeout: 10
    },

    /**
     * 测试PostgreSQL连接
     */
    async test(config: Record<string, any>): Promise<ConnectTestResult> {
        const startTime = Date.now();
        
        try {
            // 验证必填字段
            const requiredFields = ['host', 'port', 'username', 'database'];
            for (const field of requiredFields) {
                if (!config[field]) {
                    return {
                        success: false,
                        message: `缺少必填字段: ${field}`
                    };
                }
            }

            // 模拟连接测试（实际应该使用 pg 或其他PostgreSQL客户端）
            await new Promise(resolve => setTimeout(resolve, 150));
            
            const latency = Date.now() - startTime;

            return {
                success: true,
                message: 'PostgreSQL连接测试成功',
                latency,
                details: {
                    host: config.host,
                    port: config.port,
                    database: config.database,
                    schema: config.schema || 'public',
                    ssl: config.ssl || false
                }
            };
            
        } catch (error) {
            return {
                success: false,
                message: `连接失败: ${error instanceof Error ? error.message : String(error)}`,
                latency: Date.now() - startTime
            };
        }
    }
};

export default PostgreSQLConnect; 