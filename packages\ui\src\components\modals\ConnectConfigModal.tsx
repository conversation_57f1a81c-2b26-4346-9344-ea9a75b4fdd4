import React, { useState, useEffect } from 'react';
import { IConnectCategory } from '@repo/common';

import {
  <PERSON>dalBackdrop,
  ModalHeader,
  ModalContent,
  ConnectPageModalContent,
  CloseButton,
  TabNav,
  TabButton,
  TabContent,
  PremiumModalContainer,
  PremiumConnectPageModalContainer,
  PremiumTitleDesc
} from '../basic';
import { ConnectDetailsView } from '../forms/connect/ConnectDetailsView';
import { LLMCntDetailsView } from '../forms/connect/LLMCntDetailsView';
import { LuLink2 } from "react-icons/lu";
import {
  ConnectIcon,
  ConnectInfo,
  ConnectName,
  ConnectDescription,
  ConnectGrid,
  ConnectCard,
  CategorySection,
  CategoryTitle,
  ConnectCategoriesContainer,
  ErrorState,
  LoadingState
} from './ConnectConfigStyles';


// interface Category {
//   id: string;
//   name: string;
//   description: string;
//   type: string;
// }

// 通用连接接口，适配从API返回的数据结构
interface ConnectBasicInfo {
  id: string;
  name: string;
  type: string;
  provider: string;
  icon: string;
  description: string;
  version: string;
  tags?: string[];
  validateConnection: boolean;
  connectionTimeout?: number;
}

interface ConnectWithOverview {
  overview: ConnectBasicInfo;
}

interface ConnectConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave?: (data: any) => void;
  onTest?: (config: Record<string, any>, message?: string) => Promise<any>;
  onStreamTest?: (config: Record<string, any>, message: string, onChunk: (chunk: string) => void) => Promise<any>;
  editMode?: boolean;
  editData?: any;
  categories?: IConnectCategory[];
  // 数据获取回调，由父组件提供
  onFetchConnects?: () => Promise<ConnectWithOverview[]>;
  onFetchConnectDetails?: (connectId: string) => Promise<any>;
}

export const ConnectConfigModal: React.FC<ConnectConfigModalProps> = ({
  isOpen,
  onClose,
  onSave,
  onTest,
  onStreamTest,
  editMode = false,
  editData,
  categories = [],
  onFetchConnects,
  onFetchConnectDetails
}) => {
  const [activeTab, setActiveTab] = useState<'connects' | 'categories'>('connects');
  const [connects, setConnects] = useState<ConnectWithOverview[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedConnect, setSelectedConnect] = useState<any | null>(null);

  // 获取分类信息的辅助函数
  const getCategoryInfo = (type: string) => {
    const category = categories.find(cat => cat.type === type);
    return {
      name: category?.name || type,
      icon: <LuLink2 /> // 不使用icon 了，用统一的默认图标
    };
  };

  // 获取连接列表
  const fetchConnects = async () => {
    if (!onFetchConnects) {
      setError('未提供连接数据获取方法');
      return;
    }

    setLoading(true);
    setError(null);
    console.log('🔄 开始调用onFetchConnects...');

    try {
      const connectsData = await onFetchConnects();
      console.log('成功获取连接数据:', connectsData);
      setConnects(connectsData || []);
    } catch (err) {
      console.error('获取连接列表失败:', err);
      setError(err instanceof Error ? err.message : '获取连接列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen && !editMode) {
      fetchConnects();
      setSelectedConnect(null); // 重置选中的连接
    }
  }, [isOpen, editMode]);

  // 重置编辑模式状态
  useEffect(() => {
    if (editMode) {
      setSelectedConnect(null); // 重置选中的连接，等待编辑数据加载
    }
  }, [editMode]);

  const handleConnectSelect = async (connect: ConnectWithOverview) => {

    if (!onFetchConnectDetails) {
      setError('未提供连接详情获取方法');
      return;
    }

    console.log('🔄 开始获取连接详情...');
    try {
      // 获取连接详情
      const connectData = await onFetchConnectDetails(connect.overview.id);
      console.log('成功获取连接详情:', connectData);

      // 确保 connect 对象包含所有必需的属性
      const connectWithDefaults = {
        ...connectData,
        fields: connectData.fields || [], // 防止 fields 为undefined
        validateConnection: connectData.validateConnection ?? true,
        connectType: connect.overview.type // 添加连接类型信息
      };
      console.log('🔧 处理后的连接数据:', connectWithDefaults);
      setSelectedConnect(connectWithDefaults);
    } catch (err) {
      console.error('获取连接详情失败:', err);
      setError(err instanceof Error ? err.message : '获取连接详情失败');
    }
  };

  const handleBackToList = () => {
    setSelectedConnect(null);
  };

  const handleConnectSave = async (connectData: any) => {
    if (onSave) {
      await onSave(connectData);
    }
    onClose();
  };

  // 按类型分组连接
  const groupedConnects = connects.reduce((groups, connect) => {
    // 添加安全检查，确保 connect 和 connect.overview 存在
    if (!connect || !connect.overview || !connect.overview.type) {
      console.warn('无效的连接数据:', connect);
      return groups;
    }
    
    const connectType = connect.overview.type;
    if (!groups[connectType]) {
      groups[connectType] = [];
    }
    groups[connectType]!.push(connect);
    return groups;
  }, {} as Record<string, ConnectWithOverview[]>);

  const renderConnectsList = () => (
    <TabContent>
      {error && (
        <ErrorState>
          ⚠️ {error}
        </ErrorState>
      )}

      {loading ? (
        <LoadingState>
          🔄 正在加载连接类型...
        </LoadingState>
      ) : (
        <ConnectCategoriesContainer>
          {Object.entries(groupedConnects).map(([type, typeConnects]) => (
            <CategorySection key={type}>
              <CategoryTitle>
                <span>{getCategoryInfo(type).icon}</span>
                {getCategoryInfo(type).name}
                <span style={{
                  fontSize: '12px',
                  fontWeight: 'normal',
                  color: '#666',
                  marginLeft: '8px'
                }}>
                  ({typeConnects.length})
                </span>
              </CategoryTitle>
              <ConnectGrid>
                {typeConnects.map((connect) => (
                  <ConnectCard
                    key={connect.overview.id}
                    onClick={() => handleConnectSelect(connect)}
                  >
                    <ConnectIcon>
                      {connect.overview.icon ? (
                        <img
                          src={`/connect-icons/${connect.overview.icon}`}
                          alt={connect.overview.name}
                          style={{ width: '32px', height: '32px' }}
                          onError={(e) => {
                            (e.target as HTMLImageElement).style.display = 'none';
                            (e.target as HTMLImageElement).nextElementSibling!.textContent = '🔗';
                          }}
                        />
                      ) : null}
                      <span style={{ display: connect.overview.icon ? 'none' : 'block' }}>🔗</span>
                    </ConnectIcon>
                    <ConnectInfo>
                      <ConnectName>{connect.overview.name}</ConnectName>
                      <ConnectDescription>{connect.overview.description}</ConnectDescription>
                    </ConnectInfo>
                  </ConnectCard>
                ))}
              </ConnectGrid>
            </CategorySection>
          ))}

          {!loading && connects.length === 0 && (
            <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
              暂无可用的连接类型
            </div>
          )}
        </ConnectCategoriesContainer>
      )}
    </TabContent>
  );

  const renderCategories = () => (
    <TabContent>
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <h4>连接分类管理</h4>
        <p>管理和配置连接分类信息</p>
        <div>
          <p>
            • 查看所有连接分类<br />
            • 配置分类属性<br />
          </p>
        </div>
      </div>
    </TabContent>
  );

  // 在编辑模式下获取连接定义数据
  useEffect(() => {
    const fetchConnectForEdit = async () => {
      if (!editMode || !editData || !onFetchConnectDetails || !editData.ctype) return;
      
      setLoading(true);
      setError(null);
      
      try {
        console.log('📝 编辑模式：获取连接定义', editData.ctype);
        const connectDef = await onFetchConnectDetails(editData.ctype);
        console.log('✅ 获取连接定义成功:', connectDef);
        
        // 解析 configinfo 
        let config = {};
        try {
          if (editData.configinfo && typeof editData.configinfo === 'string') {
            config = JSON.parse(editData.configinfo);
          }
        } catch (e) {
          console.warn('解析配置信息失败:', e);
        }
        
        // 构造符合 LLMCntDetailsView 期望的数据结构
        const transformedEditData = {
          ...connectDef,
          // 将用户保存的配置信息合并到连接定义中
          detail: {
            ...connectDef.detail,
            fields: connectDef.detail?.fields || []
          },
          // 编辑相关信息
          editInfo: {
            id: editData.id,
            connectId: editData.ctype,
            name: editData.name,
            config: config
          }
        };
        
        console.log('🔄 转换后的编辑数据:', transformedEditData);
        setSelectedConnect(transformedEditData);
      } catch (error) {
        console.error('❌ 获取编辑连接定义失败:', error);
        setError('获取连接定义失败');
      } finally {
        setLoading(false);
      }
    };
    
    fetchConnectForEdit();
  }, [editMode, editData]);

  // 渲染主要内容
  const renderContent = () => {
    // 如果是编辑模式
    if (editMode && editData) {
      console.log('📝 编辑模式，editData:', editData);
      
      // 如果正在加载连接定义
      if (loading) {
        return (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <div>🔄 正在加载连接定义...</div>
          </div>
        );
      }
      
      // 如果加载失败
      if (error) {
        return (
          <div style={{ textAlign: 'center', padding: '40px', color: 'red' }}>
            <div>❌ {error}</div>
          </div>
        );
      }
      
      // 如果还没有获取到连接定义
      if (!selectedConnect) {
        return (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <div>⏳ 准备编辑数据...</div>
          </div>
        );
      }
      
      // 根据类型选择视图--need to delete
      // if (editData.mtype === 'llm') {
        // return (
        //   <LLMCntDetailsView
        //     connect={selectedConnect}
        //     onClose={onClose}
        //     onSave={handleConnectSave}
        //     onTest={onTest}
        //     editMode={true}
        //     editData={selectedConnect.editInfo}
        //     showBackButton={false}
        //   />
        // );
      // } else {
        return (
          <ConnectDetailsView
            connect={selectedConnect}
            onClose={onClose}
            onSave={handleConnectSave}
            onTest={onTest}
            onStreamTest={onStreamTest}
            editMode={true}
            editData={selectedConnect.editInfo}
            showBackButton={false}
          />
        );
      // }
    }

    // 如果选择了某个连接，显示连接详情
    if (selectedConnect) {
        return (
          <ConnectDetailsView
            connect={selectedConnect}
            onClose={handleBackToList}
            onSave={handleConnectSave}
            onTest={onTest}
            onStreamTest={onStreamTest}
            showBackButton={true}
          />
        );
    }

    // 否则显示连接列表
    return (
      <>
        <TabNav>
          <TabButton
            $active={activeTab === 'connects'}
            onClick={() => setActiveTab('connects')}
          >
            连接列表
          </TabButton>
          <TabButton
            $active={activeTab === 'categories'}
            onClick={() => setActiveTab('categories')}
          >
            分类浏览
          </TabButton>
        </TabNav>

        <TabContent>
          {loading ? (
            <LoadingState>加载中...</LoadingState>
          ) : error ? (
            <ErrorState>{error}</ErrorState>
          ) : (
            activeTab === 'connects' ? renderConnectsList() : renderCategories()
          )}
        </TabContent>
      </>
    );
  };

  if (!isOpen) return null;

  // 判断是否显示连接列表（使用固定高度）还是连接详情（使用自适应高度）
  const isShowingList = !selectedConnect && !editMode;
  const ModalContainer = isShowingList ? PremiumConnectPageModalContainer : PremiumModalContainer;
  const ModalContentComponent = isShowingList ? ConnectPageModalContent : ModalContent;

  return (
    <ModalBackdrop>
      <ModalContainer>
        {isShowingList && (
          <ModalHeader>
            <PremiumTitleDesc>
              <h3>添加新连接</h3>
              <p>选择并配置一个新的连接</p>
            </PremiumTitleDesc>
            <CloseButton onClick={onClose}>×</CloseButton>
          </ModalHeader>
        )}
        <ModalContentComponent>
          {renderContent()}
        </ModalContentComponent>
      </ModalContainer>
    </ModalBackdrop>
  );
}; 
