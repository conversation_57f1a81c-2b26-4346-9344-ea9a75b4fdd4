"use client";

import React, { useState, useCallback, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { JsonTree } from '../components/basic/JsonTree';
import {
  Panel,
  PanelTitle,
  OutputContainer,
  OutputContainerTitle,
  OutputContainerContent,
  NodeEmptyState,
  NodeEmptyStateText
} from './sharedStyles';
import type { LeftPanelProps, TestResult, UseTestResultPollingReturn } from './types';

// Custom hook for optimized test result polling
const useTestResultPolling = (
  selectedNodeId: string,
  nodesTestResultsMap: Record<string, any> | undefined,
  getLatestNodesTestResultsMap: (() => Record<string, any>) | undefined,
  onDisplayTestResult: ((testResult: any) => void) | undefined,
  onTest: ((nodeValues: Record<string, any>, nodeId: string) => void) | undefined,
  nodesDetailsMap: Record<string, any> | undefined,
  showToast: ((type: 'error' | 'warning', title: string, message: string) => void) | undefined
) => {
  const [localTestResult, setLocalTestResult] = useState<any>(null);
  const [isExecuting, setIsExecuting] = useState<boolean>(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const attemptCountRef = useRef(0);
  const maxAttempts = 60; // 增加�?0次尝�?
  const pollInterval = 2000; // 增加�?秒间�?

  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    attemptCountRef.current = 0;
    setIsExecuting(false);
  }, []);

  const startPolling = useCallback((nodeId: string) => {
    attemptCountRef.current = 0;

    intervalRef.current = setInterval(() => {
      attemptCountRef.current += 1;

      const currentMap = getLatestNodesTestResultsMap ? getLatestNodesTestResultsMap() : nodesTestResultsMap;

      if (currentMap?.[nodeId]) {
        const testResult = currentMap[nodeId];
        console.log(`�?[LeftPanel-Polling] 找到节点 ${nodeId} 的测试结�?`, testResult);
        console.log(`🗺�?[LeftPanel-Polling] 当前完整的nodesTestResultsMap:`, currentMap);
        console.log(`🔑 [LeftPanel-Polling] nodesTestResultsMap中的所有keys:`, Object.keys(currentMap || {}));
        setLocalTestResult(testResult);
        onDisplayTestResult?.(testResult);
        stopPolling();
        return;
      }

      if (attemptCountRef.current >= maxAttempts) {
        stopPolling();
      }
    }, pollInterval);
  }, [nodesTestResultsMap, getLatestNodesTestResultsMap, onDisplayTestResult, stopPolling, maxAttempts, pollInterval]);

  const executeNode = useCallback(async (nodeId: string) => {
    console.log('🚀 [LeftPanel] executeNode called with:', {
      nodeId,
      hasNodesDetailsMap: !!nodesDetailsMap,
      nodesDetailsMapKeys: Object.keys(nodesDetailsMap || {}),
      nodeDetailsExists: !!nodesDetailsMap?.[nodeId],
      nodeDetail: nodesDetailsMap?.[nodeId],
      hasSavedValues: !!nodesDetailsMap?.[nodeId]?.savedValues,
      savedValues: nodesDetailsMap?.[nodeId]?.savedValues
    });
   
    if (!nodesDetailsMap?.[nodeId]?.savedValues) {
      console.error('❌ [LeftPanel] Node configuration missing:', {
        nodeId,
        nodeDetail: nodesDetailsMap?.[nodeId],
        reason: !nodesDetailsMap?.[nodeId] ? 'node not in nodesDetailsMap' : 'no savedValues'
      });
      showToast?.('warning', '配置缺失', `"${nodeId}" 的节点没有做任何配置，执行失败`);
      return;
    }

    if (!onTest) {
      console.error('❌ [LeftPanel] onTest function not provided');
      return;
    }

    setIsExecuting(true);
    const nodeValues = nodesDetailsMap[nodeId].savedValues || {};

    console.log('📤 [LeftPanel] Calling onTest with:', {
      nodeId,
      nodeValues,
      nodeValuesKeys: Object.keys(nodeValues)
    });

    try {
      await onTest(nodeValues, nodeId);
      startPolling(nodeId);
    } catch (error) {
      console.error('❌ [LeftPanel] onTest execution failed:', error);
      startPolling(nodeId);
    }
  }, [nodesDetailsMap, showToast, onTest, startPolling]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, [stopPolling]);

  // Update local result when external state changes
  useEffect(() => {
    const currentMap = getLatestNodesTestResultsMap ? getLatestNodesTestResultsMap() : nodesTestResultsMap;

    if (selectedNodeId && currentMap?.[selectedNodeId]) {
      const testResult = currentMap[selectedNodeId];
      setLocalTestResult(testResult);
      onDisplayTestResult?.(testResult);

      // 如果找到结果，停止polling
      if (isExecuting) {
        setIsExecuting(false);
        stopPolling();
      }
    } else if (selectedNodeId) {
      setLocalTestResult(null);
      onDisplayTestResult?.(null);
    }
  }, [selectedNodeId, nodesTestResultsMap, getLatestNodesTestResultsMap, isExecuting, onDisplayTestResult, stopPolling]);

  // 添加一个额外的effect来监听nodesTestResultsMap的变�?
  useEffect(() => {
    if (selectedNodeId && isExecuting) {
      const currentMap = getLatestNodesTestResultsMap ? getLatestNodesTestResultsMap() : nodesTestResultsMap;

          if (currentMap?.[selectedNodeId]) {
      const testResult = currentMap[selectedNodeId];
        setLocalTestResult(testResult);
        onDisplayTestResult?.(testResult);
        setIsExecuting(false);
        stopPolling();
      } else {
        console.log(`�?[LeftPanel-Effect2] 节点 ${selectedNodeId} 还没有测试结果，继续等待...`);
      }
    }
  }, [nodesTestResultsMap, selectedNodeId, isExecuting, getLatestNodesTestResultsMap, onDisplayTestResult, stopPolling]);

  return {
    localTestResult,
    isExecuting,
    executeNode: (nodeId: string) => executeNode(nodeId),
    setLocalTestResult
  };
};

// Left panel specific styles
const PreviousNodeSelector = styled.div`
  margin-bottom: 16px;
`;

const SelectorLabel = styled.div`
  display: inline-block;
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: 13px;
  margin-bottom: 8px;
`;

const SelectDropdown = styled.select`
  border: 1px solid ${({ theme }) => theme.panel.ctlBorder};
  background: ${({ theme }) => theme.panel.nodeBg};
  color: ${({ theme }) => theme.mode === 'dark' ? '#f8fafc' : '#0f172a'};
  width: 40%;
  padding: 2px 8px;
  border-radius: 2px;
  font-size: 12px;
  &:focus {
    outline: none;
  }

  &:disabled {
    background: ${({ theme }) => theme.colors.inputBg}80;
    color: ${({ theme }) => theme.colors.textSecondary};
    cursor: not-allowed;
  }

  // option {
  //   background:${({ theme }) => theme.colors.inputBg};
  //   color: ${({ theme }) => theme.colors.textPrimary};
  // }
`;

// JSON/Schema 按钮容器
const ButtonGroup = styled.div`
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
`;

// JSON/Schema 按钮样式
const DataViewButton = styled.button<{ $active?: boolean }>`
  background: ${({ theme, $active }) =>
    $active ? theme.colors.accent : theme.panel.nodeBg
  };
  color: ${({ theme, $active }) =>
    $active ? 'white' : theme.colors.textPrimary
  };
  border: 1px solid ${({ theme, $active }) =>
    $active ? theme.panel.ctlBorder : theme.panel.ctlBorder
  };
  border-radius: 2px;
  padding: 3px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 50px;
  height:24px;

  &:hover {
    background: ${({ theme, $active }) =>
    $active ? theme.colors.accent : theme.colors.buttonHover
  };
    border-color: ${({ theme }) => theme.colors.accent};
  }

  &:disabled {
    background: ${({ theme }) => theme.colors.inputBg}80;
    color: ${({ theme }) => theme.colors.textSecondary};
    border-color: ${({ theme }) => theme.colors.border};
    cursor: not-allowed;
    opacity: 0.6;
  }
`;

// 旋转动画
const spinAnimation = `
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;

// 添加执行按钮样式
const ExecuteButton = styled.button<{ $disabled: boolean }>`
  ${spinAnimation}
  
  background: ${({ theme }) => theme.colors.tertiary};
  color: ${({ theme, $disabled }) =>
    $disabled ? theme.colors.textSecondary : 'white'
  };
  width: 180px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 2px;
  padding: 8px 16px;
  cursor: ${({ $disabled }) => $disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  &:hover {
    opacity: ${({ $disabled }) => $disabled ? 1 : 0.8};
    transform: ${({ $disabled }) => $disabled ? 'none' : 'translateY(-1px)'};
  }
  
  .loading-icon {
    animation: spin 1s linear infinite;
    font-size: 14px;
  }
`;

// LeftPanelProps is now imported from McpInterfaces.ts.ts

export const LeftPanel: React.FC<LeftPanelProps> = React.memo(({
  width,
  previousNodeOutput,
  nodeId,
  previousNodeIds = [],
  onPreviousNodeChange,
  selectedPreviousNodeId,
  onTest,
  nodeWidth,
  showSettings,
  nodesTestResultsMap,
  getLatestNodesTestResultsMap,
  onDisplayTestResult,
  nodesDetailsMap,
  showToast
}) => {
  const [selectedNodeId, setSelectedNodeId] = useState<string>(selectedPreviousNodeId || '');
  const [viewMode, setViewMode] = useState<'json' | '结构'>('json');

  // Use optimized polling hook
  const { localTestResult, isExecuting, executeNode, setLocalTestResult } = useTestResultPolling(
    selectedNodeId,
    nodesTestResultsMap,
    getLatestNodesTestResultsMap,
    onDisplayTestResult,
    onTest,
    nodesDetailsMap,
    showToast
  );

  // 当有前置节点但没有选中任何节点时，自动选中第一个
  useEffect(() => {
    if (previousNodeIds.length > 0 && !selectedNodeId && previousNodeIds[0]) {
      const firstNodeId = previousNodeIds[0];
      setSelectedNodeId(firstNodeId);
      onPreviousNodeChange?.(firstNodeId);
    }
  }, [previousNodeIds, selectedNodeId, onPreviousNodeChange]);

  const handleNodeChange = useCallback((event: React.ChangeEvent<HTMLSelectElement>) => {
    const newNodeId = event.target.value;
    setSelectedNodeId(newNodeId);
    onPreviousNodeChange?.(newNodeId);

    // 清除本地测试结果，重新检查并显示测试结果
    setLocalTestResult(null);
    if (newNodeId && nodesTestResultsMap?.[newNodeId]) {
      onDisplayTestResult?.(nodesTestResultsMap[newNodeId]);
    } else {
      onDisplayTestResult?.(null);
    }
  }, [onPreviousNodeChange, setLocalTestResult, nodesTestResultsMap, onDisplayTestResult]);

  const handleExecute = useCallback(() => {
    if (previousNodeIds.length > 0 && selectedNodeId) {
      executeNode(selectedNodeId);
    }
  }, [previousNodeIds, selectedNodeId, executeNode]);



  // 组件挂载时检查默认选中的节点
  useEffect(() => {
    if (selectedNodeId && nodesTestResultsMap?.[selectedNodeId]) {
      const testResult = nodesTestResultsMap[selectedNodeId];
      setLocalTestResult(testResult);
    } else {
      setLocalTestResult(null);
    }
  }, []); // Empty dependency array for mount-only effect

  // 生成 JSON 数据和 Schema
  const generateSchema = useCallback((data: any): any => {
    if (data === null || data === undefined) {
      return { type: 'null' };
    }

    if (typeof data === 'string') {
      return { type: 'string', example: data };
    }

    if (typeof data === 'number') {
      return { type: 'number', example: data };
    }

    if (typeof data === 'boolean') {
      return { type: 'boolean', example: data };
    }

    if (Array.isArray(data)) {
      const schema: any = { type: 'array' };
      if (data.length > 0) {
        schema.items = generateSchema(data[0]);
        schema.example = [data[0]];
      }
      return schema;
    }

    if (typeof data === 'object') {
      const schema: any = { type: 'object', properties: {} };
      const example: any = {};

      for (const [key, value] of Object.entries(data)) {
        schema.properties[key] = generateSchema(value);
        example[key] = value;
      }

      schema.example = example;
      return schema;
    }

    return { type: 'unknown', example: data };
  }, []);

  // 切换视图模式
  const handleViewModeChange = useCallback((mode: 'json' | '结构') => {
    setViewMode(mode);
  }, []);

  return (
    <Panel $width={width}>
      <PanelTitle>前置节点输入</PanelTitle>
      <OutputContainer style={{ marginRight: showSettings ? `${(nodeWidth || 500) / 2 - 20}px` : '0px' }}>
        <OutputContainerTitle>
          {/* 前置节点选择下拉框 - 只在有前置节点时显示 */}
          {previousNodeIds.length > 0 && (
            <PreviousNodeSelector>
              <SelectorLabel>前置节点 : </SelectorLabel>
              <SelectDropdown
                value={selectedNodeId}
                onChange={handleNodeChange}
                disabled={previousNodeIds.length === 0}
              >
                {previousNodeIds.map(nodeId => (
                  <option key={nodeId} value={nodeId}>
                    {nodeId}
                  </option>
                ))}
              </SelectDropdown>

              {/* JSON 和 Schema 按钮 */}
              {selectedNodeId && localTestResult && (
                <ButtonGroup>
                  <DataViewButton
                    $active={viewMode === 'json'}
                    onClick={() => handleViewModeChange('json')}
                  >
                    JSON
                  </DataViewButton>
                  <DataViewButton
                    $active={viewMode === '结构'}
                    onClick={() => handleViewModeChange('结构')}
                  >
                    结构
                  </DataViewButton>
                </ButtonGroup>
              )}
            </PreviousNodeSelector>
          )}
        </OutputContainerTitle>

        <OutputContainerContent>
          {previousNodeIds.length === 0 ? (
            <NodeEmptyState>
              <NodeEmptyStateText>
                当前节点无前置节点
              </NodeEmptyStateText>
            </NodeEmptyState>
          ) : previousNodeOutput ? (
            previousNodeOutput
          ) : selectedNodeId ? (
            // 选中了前置节点
            localTestResult ? (
              // 有本地测试结果，根据视图模式显示不同数据
              <JsonTree
                data={viewMode === '结构' ? generateSchema(localTestResult) : localTestResult}
                nodeId={selectedNodeId}
              />
            ) : (
              // 没有测试结果，显示执行按钮
              <NodeEmptyState>
                <NodeEmptyStateText>
                  节点 "{selectedNodeId}" 无测试结果
                </NodeEmptyStateText>
                <ExecuteButton $disabled={isExecuting} onClick={handleExecute}>
                  {isExecuting ? (
                    <>
                      <span className="loading-icon">⏳</span>
                      执行中...
                    </>
                  ) : (
                    '执行前置节点'
                  )}
                </ExecuteButton>
              </NodeEmptyState>
            )
          ) : (
            // 未选中前置节点的默认状态
            <NodeEmptyState>
              <NodeEmptyStateText>
                请选择前置节点
              </NodeEmptyStateText>
            </NodeEmptyState>
          )}
        </OutputContainerContent>
      </OutputContainer>
    </Panel>
  );
});