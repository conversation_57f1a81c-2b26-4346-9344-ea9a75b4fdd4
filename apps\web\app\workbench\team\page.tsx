"use client";

import React, { useState, useEffect } from 'react';
import { TeamPage } from '@repo/ui/main';
import { TbSteam } from "react-icons/tb";


// Mock 数据
const mockTeamMembers = [
  {
    id: '1',
    name: '<PERSON>',
    role: '',
    avatar: '👩‍💼',
    status: 'online' as const,
    lastSeen: ''
  },
  {
    id: '2',
    name: '<PERSON>',
    role: '',
    avatar: '👨‍💻',
    status: 'online' as const,
    lastSeen: ''
  },
  {
    id: '3',
    name: '<PERSON>',
    role: '',
    avatar: '👩‍🎨',
    status: 'online' as const,
    lastSeen: ''
  },
  {
    id: '4',
    name: '<PERSON>',
    role: '',
    avatar: '👨‍💼',
    status: 'online' as const,
    lastSeen: ''
  }
];

const mockMessages = [
  {
    id: '1',
    sender: '<PERSON>',
    content: '大家好，今天的项目进展如何？',
    timestamp: '10:30',
    avatar: '👩‍💼'
  },
  {
    id: '2',
    sender: '<PERSON>',
    content: '我这边的后端API开发已经完成80%了',
    timestamp: '10:32',
    avatar: '👨‍💻'
  },
  {
    id: '3',
    sender: '<PERSON>',
    content: '界面设计稿已经更新，请大家查看',
    timestamp: '10:35',
    avatar: '👩‍🎨'
  }
];

export default function TeamPageContainer() {
  const [loading, setLoading] = useState(false);
  const [messages, setMessages] = useState(mockMessages);

  const handleSendMessage = (message: string) => {
    const newMessage = {
      id: Date.now().toString(),
      sender: '我',
      content: message,
      timestamp: new Date().toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      }),
      avatar: '👤'
    };
    setMessages(prev => [...prev, newMessage]);
  };

  return (
    <TeamPage
      title="团队协作"
      slogan="与团队成员实时协作，提高工作效率"
      DocumentIcon={TbSteam}
      loading={loading}
      teamMembers={mockTeamMembers}
      messages={messages}
      onSendMessage={handleSendMessage}
    />
  );
} 