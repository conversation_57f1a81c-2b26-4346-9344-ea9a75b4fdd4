import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@repo/db';

/**
 * 获取单个智能体
 * GET /api/agents/[id]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('🤖 GET /api/agents/[id] 开始, ID:', params.id);
    
    const agent = await prisma.aiAgent.findUnique({
      where: { id: params.id },
      include: {
        connectConfig: true // 包含连接配置信息
      }
    });

    if (!agent) {
      return NextResponse.json({
        success: false,
        error: '智能体不存在'
      }, { status: 404 });
    }

    // 获取 MCP 关联
    const mcpRelations = await prisma.agentMcp.findMany({
      where: { agentId: agent.id },
      select: { mcpId: true }
    });

    return NextResponse.json({
      success: true,
      data: {
        id: agent.id,
        name: agent.name,
        description: agent.description,
        prompt: agent.prompt,
        avatar: agent.avatar,
        modelId: agent.modelId || '',
        modelName: agent.modelName,
        connectid: agent.connectid,
        connectConfig: agent.connectConfig, // 添加连接配置信息
        toolmode: agent.toolmode || 'function',
        mcpIds: mcpRelations.map(rel => rel.mcpId),
        createUser: agent.createUser
      }
    });

  } catch (error) {
    console.error('🤖 GET /api/agents/[id] 异常:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取智能体失败'
    }, { status: 500 });
  }
}

/**
 * 更新智能体
 * PUT /api/agents/[id]
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('🤖 PUT /api/agents/[id] 开始, ID:', params.id);
    
    const body = await request.json();
    console.log('🤖 接收到的更新数据:', body);
    
    // 检查智能体是否存在
    const existingAgent = await prisma.aiAgent.findUnique({
      where: { id: params.id }
    });

    if (!existingAgent) {
      return NextResponse.json({
        success: false,
        error: '智能体不存在'
      }, { status: 404 });
    }

    // 准备更新数据
    const updateData: any = {};
    
    if (body.name) {
      updateData.name = body.name;
    }
    
    if (body.description) {
      updateData.description = body.description;
    }
    
    if (body.prompt !== undefined) {
      updateData.prompt = body.prompt;
    }
    
    if (body.avatar !== undefined) {
      updateData.avatar = body.avatar;
    }
    
    if (body.modelId) {
      updateData.modelId = body.modelId;
    }
    
    if (body.modelName !== undefined) {
      updateData.modelName = body.modelName;
    }
    
    if (body.connectId) {
      updateData.connectid = body.connectId;
    }
    
    if (body.toolmode !== undefined) {
      updateData.toolmode = body.toolmode;
    }

    // 使用事务更新
    const result = await prisma.$transaction(async (tx) => {
      // 更新智能体基本信息
      const updatedAgent = await tx.aiAgent.update({
        where: { id: params.id },
        data: updateData,
        include: {
          connectConfig: true // 包含连接配置信息
        }
      });

      // 如果提供了 mcpIds，更新 MCP 关联
      if (body.mcpIds !== undefined) {
        // 删除原有关联
        await tx.agentMcp.deleteMany({
          where: { agentId: params.id }
        });

        // 创建新关联
        if (body.mcpIds.length > 0) {
          await tx.agentMcp.createMany({
            data: body.mcpIds.map((mcpId: string) => ({
              agentId: params.id,
              mcpId: mcpId
            }))
          });
        }
      }

      return updatedAgent;
    });

    // 获取更新后的 MCP 关联
    const mcpRelations = await prisma.agentMcp.findMany({
      where: { agentId: params.id },
      select: { mcpId: true }
    });

    return NextResponse.json({
      success: true,
      data: {
        id: result.id,
        name: result.name,
        description: result.description,
        prompt: result.prompt,
        avatar: result.avatar,
        modelId: result.modelId || '',
        modelName: result.modelName,
        connectid: result.connectid,
        connectConfig: result.connectConfig, // 添加连接配置信息
        toolmode: result.toolmode || 'function',
        mcpIds: mcpRelations.map(rel => rel.mcpId),
        createUser: result.createUser
      },
      message: '智能体更新成功'
    });

  } catch (error) {
    console.error('🤖 PUT /api/agents/[id] 异常:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '更新智能体失败'
    }, { status: 500 });
  }
}

/**
 * 删除智能体
 * DELETE /api/agents/[id]
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('🤖 DELETE /api/agents/[id] 开始, ID:', params.id);
    
    // 检查智能体是否存在
    const existingAgent = await prisma.aiAgent.findUnique({
      where: { id: params.id }
    });

    if (!existingAgent) {
      return NextResponse.json({
        success: false,
        error: '智能体不存在'
      }, { status: 404 });
    }

    // 使用事务删除
    await prisma.$transaction(async (tx) => {
      // 删除 MCP 关联
      await tx.agentMcp.deleteMany({
        where: { agentId: params.id }
      });

      // 删除智能体
      await tx.aiAgent.delete({
        where: { id: params.id }
      });
    });

    return NextResponse.json({
      success: true,
      message: '智能体删除成功'
    });

  } catch (error) {
    console.error('🤖 DELETE /api/agents/[id] 异常:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '删除智能体失败'
    }, { status: 500 });
  }
}