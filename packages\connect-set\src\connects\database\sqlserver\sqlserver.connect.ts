import { IDatabaseConnect, ConnectTestResult } from '@repo/common';

/**
 * SQL Server 数据库连接定义
 */
export const SQLServerConnect: IDatabaseConnect = {
    overview: {
        id: 'sqlserver',
        name: 'SQL Server',
        type: 'rd-db' as const,
        provider: 'sqlserver' as const,
        icon: 'sqlserver.svg',
        description: 'Microsoft SQL Server关系型数据库连接',
        version: '1.0.0',
    },

    detail: {
        defaultPort: 1433,
        supportedFeatures: [
            'transactions' as const,
            'stored_procedures' as const,
            'views' as const,
            'triggers' as const,
            'full_text_search' as const,
            'json_support' as const
        ],
        fields: [
            {
                displayName: '服务器地址',
                name: 'host',
                type: 'string' as const,
                default: 'localhost',
                description: 'SQL Server服务器的主机地址',
                placeholder: 'localhost 或 IP地址',
                required: true,
                controlType: "input"
            },
            {
                displayName: '端口',
                name: 'port',
                type: 'number' as const,
                default: 1433,
                description: 'SQL Server服务器端口号',
                typeOptions: {
                    minValue: 1,
                    maxValue: 65535
                },
                required: true,
                controlType: "input"
            },
            {
                displayName: '实例名',
                name: 'instance',
                type: 'string' as const,
                default: '',
                description: 'SQL Server实例名（可选）',
                placeholder: 'SQLEXPRESS, MSSQLSERVER等',
                controlType: "input"
            },
            {
                displayName: '用户名',
                name: 'username',
                type: 'string' as const,
                default: '',
                placeholder: "请输入数据库用户名",
                description: '数据库用户名',
                required: true,
                controlType: "input"
            },
            {
                displayName: '密码',
                name: 'password',
                type: 'string' as const,
                default: '',
                description: '数据库密码',
                placeholder: "请输入数据库密码",
                typeOptions: {
                    password: true
                },
                isSecure: true,
                controlType: "input"
            },
            {
                displayName: '数据库名',
                name: 'database',
                type: 'string' as const,
                default: '',
                description: '要连接的数据库名称',
                required: true,
                controlType: "input"
            },
            {
                displayName: '认证方式',
                name: 'authenticationType',
                type: 'options' as const,
                default: 'default',
                description: 'SQL Server认证方式',
                options: [
                    { name: 'SQL Server认证', value: 'default' },
                    { name: 'Windows认证', value: 'ntlm' },
                    { name: 'Azure AD认证', value: 'azure-active-directory-default' }
                ],
                controlType: "select"
            },
            {
                displayName: '启用加密',
                name: 'encrypt',
                type: 'boolean' as const,
                default: true,
                description: '是否启用连接加密（Azure SQL必须启用）',
                controlType: "CheckBox"
            },
            {
                displayName: '信任服务器证书',
                name: 'trustServerCertificate',
                type: 'boolean' as const,
                default: false,
                description: '是否信任服务器证书（开发环境可启用）',
                controlType: "CheckBox"
            },
            {
                displayName: '连接超时(秒)',
                name: 'connectionTimeout',
                type: 'number' as const,
                default: 15,
                description: '连接超时时间，单位：秒',
                typeOptions: {
                    minValue: 1,
                    maxValue: 300
                },
                controlType: "input"
            },
            {
                displayName: '请求超时(秒)',
                name: 'requestTimeout',
                type: 'number' as const,
                default: 15,
                description: '请求超时时间，单位：秒',
                typeOptions: {
                    minValue: 1,
                    maxValue: 300
                },
                controlType: "input"
            }
        ],
        validateConnection: true,
        connectionTimeout: 15000
    },

    /**
     * 测试SQL Server连接
     */
    async test(config: Record<string, any>): Promise<ConnectTestResult> {
        const startTime = Date.now();
        try {
            // 验证必填字段
            const requiredFields = ['host', 'port', 'username', 'database'];
            for (const field of requiredFields) {
                if (!config[field]) {
                    return {
                        success: false,
                        message: `缺少必填字段: ${field}`
                    };
                }
            }

            // 构建连接字符串用于验证
            let server = config.host;
            if (config.instance) {
                server += `\\${config.instance}`;
            }
            if (config.port && config.port !== 1433) {
                server += `,${config.port}`;
            }

            // 模拟连接测试（实际应该使用 mssql 或 tedious 库）
            await new Promise(resolve => setTimeout(resolve, 200));

            const latency = Date.now() - startTime;

            return {
                success: true,
                message: 'SQL Server连接测试成功',
                latency,
                details: {
                    server,
                    database: config.database,
                    authenticationType: config.authenticationType || 'default',
                    encrypt: config.encrypt !== false,
                    trustServerCertificate: config.trustServerCertificate || false,
                    connectionTimeout: config.connectionTimeout || 15,
                    requestTimeout: config.requestTimeout || 15
                }
            };

        } catch (error) {
            return {
                success: false,
                message: `SQL Server连接失败: ${error instanceof Error ? error.message : String(error)}`,
                latency: Date.now() - startTime
            };
        }
    }
};

export default SQLServerConnect; 