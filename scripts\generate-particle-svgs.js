#!/usr/bin/env node

/**
 * SVG粒子生成脚本
 * 用于预生成静态SVG粒子文件，进一步降低运行时消耗
 */

const fs = require('fs');
const path = require('path');

// 确保输出目录存在
const outputDir = path.join(__dirname, '../public/particles');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// 颜色配置
const themes = {
  dark: {
    primary: '#3b82f6',    // 蓝色
    secondary: '#8b5cf6',  // 紫色
    accent: '#06b6d4'      // 青色
  },
  light: {
    primary: '#60a5fa',    // 浅蓝色
    secondary: '#a78bfa',  // 浅紫色
    accent: '#22d3ee'      // 浅青色
  }
};

// 粒子类型配置
const particleTypes = ['circle', 'hexagon', 'star'];
const sizes = [20, 30, 40, 50, 60, 80];

// 生成圆形粒子SVG
function generateCircleSVG(size, theme) {
  const colors = themes[theme];
  return `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="circle-gradient-${theme}-${size}" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:${colors.primary};stop-opacity:0.8" />
      <stop offset="70%" style="stop-color:${colors.secondary};stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:${colors.accent};stop-opacity:0" />
    </radialGradient>
  </defs>
  <circle cx="${size/2}" cy="${size/2}" r="${size/2}" fill="url(#circle-gradient-${theme}-${size})" />
</svg>`;
}

// 生成六边形粒子SVG
function generateHexagonSVG(size, theme) {
  const colors = themes[theme];
  const points = [];
  for (let i = 0; i < 6; i++) {
    const angle = (i * 60) * Math.PI / 180;
    const x = size/2 + (size/2 * 0.8) * Math.cos(angle);
    const y = size/2 + (size/2 * 0.8) * Math.sin(angle);
    points.push(`${x.toFixed(2)},${y.toFixed(2)}`);
  }
  
  return `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="hex-gradient-${theme}-${size}" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:${colors.primary};stop-opacity:0.6" />
      <stop offset="50%" style="stop-color:${colors.secondary};stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:${colors.accent};stop-opacity:0.2" />
    </linearGradient>
  </defs>
  <polygon points="${points.join(' ')}" fill="url(#hex-gradient-${theme}-${size})" />
</svg>`;
}

// 生成星形粒子SVG
function generateStarSVG(size, theme) {
  const colors = themes[theme];
  const outerRadius = size / 2 * 0.8;
  const innerRadius = outerRadius * 0.5;
  const points = [];
  
  for (let i = 0; i < 10; i++) {
    const angle = (i * 36) * Math.PI / 180;
    const radius = i % 2 === 0 ? outerRadius : innerRadius;
    const x = size/2 + radius * Math.cos(angle);
    const y = size/2 + radius * Math.sin(angle);
    points.push(`${x.toFixed(2)},${y.toFixed(2)}`);
  }
  
  return `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="star-gradient-${theme}-${size}" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:${colors.accent};stop-opacity:0.7" />
      <stop offset="60%" style="stop-color:${colors.primary};stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:${colors.secondary};stop-opacity:0.1" />
    </radialGradient>
  </defs>
  <polygon points="${points.join(' ')}" fill="url(#star-gradient-${theme}-${size})" />
</svg>`;
}

// 生成所有粒子文件
console.log('🎨 开始生成SVG粒子文件...');

let totalFiles = 0;
Object.keys(themes).forEach(theme => {
  particleTypes.forEach(type => {
    sizes.forEach(size => {
      let svgContent;
      
      switch(type) {
        case 'circle':
          svgContent = generateCircleSVG(size, theme);
          break;
        case 'hexagon':
          svgContent = generateHexagonSVG(size, theme);
          break;
        case 'star':
          svgContent = generateStarSVG(size, theme);
          break;
      }
      
      const filename = `particle-${type}-${theme}-${size}.svg`;
      const filepath = path.join(outputDir, filename);
      
      fs.writeFileSync(filepath, svgContent);
      totalFiles++;
      
      console.log(`✅ 生成: ${filename}`);
    });
  });
});

// 生成索引文件
const indexContent = `// SVG粒子文件索引
// 自动生成，请勿手动编辑

export interface ParticleFile {
  type: 'circle' | 'hexagon' | 'star';
  theme: 'light' | 'dark';
  size: number;
  filename: string;
}

export const particleFiles: ParticleFile[] = [
${Object.keys(themes).map(theme => 
  particleTypes.map(type =>
    sizes.map(size => `  {
    type: '${type}',
    theme: '${theme}',
    size: ${size},
    filename: 'particle-${type}-${theme}-${size}.svg'
  }`).join(',\n')
  ).join(',\n')
).join(',\n')}
];

export function getParticleFile(type: string, theme: string, size: number): string {
  return \`/particles/particle-\${type}-\${theme}-\${size}.svg\`;
}

export function getRandomParticleFile(theme: 'light' | 'dark' = 'dark'): string {
  const availableTypes = ['circle', 'hexagon', 'star'];
  const availableSizes = [20, 30, 40, 50, 60, 80];
  
  const randomType = availableTypes[Math.floor(Math.random() * availableTypes.length)];
  const randomSize = availableSizes[Math.floor(Math.random() * availableSizes.length)];
  
  return getParticleFile(randomType, theme, randomSize);
}
`;

fs.writeFileSync(path.join(outputDir, 'index.ts'), indexContent);

console.log(`\n🎉 SVG粒子生成完成！`);
console.log(`📁 生成了 ${totalFiles} 个SVG文件`);
console.log(`📍 文件位置: ${outputDir}`);
console.log(`📝 索引文件: ${path.join(outputDir, 'index.ts')}`);

// 生成使用示例文件
const exampleContent = `/**
 * 预制SVG粒子使用示例
 * 这种方式将进一步降低运行时消耗
 */

import { getRandomParticleFile, getParticleFile } from './particles';

// 方式1: 使用随机粒子
function createRandomParticle() {
  const particleUrl = getRandomParticleFile('dark');
  const img = document.createElement('img');
  img.src = particleUrl;
  img.style.cssText = \`
    position: absolute;
    left: \${Math.random() * 100}%;
    top: \${Math.random() * 100}%;
    opacity: \${Math.random() * 0.3 + 0.1};
    transform: rotate(\${Math.random() * 360}deg);
    pointer-events: none;
  \`;
  return img;
}

// 方式2: 使用指定粒子
function createSpecificParticle() {
  const particleUrl = getParticleFile('circle', 'dark', 40);
  const img = document.createElement('img');
  img.src = particleUrl;
  return img;
}

// 方式3: 批量创建粒子
function createParticleField(count = 10, theme = 'dark') {
  const container = document.createElement('div');
  container.style.cssText = \`
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 50;
  \`;
  
  for (let i = 0; i < count; i++) {
    const particle = createRandomParticle();
    container.appendChild(particle);
  }
  
  return container;
}
`;

fs.writeFileSync(path.join(outputDir, 'example.ts'), exampleContent);
console.log(`📖 使用示例: ${path.join(outputDir, 'example.ts')}`); 