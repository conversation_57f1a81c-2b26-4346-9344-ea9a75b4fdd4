import { NextRequest, NextResponse } from "next/server";
import { triggerConfig, Workflow, WorkflowEdge } from "@repo/engine";
import { EventMediator } from "@repo/engine"

export async function POST(req: NextRequest){
    try {
        const data = await req.json() as unknown as Workflow;
        if((data.actions && data.actions.length == 1 && data.actions[0]) && (data.edges && data.edges.length == 0)) {
            data.edges.push({
                from: "$source",
                to: data.actions[0].id
            } as WorkflowEdge)
        }

        const json = await EventMediator.sendEvent(triggerConfig.event, data, (req.nextUrl.searchParams.get("waitoutput") as string)?.toLowerCase() === 'true');
        return NextResponse.json(json);
    } catch (error) {
        return NextResponse.json(
            { 
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined
            }, 
            { status: 500 }
        );
    }
}