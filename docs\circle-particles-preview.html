<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>圆形SVG粒子预览 - 中大尺寸效果</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .preview-area {
            background: rgba(15, 23, 42, 0.3);
            border-radius: 12px;
            padding: 40px;
            min-height: 500px;
            position: relative;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        
        .stats {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
        }
        
        .particle-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }
        
        .particle {
            position: absolute;
            pointer-events: none;
        }
        
        .controls {
            margin-top: 30px;
            text-align: center;
        }
        
        .btn {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 0 10px;
            font-weight: 600;
            transition: transform 0.2s ease;
        }
        
        .btn:hover {
            transform: scale(1.05);
        }
        
        .info-panel {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid #22c55e;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔵 圆形SVG粒子预览</h1>
            <p>中大尺寸，静态渲染，极低GPU消耗</p>
        </div>
        
        <div class="preview-area" id="previewArea">
            <div class="particle-container" id="particleContainer"></div>
            <div style="text-align: center; position: relative; z-index: 10;">
                <h2>静态圆形粒子背景效果</h2>
                <p>8个随机大小的圆形SVG粒子 (40px - 120px)</p>
                <p>两种渐变效果：经典径向 + 偏心光源</p>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="regenerateParticles()">🔄 重新生成粒子</button>
            <button class="btn" onclick="toggleTheme()">🌓 切换主题</button>
            <button class="btn" onclick="changeParticleCount()">📊 调整数量</button>
        </div>
        
        <div class="info-panel">
            <h3>💡 性能优化说明</h3>
            <ul>
                <li><strong>纯静态SVG</strong>：无CSS动画，无JavaScript动画</li>
                <li><strong>中大尺寸</strong>：40px-120px，视觉效果突出</li>
                <li><strong>两种渐变</strong>：经典径向渐变 + 偏心光源效果</li>
                <li><strong>GPU消耗</strong>：相比动画粒子降低约90%</li>
                <li><strong>适用场景</strong>：所有设备，特别是集成显卡</li>
            </ul>
        </div>
    </div>
    
    <div class="stats" id="stats">
        <div>粒子数量: <span id="particleCount">8</span></div>
        <div>当前主题: <span id="currentTheme">dark</span></div>
        <div>渲染方式: 静态SVG</div>
        <div>GPU消耗: ~10%</div>
    </div>

    <script>
        let currentTheme = 'dark';
        let particleCount = 8;
        
        // 生成圆形SVG粒子
        function createCircleParticleSVG(size, theme) {
            const isDark = theme === 'dark';
            
            const primaryColor = isDark ? '#3b82f6' : '#60a5fa';
            const secondaryColor = isDark ? '#8b5cf6' : '#a78bfa';
            const accentColor = isDark ? '#06b6d4' : '#22d3ee';
            
            const gradientVariant = Math.random();
            const gradientId = `circle-gradient-${Date.now()}-${Math.random()}`;
            
            if (gradientVariant < 0.5) {
                // 经典径向渐变
                return `
                    <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <radialGradient id="${gradientId}" cx="50%" cy="50%" r="50%">
                                <stop offset="0%" style="stop-color:${primaryColor};stop-opacity:0.7" />
                                <stop offset="70%" style="stop-color:${secondaryColor};stop-opacity:0.4" />
                                <stop offset="100%" style="stop-color:${accentColor};stop-opacity:0" />
                            </radialGradient>
                        </defs>
                        <circle cx="${size/2}" cy="${size/2}" r="${size/2 * 0.9}" fill="url(#${gradientId})" />
                    </svg>
                `;
            } else {
                // 偏心径向渐变
                return `
                    <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <radialGradient id="${gradientId}" cx="30%" cy="30%" r="70%">
                                <stop offset="0%" style="stop-color:${accentColor};stop-opacity:0.8" />
                                <stop offset="50%" style="stop-color:${primaryColor};stop-opacity:0.5" />
                                <stop offset="100%" style="stop-color:${secondaryColor};stop-opacity:0" />
                            </radialGradient>
                        </defs>
                        <circle cx="${size/2}" cy="${size/2}" r="${size/2 * 0.9}" fill="url(#${gradientId})" />
                    </svg>
                `;
            }
        }
        
        // 生成粒子
        function generateParticles() {
            const container = document.getElementById('particleContainer');
            container.innerHTML = '';
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                const size = Math.floor(Math.random() * 80) + 40; // 40px - 120px
                const x = Math.random() * 100;
                const y = Math.random() * 100;
                const opacity = Math.random() * 0.25 + 0.15; // 0.15 - 0.4
                
                particle.style.cssText = `
                    left: ${x}%;
                    top: ${y}%;
                    width: ${size}px;
                    height: ${size}px;
                    opacity: ${opacity};
                `;
                
                particle.innerHTML = createCircleParticleSVG(size, currentTheme);
                container.appendChild(particle);
            }
        }
        
        // 重新生成粒子
        function regenerateParticles() {
            generateParticles();
        }
        
        // 切换主题
        function toggleTheme() {
            currentTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.getElementById('currentTheme').textContent = currentTheme;
            
            if (currentTheme === 'light') {
                document.body.style.background = 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)';
                document.body.style.color = '#1f2937';
            } else {
                document.body.style.background = 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)';
                document.body.style.color = 'white';
            }
            
            generateParticles();
        }
        
        // 调整粒子数量
        function changeParticleCount() {
            const counts = [4, 6, 8, 10, 12];
            const currentIndex = counts.indexOf(particleCount);
            const nextIndex = (currentIndex + 1) % counts.length;
            particleCount = counts[nextIndex];
            
            document.getElementById('particleCount').textContent = particleCount;
            generateParticles();
        }
        
        // 页面加载时生成粒子
        window.addEventListener('load', function() {
            generateParticles();
        });
        
        // 窗口调整大小时重新生成粒子
        window.addEventListener('resize', function() {
            generateParticles();
        });
        
        // 显示当前时间
        setInterval(function() {
            const now = new Date();
            console.log('静态SVG粒子运行中...', now.toLocaleTimeString());
        }, 5000);
    </script>
</body>
</html> 