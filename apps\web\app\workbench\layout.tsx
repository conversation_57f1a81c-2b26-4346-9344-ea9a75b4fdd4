"use client";

import React, { useEffect, useState } from 'react';
import { useRouter, useSelectedLayoutSegment } from 'next/navigation';
import Link from 'next/link';
import {
  PageContainer,
  MainContent,
  SidebarToggle,
  ToggleDots,
  ThemeProvider,
  GlassSidebar,
  GlassSidebarItem,
  GlassSidebarIconContainer,
  GlassSidebarLabel
} from '@repo/ui/main';
import { IoMdHome } from "react-icons/io";
import { GrConnectivity } from "react-icons/gr";
import { SiAkasaair } from "react-icons/si";
import { TbHelpHexagonFilled } from "react-icons/tb";
import { IoMdSettings } from "react-icons/io";
import { BsPersonFillGear } from "react-icons/bs";
import { MdGroups } from "react-icons/md";

import { useAuth } from '../../src/hooks/useAuth';
import { WorkflowProvider } from '@/contexts/WorkflowContext';

export default function WorkbenchLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isAuthenticated, loading } = useAuth();
  const router = useRouter();
  const segment = useSelectedLayoutSegment();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  

  // 检查用户是否已登录
  useEffect(() => {
    if (!loading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, loading, router]);

  // 如果正在加载或未认证，显示加载状态
  if (loading || !isAuthenticated) {
    return <div>Loading...</div>;
  }

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  }

  return (
    <ThemeProvider>
      <WorkflowProvider>
        <PageContainer style={{ position: 'relative' }}>
          <GlassSidebar $collapsed={sidebarCollapsed}>
            <Link href="/workbench/home" style={{ textDecoration: 'none' }}>
              <GlassSidebarItem $active={segment === 'home' || segment === null} $collapsed={sidebarCollapsed}>
                <GlassSidebarIconContainer $active={segment === 'home' || segment === null} $collapsed={sidebarCollapsed}>
                  <IoMdHome />
                </GlassSidebarIconContainer>
                <GlassSidebarLabel $active={segment === 'home' || segment === null} $collapsed={sidebarCollapsed}>
                  主页
                </GlassSidebarLabel>
              </GlassSidebarItem>
            </Link>
            <Link href="/workbench/connections" style={{ textDecoration: 'none' }}>
              <GlassSidebarItem $active={segment === 'connections'} $collapsed={sidebarCollapsed}>
                <GlassSidebarIconContainer $active={segment === 'connections'} $collapsed={sidebarCollapsed}>
                  <GrConnectivity />
                </GlassSidebarIconContainer>
                <GlassSidebarLabel $active={segment === 'connections'} $collapsed={sidebarCollapsed}>
                  连接
                </GlassSidebarLabel>
              </GlassSidebarItem>
            </Link>
            <Link href="/workbench/agent" style={{ textDecoration: 'none' }}>
              <GlassSidebarItem $active={segment === 'agent'} $collapsed={sidebarCollapsed}>
                <GlassSidebarIconContainer $active={segment === 'agent'} $collapsed={sidebarCollapsed}>
                  <SiAkasaair />
                </GlassSidebarIconContainer>
                <GlassSidebarLabel $active={segment === 'agent'} $collapsed={sidebarCollapsed}>
                  AI
                </GlassSidebarLabel>
              </GlassSidebarItem>
            </Link>
            <Link href="/workbench/team" style={{ textDecoration: 'none' }}>
              <GlassSidebarItem $active={segment === 'team'} $collapsed={sidebarCollapsed}>
                <GlassSidebarIconContainer $active={segment === 'team'} $collapsed={sidebarCollapsed}>
                  <MdGroups />
                </GlassSidebarIconContainer>
                <GlassSidebarLabel $active={segment === 'team'} $collapsed={sidebarCollapsed}>
                  协作
                </GlassSidebarLabel>
              </GlassSidebarItem>
            </Link>
            <div style={{ marginTop: 'auto' }}>
              <Link href="/workbench/help" style={{ textDecoration: 'none' }}>
                <GlassSidebarItem $active={segment === 'help'} $collapsed={sidebarCollapsed}>
                  <GlassSidebarIconContainer $active={segment === 'help'} $collapsed={sidebarCollapsed}>
                    <TbHelpHexagonFilled />
                  </GlassSidebarIconContainer>
                  <GlassSidebarLabel $active={segment === 'help'} $collapsed={sidebarCollapsed}>
                    帮助
                  </GlassSidebarLabel>
                </GlassSidebarItem>
              </Link>
              <Link href="../workbench/setting" style={{ textDecoration: 'none' }}>
                <GlassSidebarItem $active={segment === 'setting'} $collapsed={sidebarCollapsed}>
                  <GlassSidebarIconContainer $active={segment === 'setting'} $collapsed={sidebarCollapsed}>
                    <IoMdSettings />
                  </GlassSidebarIconContainer>
                  <GlassSidebarLabel $active={segment === 'setting'} $collapsed={sidebarCollapsed}>
                    系统
                  </GlassSidebarLabel>
                </GlassSidebarItem>
              </Link>
              <Link href="/workbench/profile" style={{ textDecoration: 'none' }}>
                <GlassSidebarItem $active={segment === 'profile'} $collapsed={sidebarCollapsed}>
                  <GlassSidebarIconContainer $active={segment === 'profile'} $collapsed={sidebarCollapsed}>
                    <BsPersonFillGear  />
                  </GlassSidebarIconContainer>
                  <GlassSidebarLabel $active={segment === 'profile'} $collapsed={sidebarCollapsed}>
                    个人
                  </GlassSidebarLabel>
                </GlassSidebarItem>
              </Link>
            </div>

            <SidebarToggle onClick={toggleSidebar}>
              <ToggleDots>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
              </ToggleDots>
            </SidebarToggle>

          </GlassSidebar>

          <MainContent>
            {children}
          </MainContent>
        </PageContainer>
      </WorkflowProvider>
    </ThemeProvider>
  );
}
