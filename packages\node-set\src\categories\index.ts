import { ICategory } from '@repo/common';
import { GeneralCategory } from './general';
import { AICategory } from './AI';
import { TriggerCategory } from './trigger';
// 导入其他分类文件...

const allCategories: Record<string, ICategory> = {
    [TriggerCategory.id]: TriggerCategory,
    [AICategory.id]: AICategory,
    [GeneralCategory.id]: GeneralCategory,
};

export const getCategoryById = (id: string): ICategory | undefined => {
    return allCategories[id];
};

export const getAllCategories = (): ICategory[] => {
    return Object.values(allCategories);
};

// 直接导出所有分类对象，如果前端或后端需要直接访问
export { TriggerCategory, AICategory , GeneralCategory};