import React, { useState, useMemo } from 'react';
import { WorkflowListProps } from './types';
import { filterAndSortWorkflows, paginateItems } from './utils';
import { SearchAndFilter } from './SearchAndFilter';
import { WorkflowCard } from './WorkflowCard';
import { Pagination } from './Pagination';
import { ToolbarControls } from '../chat/ToolbarControls';

export const WorkflowList: React.FC<WorkflowListProps> = ({
  workflows,
  onWorkflowClick,
  onToggleWorkflow,
  onCreateWorkflow,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('last-updated');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);

  // 过滤和排序工作流
  const filteredWorkflows = useMemo(() => 
    filterAndSortWorkflows(workflows, searchTerm, sortBy),
    [workflows, searchTerm, sortBy]
  );

  // 分页处理
  const paginationData = useMemo(() => 
    paginateItems(filteredWorkflows, currentPage, pageSize),
    [filteredWorkflows, currentPage, pageSize]
  );

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 工具栏区域 */}
      <ToolbarControls
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        searchPlaceholder="Search"
        sortBy={sortBy}
        onSortChange={() => setSortBy(sortBy === 'last-updated' ? 'name' : 'last-updated')}
      />

      {/* 工作流卡片区域 */}
      <div style={{ 
        flex: 1, 
        padding: '0 30px', 
        overflowY: 'auto',
        marginBottom: '20px'
      }}>
        {paginationData.paginatedItems.map((workflow) => (
          <WorkflowCard
            key={workflow.id}
            workflow={workflow}
            onWorkflowClick={onWorkflowClick}
            onToggleWorkflow={onToggleWorkflow}
          />
        ))}
      </div>

      {/* 分页区域 */}
      <Pagination
        currentPage={currentPage}
        totalPages={paginationData.totalPages}
        totalItems={filteredWorkflows.length}
        pageSize={pageSize}
        onPageChange={setCurrentPage}
        onPageSizeChange={handlePageSizeChange}
      />
    </div>
  );
}; 