"use client";

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { ConnectParameterInput } from './ConnectParameterInput';
import { LLMDialogue } from '../../../controls/llmdialogue';
import { ILLMConnect } from '@repo/common';
import {
  Container,
  ConfigPanel,
  Header,
  Icon,
  Title,
  Subtitle,
  FormSection,
  FieldsGrid,
  ButtonContainer,
  Button,
  TestButton,
  StatusMessage,
  InputContainer,
  Label,
  Required,
  TextInput
} from '../../shared/modalStyle';

// 对话框显示在右侧，并添加从右往左的动画效果
// const DialoguePanel = styled.div<{ $isVisible: boolean }>`
//   width: 45%;
//   min-width: 350px;
//   height: 100%;
//   background: #fafafa;
//   border-left: 1px solid #e0e0e0;
//   padding: 20px;
//   overflow: hidden;
//   display: flex;
//   flex-direction: column;
//   position: ${props => props.$isVisible ? 'relative' : 'absolute'};
//   right: ${props => props.$isVisible ? 'auto' : '0'};
//   z-index: ${props => props.$isVisible ? 'auto' : '-1'};

//   /* 从右往左滑入动画 */
//   transform: ${props => props.$isVisible ? 'translateX(0)' : 'translateX(100%)'};
//   transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
//   opacity: ${props => props.$isVisible ? '1' : '0'};

//   /* 小屏幕适配 */
//   @media (max-width: 1200px) {
//     width: 50%;
//     min-width: 300px;
//   }

//   @media (max-width: 768px) {
//     width: 100%;
//     min-width: unset;
//     border-left: none;
//     border-top: 1px solid #e0e0e0;
//     height: 40%;
//     position: ${props => props.$isVisible ? 'relative' : 'absolute'};
//     bottom: ${props => props.$isVisible ? 'auto' : '0'};
//     transform: ${props => props.$isVisible ? 'translateY(0)' : 'translateY(100%)'};
//   }
// `;

interface ConnectSettingsProps {
  connect: ILLMConnect;
  savedValues?: Record<string, any>;
  onClose: () => void;
  onSave: (connectData: any) => void;
  onTest?: (config: Record<string, any>, message?: string) => Promise<any>;
  onStreamTest?: (config: Record<string, any>, message: string, onChunk: (chunk: string) => void) => Promise<any>;
  onDialogueToggle?: (show: boolean) => void;
  editMode?: boolean;
  editData?: {
    id: string;
    connectId: string;
    name: string;
    config: Record<string, any>;
  };
}

export const ConnectSettings: React.FC<ConnectSettingsProps> = ({
  connect,
  savedValues = {},
  onClose,
  onSave,
  onTest,
  onStreamTest,
  onDialogueToggle,
  editMode = false,
  editData
}) => {
  const [formValues, setFormValues] = useState<Record<string, any>>({});
  const [configName, setConfigName] = useState<string>('');
  const [testStatus, setTestStatus] = useState<{
    type: 'success' | 'error' | 'info' | null;
    message: string;
  }>({ type: null, message: '' });
  const [isTestLoading, setIsTestLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showDialogue, setShowDialogue] = useState(false);

  // 判断是否为LLM连接，添加安全检查
  const isLLMConnect = connect?.overview?.type === 'llm';

  // 当对话显示状态改变时，通知父组件
  const updateDialogueVisibility = (show: boolean) => {
    setShowDialogue(show);
    onDialogueToggle?.(show);
  };

  // 初始化表单值
  useEffect(() => {
    // 添加安全检查
    if (!connect?.detail?.fields) {
      console.warn('连接详情数据无效: fields 属性缺失');
      return;
    }

    const initialValues: Record<string, any> = {};

    // 使用保存的值或默认值
    connect.detail.fields.forEach(field => {
      if (savedValues[field.name] !== undefined) {
        initialValues[field.name] = savedValues[field.name];
      } else if (field.default !== undefined) {
        initialValues[field.name] = field.default;
      }
    });

    setFormValues(initialValues);
  }, [connect?.detail?.fields, savedValues]);

  // 初始化配置名称 - 支持编辑模式
  useEffect(() => {
    if (editMode && editData) {
      // 编辑模式：使用现有配置名称
      setConfigName(editData.name);
    } else if (!configName && connect?.overview?.name) {
      // 新建模式：生成默认配置名称
      setConfigName(`${connect.overview.name}模型`);
    }
  }, [connect?.overview?.name, editMode, editData]);

  // 组件卸载时重置对话状态
  useEffect(() => {
    return () => {
      if (showDialogue) {
        updateDialogueVisibility(false);
      }
    };
  }, [showDialogue]);

  // 数据有效性检查
  if (!connect?.overview || !connect?.detail) {
    return (
      <Container>
        <ConfigPanel $hasDialogue={false}>
          <StatusMessage type="error">
            连接数据无效，请重新选择连接
          </StatusMessage>
          <ButtonContainer>
            <Button onClick={onClose}>返回</Button>
          </ButtonContainer>
        </ConfigPanel>
      </Container>
    );
  }

  const handleInputChange = (name: string, value: any) => {
    console.log('字段值变化:', name, '新值:', value);
    setFormValues(prev => {
      const newFormValues = {
        ...prev,
        [name]: value
      };
      console.log('更新后的formValues:', newFormValues);
      return newFormValues;
    });

    // 清除测试状态，确保用户修改配置后可以重新测试
    if (testStatus.type) {
      setTestStatus({ type: null, message: '' });
    }
  };

  const handleTest = async () => {
    if (!onTest || !connect.detail.validateConnection) return;

    console.log('开始测试连接，当前formValues:', formValues);

    // 如果是LLM连接，显示对话界面
    if (isLLMConnect) {
      updateDialogueVisibility(true);
      setTestStatus({
        type: 'info',
        message: '对话测试界面已打开，请在右侧输入您的问题进行测试'
      });
      return;
    }

    // 否则进行普通的连接测试
    setIsTestLoading(true);
    setTestStatus({ type: 'info', message: '正在测试连接...' });

    try {
      const result = await onTest(formValues);

      if (result.success) {
        setTestStatus({
          type: 'success',
          message: result.message || '连接测试成功'
        });
        console.log('连接测试成功，保存按钮应该可用');
      } else {
        setTestStatus({
          type: 'error',
          message: result.message || '连接测试失败'
        });
        console.log('连接测试失败，但保存按钮仍应可用');
      }
    } catch (error) {
      setTestStatus({
        type: 'error',
        message: error instanceof Error ? error.message : '连接测试失败'
      });
      console.log('连接测试异常，但保存按钮仍应可用');
    } finally {
      setIsTestLoading(false);
      console.log('测试完成，isTestLoading设为false，表单状态保持不变');
    }
  };

  // LLM对话测试处理
  const handleLLMTest = async (config: Record<string, any>, message: string): Promise<any> => {
    if (!onTest) return { success: false, message: '测试接口未配置' };

    try {
      const result = await onTest(config, message);
      return result;
    } catch (error) {
      console.error('LLM对话测试失败:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '对话测试失败'
      };
    }
  };

  const handleSave = async () => {
    console.log('🚀 handleSave 函数被调用');
    
    // 添加安全检查
    if (!connect?.overview) {
      console.error('连接数据无效: overview 属性缺失');
      setTestStatus({
        type: 'error',
        message: '连接数据无效，请重新选择连接'
      });
      return;
    }

    setIsSaving(true);

    try {
      // 准备config数据，如果overview中有driver属性，则添加到config中
      const configData = { ...formValues };
      if (connect.overview.driver) {
        configData.driver = connect.overview.driver;
      }
      
      const connectData = {
        ...(editMode && editData ? { id: editData.id } : {}), // 编辑模式下包含记录ID
        connectId: connect.overview.id,
        name: configName || `${connect.overview.name}`,
        config: configData,
        mtype: connect.overview.type // 如果 connectType 不存在，使用 connect.type 作为后备
      };

      console.log('准备保存的数据:', connectData);

      await onSave(connectData);
      console.log('配置保存成功');
      // 保存成功后关闭弹窗
      onClose();
    } catch (error) {
      console.error('保存配置失败:', error);
      setTestStatus({
        type: 'error',
        message: '保存配置失败，请重试'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSave();
  };

  // 检查必填字段
  const hasRequiredFields = () => {
    // 添加安全检查
    if (!connect?.detail?.fields) {
      console.warn('连接详情数据无效: fields 属性缺失');
      return false;
    }

    // 检查配置名称是否填写
    if (!configName || configName.trim() === '') {
      //console.log('配置名称未填写:', configName);
      return false;
    }

    // 检查连接字段必填项
    const requiredFields = connect.detail.fields.filter(field => field.required);
    const missingFields = requiredFields.filter(field => {
      const value = formValues[field.name];
      const isEmpty = value === undefined || value === '' || value === null;
      if (isEmpty) {
        console.log('必填字段缺失:', field.name, '当前值:', value);
      }
      return isEmpty;
    });

    return missingFields.length === 0;
  };

  // 关闭对话界面
  const handleCloseDialogue = () => {
    updateDialogueVisibility(false);
    setTestStatus({ type: null, message: '' });
  };

  return (
    <Container>
      <ConfigPanel $hasDialogue={showDialogue}>
        <Header>
          <Icon>{connect.overview.icon as string}</Icon>
          <div>
            <Title>{connect.overview.name} 连接配置</Title>
            <Subtitle>{connect.overview.description}</Subtitle>
          </div>
        </Header>

        <FormSection>
          {/* 配置名称输入 */}
          <InputContainer>
            <Label>
              配置名称
              <Required>*</Required>
            </Label>
            <TextInput
              type="text"
              value={configName}
              onChange={(e) => setConfigName(e.target.value)}
              placeholder="请输入配置名称"
              required
            />
          </InputContainer>

          {/* 连接字段 - 根据是否有对话框调整布局 */}
          <FieldsGrid $hasDialogue={showDialogue}>
            {connect.detail.fields
              .filter(field => field.controlType !== 'llmdialogue') // 过滤掉对话控件
              .map((field) => {
                // console.log('ConnectSettings - 渲染字段:', field.name, {
                //   field,
                //   value: formValues[field.name]
                // });
                return (
                  <ConnectParameterInput
                    key={field.name}
                    field={field}
                    value={formValues[field.name]}
                    onChange={handleInputChange}
                    formValues={formValues}
                  />
                );
              })}
          </FieldsGrid>

          <ButtonContainer>
            <Button type="button" onClick={onClose}>
              取消
            </Button>

            {connect.detail.validateConnection && onTest && (
              <TestButton
                type="button"
                onClick={handleTest}
                disabled={isTestLoading || !hasRequiredFields()}
              >
                {isTestLoading ? '测试中...' : isLLMConnect ? '开启对话测试' : '测试连接'}
              </TestButton>
            )}

            <Button
              $variant="primary"
              type="button"
              onClick={handleSave}
              disabled={isSaving || !hasRequiredFields()}
            >
              {isSaving ? '保存中...' : '保存配置'}
            </Button>
          </ButtonContainer>
        </FormSection>

        {testStatus.type && (
          <StatusMessage type={testStatus.type}>
            {testStatus.message}
          </StatusMessage>
        )}
      </ConfigPanel>

      {/* {isLLMConnect && (
        <DialoguePanel $isVisible={showDialogue}>
          {showDialogue && (
            <LLMDialogue
              title={`${connect.overview.name} 对话测试`}
              placeholder="输入您的问题来测试大模型..."
              onTest={handleLLMTest}
              onStreamTest={onStreamTest}
              connectConfig={formValues}
              disabled={!hasRequiredFields()}
              onClose={handleCloseDialogue}
            />
          )}
        </DialoguePanel>
      )} */}
    </Container>
  );
};