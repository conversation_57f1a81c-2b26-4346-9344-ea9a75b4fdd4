"use client";

import React, { useState, useMemo, useCallback } from 'react';
import styled from 'styled-components';

// JSON tree 容器样式
const JsonTreeContainer = styled.div`
  .json-tree {
    font-family: monospace;
    font-size: 13px;
    line-height: 1.0;
    color: ${({ theme }) => theme.colors.textPrimary};
  }

  .vjs-tree-node {
    padding: 2px 0;
    display: flex;
    align-items: center;
    position: relative;
  }

  .vjs-indent {
    display: flex;
    align-items: center;
    position: relative;
  }

  .vjs-indent-line {
    width: 18px;
    height: 20px;
    position: relative;
    display: inline-block;
    
    &::before {
      content: '';
      position: absolute;
      left: 9px;
      top: 0;
      bottom: 0;
      width: 1px;
      background: rgba(255, 255, 255, 0.2);
    }
    
    &:last-child::after {
      content: '';
      position: absolute;
      left: 9px;
      top: 50%;
      width: 9px;
      height: 1px;
      background: rgba(255, 255, 255, 0.2);
    }
  }

  .vjs-icon {
    width: 16px;
    height: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 6px;
    font-size: 13px;
    user-select: none;
  }

  .vjs-icon-toggle {
    cursor: pointer;
    color: rgb(102, 217, 239);
    transition: all 0.2s ease;
    
    &:hover {
      color: rgb(109, 155, 192);
      transform: scale(1.2);
    }
    
    &.expanded {
      color: #569cd6;
      font-size: 16px;
    }
    
    &.collapsed {
      color: rgb(174, 129, 255);
      font-size: 18px;
    }
  }

  .vjs-icon-leaf {
    color: rgba(255, 255, 255, 0.3);
    font-size: 10px;
  }

  .vjs-key {
    color: #fff;
    font-weight: 500;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
    padding: 2px 4px;
    border-radius: 3px;
    white-space: nowrap;
    
    &:hover {
      background-color: rgba(86, 156, 214, 0.3);
      color: #7bb3f0;
    }
    
    &:active {
      background-color: rgba(86, 156, 214, 0.5);
    }
  }

  .vjs-colon {
    margin: 0 6px;
    color: ${({ theme }) => theme.colors.textPrimary};
    opacity: 0.8;
  }

  .vjs-value {
    color: ${({ theme }) => theme.colors.textPrimary};
    word-break: break-word;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
    padding: 2px 4px;
    border-radius: 3px;
    
    &:hover {
      background-color: rgba(86, 156, 214, 0.2);
      color: #7bb3f0;
    }
    
    &:active {
      background-color: rgba(86, 156, 214, 0.4);
    }
  }

  .vjs-value-string {
    color: #f39c12;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
    padding: 2px 4px;
    border-radius: 3px;
    
    &:hover {
      background-color: rgba(243, 156, 18, 0.2);
      color: #f5c842;
    }
    
    &:active {
      background-color: rgba(243, 156, 18, 0.3);
    }
  }

  .vjs-value-number {
    color: rgb(166, 226, 46);
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
    padding: 2px 4px;
    border-radius: 3px;
    
    &:hover {
      background-color: rgba(166, 226, 46, 0.2);
      color: rgb(186, 246, 66);
    }
    
    &:active {
      background-color: rgba(166, 226, 46, 0.3);
    }
  }

  .vjs-value-boolean {
    color: #3498db;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
    padding: 2px 4px;
    border-radius: 3px;
    
    &:hover {
      background-color: rgba(52, 152, 219, 0.2);
      color: #5dade2;
    }
    
    &:active {
      background-color: rgba(52, 152, 219, 0.3);
    }
  }

  .vjs-value-null {
    color: #95a5a6;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
    padding: 2px 4px;
    border-radius: 3px;
    
    &:hover {
      background-color: rgba(149, 165, 166, 0.2);
      color: #bdc3c7;
    }
    
    &:active {
      background-color: rgba(149, 165, 166, 0.3);
    }
  }

  .vjs-value-object {
    color: #e74c3c;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
    padding: 2px 4px;
    border-radius: 3px;
    
    &:hover {
      background-color: rgba(231, 76, 60, 0.2);
      color: #ec7063;
    }
    
    &:active {
      background-color: rgba(231, 76, 60, 0.3);
    }
  }

  .vjs-expandable {
    color: #e74c3c;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
    padding: 2px 4px;
    border-radius: 3px;
    
    &:hover {
      background-color: rgba(231, 76, 60, 0.2);
      color: #ec7063;
    }
    
    &:active {
      background-color: rgba(231, 76, 60, 0.3);
    }
  }

  .vjs-bracket {
    color: ${({ theme }) => theme.colors.textPrimary};
    opacity: 0.6;
    margin-left: 4px;
  }

  .vjs-comma {
    color: ${({ theme }) => theme.colors.textPrimary};
    opacity: 0.6;
    margin-left: 2px;
  }

  [draggable="true"] {
    transition: all 0.2s ease;
    
    &:active {
      opacity: 0.7;
      transform: scale(0.98);
    }
  }

  .copied {
    background: rgba(46, 204, 113, 0.3);
    border-color: #2ecc71;
    animation: pulse 0.5s;
  }
`;

interface TreeNodeProps {
  nodeKey: string;
  value: any;
  level: number;
  isLast: boolean;
  parentPath: string;
  isArrayItem?: boolean;
  onToggle: () => void;
  isExpanded: boolean;
  nodeId?: any;
}

// TreeNode component
const TreeNode = React.memo<TreeNodeProps>(({
  nodeKey,
  value,
  level,
  isLast,
  parentPath,
  isArrayItem = false,
  onToggle,
  isExpanded,
  nodeId
}) => {
  const currentPath = isArrayItem ? `${nodeId}.${parentPath}[${nodeKey}]` : (parentPath ? `${nodeId}.${parentPath}.${nodeKey}` : `${nodeId}.${nodeKey}`);
  const isExpandable = typeof value === 'object' && value !== null;

  const renderIndent = () => {
    const indentElements = [];
    for (let i = 0; i < level; i++) {
      indentElements.push(<span key={i} className="vjs-indent-line" />);
    }
    return indentElements;
  };

  const renderIcon = () => {
    if (isExpandable) {
      return (
        <span 
          className={`vjs-icon vjs-icon-toggle ${isExpanded ? 'expanded' : 'collapsed'}`}
          onClick={onToggle}
        >
          {isExpanded ? '⊖' : '⊕'}
        </span>
      );
    }
    return <span className="vjs-icon vjs-icon-leaf">•</span>;
  };

  const getValueClass = () => {
    const type = typeof value;
    if (value === null) return 'vjs-value-null';
    if (type === 'string') return 'vjs-value-string';
    if (type === 'number') return 'vjs-value-number';
    if (type === 'boolean') return 'vjs-value-boolean';
    if (type === 'object') return 'vjs-value-object';
    return 'vjs-value';
  };

  const getDisplayValue = () => {
    if (value === null) return 'null';
    if (typeof value === 'string') return `"${value}"`;
    if (typeof value === 'object') {
      const keys = Object.keys(value);
      const summary = Array.isArray(value) 
        ? `Array(${value.length})`
        : `Object(${keys.length})`;
      return summary;
    }
    return String(value);
  };

  const displayValue = getDisplayValue();

  const createDragHandlers = (dragPath: string) => ({
    onDragStart: (e: React.DragEvent) => {
      // 检查拖拽目标是否为受限制的输入框
      const checkForRestrictedInput = (element: Element | null): boolean => {
        if (!element) return false;
        
        // 检查当前元素是否有 data-no-json-drop 属性
        if (element.hasAttribute('data-no-json-drop')) {
          return true;
        }
        
        // 递归检查父元素
        return checkForRestrictedInput(element.parentElement);
      };

      // 监听整个拖拽过程
      const handleDragOver = (event: Event) => {
        const dragEvent = event as DragEvent;
        const target = dragEvent.target as Element;
        
        if (checkForRestrictedInput(target)) {
          dragEvent.preventDefault();
          dragEvent.stopPropagation();
          if (dragEvent.dataTransfer) {
            dragEvent.dataTransfer.dropEffect = 'none';
          }
        }
      };

      // 临时添加全局拖拽监听
      document.addEventListener('dragover', handleDragOver);
      
      // 清理监听器
      const cleanup = () => {
        document.removeEventListener('dragover', handleDragOver);
        document.removeEventListener('dragend', cleanup);
      };
      
      document.addEventListener('dragend', cleanup);

      console.log('🚀 JsonTree drag start - dragPath:', dragPath);
      console.log('🚀 JsonTree drag start - timestamp:', Date.now());
      
      const plainData = `{{ $.${dragPath} }}`;
      const noBracesData = `$.${dragPath}`;
      const jsonData = JSON.stringify({
        type: 'json-path',
        path: dragPath,
        value: dragPath,
        noBraces: noBracesData
      });
      
      e.dataTransfer.setData('text/plain', plainData);
      e.dataTransfer.setData('text/plain-no-braces', noBracesData);
      e.dataTransfer.setData('application/json', jsonData);
      e.dataTransfer.effectAllowed = 'copy';
      
      // console.log('📦 JsonTree data set:');
      // console.log('  - text/plain:', `"${plainData}"`);
      // console.log('  - text/plain-no-braces:', `"${noBracesData}"`);
      // console.log('  - application/json:', jsonData);
      // console.log('  - dataTransfer McpInterfaces.ts after set:', Array.from(e.dataTransfer.types));
      e.stopPropagation();
    }
  });

  // 数组项渲染
  if (isArrayItem && isExpandable) {
    if (isExpanded) {
      return (
        <div className="vjs-tree-node">
          <div className="vjs-indent">
            {renderIndent()}
          </div>
          {renderIcon()}
          <span 
            className="vjs-key"
            draggable={true}
            {...createDragHandlers(currentPath)}
          >
            {nodeKey}
          </span>
          <span className="vjs-colon">: </span>
          <span className="vjs-bracket">
            {Array.isArray(value) ? '[' : '{'}
          </span>
        </div>
      );
    } else {
      return (
        <div className="vjs-tree-node">
          <div className="vjs-indent">
            {renderIndent()}
          </div>
          {renderIcon()}
          <span 
            className="vjs-key"
            draggable={true}
            {...createDragHandlers(currentPath)}
          >
            {nodeKey}
          </span>
          <span className="vjs-colon">: </span>
          <span 
            className="vjs-expandable"
            onClick={onToggle}
            draggable={true}
            {...createDragHandlers(currentPath)}
          >
            {displayValue}
          </span>
          {!isLast && <span className="vjs-comma">,</span>}
        </div>
      );
    }
  }
  
  // 数组项叶子节点
  if (isArrayItem) {
    return (
      <div className="vjs-tree-node">
        <div className="vjs-indent">
          {renderIndent()}
        </div>
        {renderIcon()}
        <span 
          className={getValueClass()} 
        >
          {displayValue}
        </span>
        {!isLast && <span className="vjs-comma">,</span>}
      </div>
    );
  }

  // 普通属性渲染
  if (isExpandable && isExpanded) {
    return (
      <div className="vjs-tree-node">
        <div className="vjs-indent">
          {renderIndent()}
        </div>
        {renderIcon()}
        <span 
          className="vjs-key" 
          draggable={true}
          {...createDragHandlers(currentPath)}
        >
          "{nodeKey}"
        </span>
        <span className="vjs-colon">: </span>
        <span className="vjs-bracket">
          {Array.isArray(value) ? '[' : '{'}
        </span>
      </div>
    );
  }
  
  if (isExpandable) {
    return (
      <div className="vjs-tree-node">
        <div className="vjs-indent">
          {renderIndent()}
        </div>
        {renderIcon()}
        <span 
          className="vjs-key" 
          draggable={true}
          {...createDragHandlers(currentPath)}
        >
          "{nodeKey}"
        </span>
        <span className="vjs-colon">: </span>
        <span 
          className="vjs-expandable" 
          onClick={onToggle}
          draggable={true}
          {...createDragHandlers(currentPath)}
        >
          {displayValue}
        </span>
        {!isLast && <span className="vjs-comma">,</span>}
      </div>
    );
  }

  // 叶子节点
  return (
    <div className="vjs-tree-node">
      <div className="vjs-indent">
        {renderIndent()}
      </div>
      {renderIcon()}
      <span 
        className="vjs-key" 
        draggable={true}
        {...createDragHandlers(currentPath)}
      >
        "{nodeKey}"
      </span>
      <span className="vjs-colon">: </span>
      <span 
        className={getValueClass()}
        onClick={() => {
          navigator.clipboard.writeText(displayValue);
        }}
      >
        {displayValue}
      </span>
      {!isLast && <span className="vjs-comma">,</span>}
    </div>
  );
});

interface JsonTreeProps {
  data: any;
  nodeId?: any;
  initialExpandDepth?: number;
}

// JSON树主组件
export const JsonTree = React.memo<JsonTreeProps>(({ 
  data, 
  nodeId, 
  initialExpandDepth = 2 
}) => {
  const getInitialExpandedKeys = useCallback(() => {
    const keys = new Set<string>();
    const addKeys = (obj: any, path: string, depth: number) => {
      if (depth > initialExpandDepth || typeof obj !== 'object' || obj === null) return;
      
      keys.add(path);
      
      if (Array.isArray(obj)) {
        obj.forEach((item, index) => {
          if (typeof item === 'object' && item !== null) {
            addKeys(item, `${path}[${index}]`, depth + 1);
          }
        });
      } else {
        Object.keys(obj).forEach(key => {
          const newPath = path ? `${path}.${key}` : key;
          if (typeof obj[key] === 'object' && obj[key] !== null) {
            addKeys(obj[key], newPath, depth + 1);
          }
        });
      }
    };
    
    Object.keys(data).forEach(key => {
      if (typeof data[key] === 'object' && data[key] !== null) {
        addKeys(data[key], key, 1);
      }
    });
    
    return keys;
  }, [data, initialExpandDepth]);

  const [expandedKeys, setExpandedKeys] = useState<Set<string>>(() => getInitialExpandedKeys());

  const toggleExpand = useCallback((key: string) => {
    setExpandedKeys(prev => {
      const newSet = new Set(prev);
      if (newSet.has(key)) {
        newSet.delete(key);
      } else {
        newSet.add(key);
      }
      return newSet;
    });
  }, []);

  const renderNode = useCallback((
    key: string, 
    value: any, 
    level: number, 
    isLast: boolean, 
    parentPath: string, 
    isArrayItem: boolean = false
  ): React.ReactNode => {
    const currentPath = parentPath ? `${parentPath}.${key}` : key;
    const nodeKey = isArrayItem ? `${parentPath}[${key}]` : currentPath;
    const isExpanded = expandedKeys.has(nodeKey);
    const isExpandable = typeof value === 'object' && value !== null;

    const node = (
      <TreeNode
        key={nodeKey}
        nodeKey={key}
        value={value}
        level={level}
        isLast={isLast && (!isExpandable || !isExpanded)}
        parentPath={parentPath}
        isArrayItem={isArrayItem}
        onToggle={() => toggleExpand(nodeKey)}
        isExpanded={isExpanded}
        nodeId={nodeId}
      />
    );

    if (isExpandable && isExpanded) {
      const children = Array.isArray(value) 
        ? value.map((item, index) => 
            renderNode(index.toString(), item, level + 1, index === value.length - 1, nodeKey, true)
          )
        : Object.keys(value).map((k, index, arr) => 
            renderNode(k, value[k], level + 1, index === arr.length - 1, nodeKey, false)
          );

      const closingBracket = (
        <div key={`${nodeKey}-close`} className="vjs-tree-node">
          <div className="vjs-indent">
            {Array.from({ length: level }, (_, i) => (
              <span key={i} className="vjs-indent-line" />
            ))}
          </div>
          <span className="vjs-icon vjs-icon-leaf">•</span>
          <span className="vjs-bracket">
            {Array.isArray(value) ? ']' : '}'}
          </span>
          {!isLast && <span className="vjs-comma">,</span>}
        </div>
      );

      return (
        <React.Fragment key={nodeKey}>
          {node}
          {children}
          {closingBracket}
        </React.Fragment>
      );
    }

    return node;
  }, [expandedKeys, toggleExpand, nodeId]);

  return (
    <JsonTreeContainer>
      <div className="json-tree">
        {Object.keys(data).map((key, index, arr) => 
          renderNode(key, data[key], 0, index === arr.length - 1, '', false)
        )}
      </div>
    </JsonTreeContainer>
  );
}); 