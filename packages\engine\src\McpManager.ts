import { McpServerConfig } from "@/McpInterfaces";


class McpManager {
    readonly #servers : Map<string, McpServerConfig>;
    #version : number = 0;

    constructor() {
        this.#servers = new Map();
        this.#version = Date.now();
    }

    get servers() : McpServerConfig[] {
        return Array.from(this.#servers.values());
    }

    get version() : number {
        return this.#version;
    }

    add (server : McpServerConfig) {
        this.#servers.set(server.id, server);
        this.#version = Date.now();
        return this;
    }

    update(server: McpServerConfig) {
        this.#servers.set(server.id, server);
        this.#version = Date.now();
        return this;
    }

    remove(id: string) {
        if(this.#servers.has(id)) {
            this.#version = Date.now();
            this.#servers.delete(id);
        }
        return this;
    }

    get(id : string) {
        return this.#servers.get(id);
    }
}

export const mcpManager = new McpManager();