import { JSONPath } from '@astronautlabs/jsonpath'
import { AiAgentOptions } from "../types";

export default async function siliconflow(opts : AiAgentOptions) {
    const apiUrl = opts.baseUrl??"https://api.siliconflow.cn/v1/chat/completions";
    const body = {
        model: opts.modelTag,
        messages: [{ role: 'user', content: opts.systemMessage + "\r\n" + JSON.stringify(opts.contents) }],
    }
    const inputs = {
        method: 'POST',
        headers: {Authorization: `Bearer ${opts.apiKey}`, 'Content-Type': 'application/json'},
        body: JSON.stringify(body)
    };

    const resp = await fetch(apiUrl, inputs);
    const data = await resp.json();
    return JSONPath.query(data, "$..content")[0];
}