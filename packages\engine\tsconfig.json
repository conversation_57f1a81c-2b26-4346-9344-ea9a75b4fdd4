{"extends": "../typescript-config/base.json", "compilerOptions": {"outDir": "dist", "strict": true, "noImplicitAny": true, "strictNullChecks": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "moduleResolution": "NodeNext", "target": "ES6", "baseUrl": ".", "paths": {"@/*": ["./src/*", "./app/*"], "@repo/*": ["../packages/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}