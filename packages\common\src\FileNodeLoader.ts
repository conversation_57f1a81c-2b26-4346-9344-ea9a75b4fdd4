// 节点加载器，使用fast-glob来动态加载所有节点
import 'reflect-metadata';
import { Container } from 'inversify';
import glob from 'fast-glob';
import path from 'path';
import { createRequire } from 'module';
import { INode, AbstractNodeLoader, NodeRegistry, Symbols } from './index';

// 创建一个指向项目根目录的 require 函数
const projectRequire = createRequire(process.cwd() + '/index.js');

//const projectRequire = createRequire(process.cwd() + '/package.json');

// 扩展 NodeJS 的 Module 类型
declare module 'module' {
    interface Module {
        _nodeModulePaths: (from: string) => string[];
        _resolveFilename: (request: string, parent: any, isMain: boolean, options: any) => string;
    }
}

/**
 * 具体的文件系统节点加载器实现
 * 专门用于从 node-set 包中加载节点
 */
export class FileNodeLoader extends AbstractNodeLoader {
    private container: Container;
    private nodeSetRoot: string;

    constructor() {
        // 创建依赖注入容器和 NodeRegistry
        const container = new Container();
        container.bind<NodeRegistry>(Symbols.NodeRegistry).to(NodeRegistry).inSingletonScope();
        const nodeRegistry = container.get<NodeRegistry>(Symbols.NodeRegistry);

        super(nodeRegistry);
        this.container = container;

        // 设置 node-set 包的根目录
        this.nodeSetRoot = this.resolveNodeSetRoot();
    }

    private resolveNodeSetRoot(): string {
        try {
            console.log('Starting to resolve package path...');
            console.log('Current working directory:', process.cwd());

            // 使用 __dirname 来定位当前文件位置
            const currentDir = __dirname;
            console.log('Current directory:', currentDir);

            // 由于编译后文件在 dist/container 目录下，需要向上查找三层到 node-set 根目录
            const nodeSetRoot = path.resolve(currentDir, '../../..');
            console.log('Node set root:', nodeSetRoot);

            // 验证目录是否正确
            const fs = require('fs');
            const pkgJsonPath = path.join(nodeSetRoot, 'package.json');
            console.log('Looking for package.json at:', pkgJsonPath);

            if (!fs.existsSync(pkgJsonPath)) {
                // 如果没找到，尝试其他可能的位置
                const possibleRoots = [
                    path.resolve(process.cwd(), 'packages/node-set'),
                    path.resolve(process.cwd(), '../node-set'),
                    path.resolve(process.cwd(), '../../packages/node-set')
                ];

                for (const root of possibleRoots) {
                    console.log('Trying alternative path:', root);
                    if (fs.existsSync(path.join(root, 'package.json'))) {
                        console.log('Found package.json in alternative location:', root);
                        return root;
                    }
                }
                throw new Error('Could not find package.json in any of the expected locations');
            }

            return nodeSetRoot;
        } catch (e) {
            console.error('Failed to resolve package path:', e);
            const fallback = process.cwd();
            console.log('Falling back to current working directory:', fallback);
            return fallback;
        }
    }

    private setupModuleResolution(): void {
        try {
            const Module = eval('require')('module'); // 使用 eval 来避免 Next.js 打包时的模块替换
            const originalResolve = Module._resolveFilename;

            // 重写模块解析逻辑
            Module._resolveFilename = (request: string, parent: any, isMain: boolean, options: any): string => {
                if (request === '@repo/common') {
                    // 尝试从多个可能的位置解析 @repo/common
                    const possiblePaths = [
                        path.resolve(this.nodeSetRoot, '../../node_modules/@repo/common/dist/index.js'),
                        path.resolve(this.nodeSetRoot, '../../packages/common/dist/index.js'),
                        path.resolve(process.cwd(), 'node_modules/@repo/common/dist/index.js'),
                        path.resolve(process.cwd(), 'packages/common/dist/index.js')
                    ];

                    for (const possiblePath of possiblePaths) {
                        try {
                            return projectRequire.resolve(possiblePath);
                        } catch (e) {
                            // 继续尝试下一个路径
                        }
                    }
                }

                // 对于其他模块，使用原始解析逻辑
                return originalResolve.call(this, request, parent, isMain, options);
            };
        } catch (error) {
            console.warn('Failed to setup module resolution, falling back to default behavior:', error);
            // 如果模块解析设置失败，就跳过这一步，使用默认的模块解析
        }
    }

    async loadNodes(): Promise<void> {
        try {
            this.setupModuleResolution();

            const nodesDir = path.join(this.nodeSetRoot, 'dist', 'nodes');
            console.log('Looking for nodes in:', nodesDir);

            // 检查目录是否存在
            const fs = require('fs');
            if (!fs.existsSync(nodesDir)) {
                console.error(`Nodes directory does not exist: ${nodesDir}`);
                // 创建目录
                fs.mkdirSync(nodesDir, { recursive: true });
            }

            // 检查源码目录
            const srcNodesDir = path.join(this.nodeSetRoot, 'src', 'nodes');
            console.log('Source nodes directory:', srcNodesDir);
            if (fs.existsSync(srcNodesDir)) {
                console.log('Source nodes directory contents:', fs.readdirSync(srcNodesDir));
            }

            // 检查编译后目录
            if (fs.existsSync(nodesDir)) {
                console.log('Compiled nodes directory contents:', fs.readdirSync(nodesDir));
            }

            const nodeFiles = await glob(['**/node.js'], {
                cwd: nodesDir,
                absolute: true,
                ignore: ['**/node_modules/**'],
            });

            console.log('Found node files:', nodeFiles);

            // 遍历并加载每个节点文件
            for (const filePath of nodeFiles) {
                await this.loadNodeFile(filePath);
            }
        } catch (error) {
            // 记录整体初始化失败
            if (error instanceof Error) {
                console.error('节点加载器初始化失败:', error.message);
            }
            throw error; // 重新抛出错误，因为这是致命错误
        }
    }

    private async loadNodeFile(filePath: string): Promise<void> {
        try {
            // 使用 require 动态加载节点模块
            const nodeModule = require(filePath);

            // 过滤出符合 INode 接口的导出类
            const exportedClasses = Object.values(nodeModule).filter(
                (exp): exp is new () => INode =>
                    typeof exp === 'function' &&
                    exp.prototype &&
                    this.isValidNodeClass(exp as new () => any)
            );

            // 将找到的节点类注册到容器中
            for (const NodeClass of exportedClasses) {
                await this.registerNodeClass(NodeClass, filePath);
            }
        } catch (error) {
            // 记录单个节点加载失败，但继续处理其他节点
            if (error instanceof Error) {
                console.error(`节点加载失败 (${filePath}):`, error.message);
            }
        }
    }

    private isValidNodeClass(NodeClass: new () => any): boolean {
        try {
            const instance = new NodeClass();
            return 'detail' in instance && 'node' in instance;
        } catch {
            return false;
        }
    }

    private async registerNodeClass(NodeClass: new () => INode, filePath: string): Promise<void> {
        try {
            const instance = new NodeClass();
            if (!instance.detail) {
                console.warn(`Skipping node class ${NodeClass.name}: missing required 'detail' property`);
                return;
            }

            this.container.bind<INode>(Symbols.NodeType).to(NodeClass);
            // 使用 nodeRegistry 直接注册，而不是调用 this.registerNode
            this.nodeRegistry.registerNode(instance);
            console.log(`✅ Successfully registered node: ${instance.detail}`);
        } catch (error) {
            console.error(`❌ Failed to register node class ${NodeClass.name || 'unknown'} from ${filePath}:`, error);
        }
    }
}
