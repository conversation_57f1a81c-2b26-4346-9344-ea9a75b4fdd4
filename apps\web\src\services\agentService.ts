/**
 * 智能体数据类型
 */
export interface AgentData {
  id?: string;
  name: string;
  description: string;
  prompt?: string | null; // 支持 null 类型
  avatar?: string | null; // 智能体头像，支持 null 类型
  modelId: string; // 选择的具体模型ID，必填
  modelName?: string | null; // 选择的模型名称，用于显示
  mcpIds?: string[];
  // 添加 MCP 工具信息
  mcpTools?: Array<{
    id: string;
    name: string;
    type: string;
  }>;
  connectid: string;
  // 关联的连接配置信息
  connectConfig?: {
    id: string;
    name: string;
    ctype: string;
    mtype?: string | null;
    configinfo: string;
    createdtime: Date;
    updatedtime: Date;
    creator?: string | null;
  };
  toolmode?: string; // 工具模式字段
  createUser?: string;
}

/**
 * 保存智能体的请求数据
 */
export interface SaveAgentRequest {
  name: string;
  description: string;
  prompt?: string;
  avatar?: string; // 智能体头像
  modelId: string; // 选择的具体模型ID，必填
  modelName?: string; // 选择的模型名称，用于显示
  connectId: string; // 连接配置ID（原来的modelId重命名为connectId）
  mcpIds?: string[];
  toolmode?: string; // 工具模式
  createUser?: string;
}

/**
 * 智能体响应数据
 */
export interface AgentResponse {
  success: boolean;
  data?: AgentData;
  error?: string;
  message?: string;
}

/**
 * 智能体列表响应数据
 */
export interface AgentListResponse {
  success: boolean;
  data: AgentData[];
  total: number;
  error?: string;
}

/**
 * 智能体服务类 - 通过HTTP API调用
 */
export class AgentService {
  
  /**
   * 保存智能体配置
   */
  static async saveAgent(request: SaveAgentRequest): Promise<AgentResponse> {
    console.log('🔧 AgentService.saveAgent 开始执行');
    console.log('📥 接收到的请求数据:', request);
    
    try {
      const response = await fetch('/api/agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      const result = await response.json();
      
      if (!response.ok) {
        return {
          success: false,
          error: result.error || '保存智能体失败'
        };
      }

      return result;

    } catch (error) {
      console.error('❌ 保存智能体失败:', error);
      return {
        success: false,
        error: '保存智能体失败',
        message: error instanceof Error ? error.message : '网络错误'
      };
    }
  }

  /**
   * 获取智能体列表
   */
  static async getAgents(filter?: {
    createUser?: string;
  }): Promise<AgentListResponse> {
    try {
      const searchParams = new URLSearchParams();
      if (filter?.createUser) {
        searchParams.append('createUser', filter.createUser);
      }

      const url = `/api/agents${searchParams.toString() ? '?' + searchParams.toString() : ''}`;
      const response = await fetch(url);

      const result = await response.json();
      
      if (!response.ok) {
        return {
          success: false,
          data: [],
          total: 0,
          error: result.error || '获取智能体列表失败'
        };
      }

      return result;

    } catch (error) {
      console.error('获取智能体列表失败:', error);
      return {
        success: false,
        data: [],
        total: 0,
        error: error instanceof Error ? error.message : '网络错误'
      };
    }
  }

  /**
   * 获取单个智能体
   */
  static async getAgent(id: string): Promise<AgentResponse> {
    try {
      const response = await fetch(`/api/agents/${id}`);
      const result = await response.json();
      
      if (!response.ok) {
        return {
          success: false,
          error: result.error || '获取智能体失败'
        };
      }

      return result;

    } catch (error) {
      console.error('获取智能体失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '网络错误'
      };
    }
  }

  /**
   * 更新智能体
   */
  static async updateAgent(id: string, request: Partial<SaveAgentRequest>): Promise<AgentResponse> {
    try {
      const response = await fetch(`/api/agents/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      const result = await response.json();
      
      if (!response.ok) {
        return {
          success: false,
          error: result.error || '更新智能体失败',
          message: result.message
        };
      }

      return result;

    } catch (error) {
      console.error('更新智能体失败:', error);
      return {
        success: false,
        error: '更新智能体失败',
        message: error instanceof Error ? error.message : '网络错误'
      };
    }
  }

  /**
   * 删除智能体
   */
  static async deleteAgent(id: string): Promise<AgentResponse> {
    try {
      const response = await fetch(`/api/agents/${id}`, {
        method: 'DELETE',
      });

      const result = await response.json();
      
      if (!response.ok) {
        return {
          success: false,
          error: result.error || '删除智能体失败',
          message: result.message
        };
      }

      return result;

    } catch (error) {
      console.error('删除智能体失败:', error);
      return {
        success: false,
        error: '删除智能体失败',
        message: error instanceof Error ? error.message : '网络错误'
      };
    }
  }
}