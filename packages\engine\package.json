{"name": "@repo/engine", "version": "0.1.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./dist/*": {"types": "./dist/*.d.ts", "default": "./dist/*.js"}}, "scripts": {"lint": "eslint . --max-warnings 0", "generate:component": "turbo gen react-component", "check-types": "tsc --noEmit", "clean": "powershell -Command \"Remove-Item -Path '.turbo', 'node_modules' -Recurse -Force -ErrorAction SilentlyContinue\"", "build": "tsup"}, "devDependencies": {"@dmitryrechkin/json-schema-to-zod": "^1.0.0", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@turbo/gen": "^2.5.0", "@types/mustache": "^4.2.6", "@types/xxhashjs": "^0.2.4", "eslint": "^9.25.0", "tsup": "^8.0.2", "typescript": "5.8.2"}, "dependencies": {"@astronautlabs/jsonpath": "^1.1.2", "@inngest/realtime": "^0.3.1", "@modelcontextprotocol/sdk": "^1.11.2", "@repo/common": "workspace:*", "@sinclair/typebox": "^0.34.33", "@smithery/sdk": "^1.5.2", "graphology": "^0.26.0", "graphology-dag": "^0.4.1", "inngest": "^3.37.0", "mustache": "^4.2.0", "xxhashjs": "^0.2.2", "zod": "^3.23.8", "zod-to-json-schema": "^3.24.3"}}