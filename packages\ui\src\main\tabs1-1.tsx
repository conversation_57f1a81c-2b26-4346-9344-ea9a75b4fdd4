// "use client";

// import styled from 'styled-components';

// export const TabsContainer = styled.div`
//   display: flex;
//   background-color: ${({ theme }) => theme.colors.secondary};
//   border-bottom: 1px solid ${({ theme }) => theme.colors.border};
// `;

// export const Tab = styled.div<{ $active?: boolean }>`
//   padding: 15px 20px;
//   cursor: pointer;
//   color: ${props => props.$active ? props.theme.colors.accent : props.theme.colors.textSecondary};
//   border-bottom: ${props => props.$active ? `2px solid ${props.theme.colors.accent}` : '2px solid transparent'};
//   font-size: 14px;

//   &:hover {
//     color: ${({ theme }) => theme.colors.accent};
//   }
// `;

// Custom styled TabsContainer to center the tabs
// const CenteredTabsContainer = styled.div`
//   display: flex;
//   justify-content: center;
//   background-color: white;
//   border-bottom: 1px solid #e0e0e0;
// `;

// export const TabsContainer = styled.div`
//   display: flex;
//   background-color: white;
//   border-bottom: 1px solid #e0e0e0;
// `;

// export const Tab = styled.div<{ $active?: boolean }>`
//   position: absulute;
//   top:20px;
//   left:50%;
//   padding: 15px 20px;
//   cursor: pointer;
//   color: ${props => props.$active ? '#33C2EE' : '#666'};
//   // border-bottom: ${props => props.$active ? '2px solid #33C2EE' : '2px solid transparent'};
//   font-size: 14px;

//   &:hover {
//     color: #33C2EE;
//   }
// `;

// export const TabsContainer = styled.div`
//   position: absolute;
//   left: 50%;
//   top:31px;
//   transform: translateY(-50%);
//   z-index: 10;
//   font-family: 'PingFang SC', 'Microsoft YaHei', 'SimSun', sans-serif;
//   cursor: pointer;
//   user-select: none;
//   display: flex;
//   flex-direction: row
//   align-items: center;
//   justify-content: center;
//   width: 200px;
//   height: 20px;  
//   gap: 8px;
// `

// export const Tab = styled.div<{ $active?: boolean }>`
//   position: relative;
//   padding: 20px 20px;
//   cursor: pointer;

//   color: ${props => props.$active ? '#33C2EE' : '#666'};
//   border-bottom: ${props => props.$active ? '2px solid #33C2EE' : '2px solid transparent'};
//   font-size: 14px;
//   &:hover {
//     color: #33C2EE;
//   }
// `;