import { defineConfig } from 'tsup';

export default defineConfig({
  entry: {
    index: 'src/index.ts',
    // 数据库连接器集合
    'connects/database/index': 'src/connects/database/index.ts',
    // LLM连接器集合
    'connects/llm/index': 'src/connects/llm/index.ts',
    // LLM测试工具
    'connects/llm/utils/llm-tester': 'src/connects/llm/utils/llm-tester.ts',
    // 数据库测试工具
    'connects/database/utils/database-tester': 'src/connects/database/utils/database-tester.ts',
    // 分别构建每个连接器
    'connects/mysql': 'src/connects/database/mysql/mysql.connect.ts',
    'connects/postgresql': 'src/connects/database/postgresql/postgresql.connect.ts',
    'connects/rest-api': 'src/connects/http/rest-api/rest-api.connect.ts',
    'connects/openai': 'src/connects/llm/openai/openai.connect.ts',
    'connects/siliconflow': 'src/connects/llm/siliconFlow/siliconflow.connect.ts'
  },
  format: ['cjs', 'esm'],
  dts: true,
  splitting: false,
  sourcemap: true,
  clean: true,
  external: [
    '@repo/common',
    // Node.js 内置模块
    'fs',
    'path',
    'os',
    'module',
    'url',
    'util',
    'crypto',
    'stream',
    'events',
    'child_process',
    'readline',
    // 第三方 Node.js 模块
    'fast-glob',
    'inversify',
    'reflect-metadata',
    // glob 相关依赖
    '@nodelib/fs.scandir',
    '@nodelib/fs.stat',
    '@nodelib/fs.walk',
    'micromatch',
    'glob-parent',
    'merge2',
    'picomatch'
  ],
  platform: 'browser', // 明确指定为浏览器平台
  target: 'es2020',
  outDir: 'dist',
  // 保持目录结构
  outExtension({ format }) {
    return {
      js: format === 'cjs' ? '.js' : '.mjs'
    }
  },
  // 添加 NoEmitOnError 选项
  noExternal: [],
  // 完全排除某些包
  treeshake: true
}); 