import React, { useState, useRef, useEffect } from 'react';
import { IConnectCategory } from '@repo/common';

// 导入UI组件样式
import {
  GlassContainer,
  GlassMain,
  GlassHeader,
  GlassDescription,
  GlassTabNav,
  GlassTab,
  GlassDescInfo,
} from '../home/<USER>';

import {
  HeaderContainer,
  TitleContainer,
  WelcomeContainer,
  WelcomeContent,
  IconContainer,
  WelcomeTitle,
  PlaceholderContainer,
}
  from '../shared/styles/welcome';
import { SiGitconnected } from "react-icons/si";
import { CoButton } from '../../components/basic/Buttons';

import { ConnectConfigModal } from '../../components/modals/ConnectConfigModal';
import { ConnectList } from './ConnectList';

// interface Category {
//   id: string;
//   name: string;
//   description: string;
//   type: string;
// }

interface ConnectConfig {
  id: string;
  name: string;
  ctype: string;
  mtype: string; // 数据库中实际的类型字段
  configinfo: string;
  createdtime: Date;
  updatedtime: Date;
  creator: string | null;
}

interface ConnectPageProps {
  title: string;
  slogan: string;
  DocumentIcon: React.ComponentType;
  loading: boolean;
  categories?: IConnectCategory[];
  connectConfigs?: ConnectConfig[];
  onConnectSave?: (data: any) => Promise<boolean>;
  onFetchConnects?: () => Promise<any[]>;
  onFetchConnectDetails?: (connectId: string) => Promise<any>;
  onFetchConnectConfigs?: () => Promise<ConnectConfig[]>;
  onDeleteConnect?: (connectId: string) => Promise<boolean>;
  onEditConnect?: (connect: ConnectConfig) => void;
  onTest?: (config: Record<string, any>, message?: string) => Promise<any>;
  onStreamTest?: (config: Record<string, any>, message: string, onChunk: (chunk: string) => void) => Promise<any>;
}

export const ConnectPage: React.FC<ConnectPageProps> = ({
  title,
  slogan,
  DocumentIcon,
  loading,
  categories = [],
  connectConfigs = [],
  onConnectSave,
  onFetchConnects,
  onFetchConnectDetails,
  onFetchConnectConfigs,
  onDeleteConnect,
  onEditConnect,
  onTest,
  onStreamTest
}) => {
  const [activeTab, setActiveTab] = useState('all');

  const [isConnectModalOpen, setIsConnectModalOpen] = useState(false);
  const [editingConnect, setEditingConnect] = useState<ConnectConfig | null>(null);

  const handleCreateConnect = () => {

    setEditingConnect(null);
    setIsConnectModalOpen(true);
  };

  const handleConnectModalClose = () => {
    setIsConnectModalOpen(false);
    setEditingConnect(null);
  };

  const handleConnectClick = (connectId: string) => {
    // 可以实现连接详情查看逻辑
    console.log('连接配置点击:', connectId);
  };

  const handleDeleteConnect = async (connectId: string) => {
    if (onDeleteConnect) {
      try {
        const success = await onDeleteConnect(connectId);
        if (success) {
          // 刷新列表
          if (onFetchConnectConfigs) {
            await onFetchConnectConfigs();
          }
        }
      } catch (error) {
        console.error('删除连接配置失败:', error);
      }
    }
  };

  const handleEditConnectFromList = (connect: ConnectConfig) => {
    setEditingConnect(connect);
    setIsConnectModalOpen(true);
    if (onEditConnect) {
      onEditConnect(connect);
    }
  };

  const handleDebugConnect = (connect: ConnectConfig) => {
    console.log('调试连接配置:', connect);
    // 这里可以实现调试功能，比如测试连接、查看配置详情等
    alert(`正在调试连接配置: ${connect.name}\n类型: ${connect.ctype}\nID: ${connect.id}`);
  };

  const handleConnectSave = async (data: any) => {

    if (onConnectSave) {
      try {
        const success = await onConnectSave(data);

        if (success) {
          alert('连接配置保存成功！');
          setIsConnectModalOpen(false);
        } else {
          alert('保存失败，请重试');
        }
      } catch (error) {
        alert(`保存失败: ${error instanceof Error ? error.message : '保存过程中发生错误'}`);
      }
    } else {
      setIsConnectModalOpen(false);
    }
  };

  return (
    <GlassContainer>
      {/* <LiquidBackground /> */}
      <GlassMain>
        <GlassHeader>
          <GlassDescription>
            <HeaderContainer>
              <TitleContainer>
                <h3>{title}</h3>
              </TitleContainer>
              <CoButton variant='liquid' onClick={handleCreateConnect}>
                <SiGitconnected />
                <span> 创建新的连接 </span>
              </CoButton>
            </HeaderContainer>
            <GlassDescInfo>
              {slogan}
            </GlassDescInfo>
          </GlassDescription>

          <GlassTabNav>
            <GlassTab $active={activeTab === 'all'} onClick={() => setActiveTab('all')}>
              全部
            </GlassTab>
            {categories.map((category) => (
              <GlassTab
                key={category.type}
                $active={activeTab === category.type}
                onClick={() => setActiveTab(category.type)}
              >
                {category.name}
              </GlassTab>
            ))}
          </GlassTabNav>
        </GlassHeader>
        {(() => {
          if (connectConfigs.length === 0) return (
            <WelcomeContainer>
              <WelcomeContent>
                <IconContainer>
                  <DocumentIcon />
                </IconContainer>
                <WelcomeTitle>
                  还没有任何连接配置
                  <p>点击上方的"创建连接"按钮开始配置你的第一个连接</p>
                </WelcomeTitle>
                <CoButton onClick={handleCreateConnect}>
                  快速开始创建连接
                </CoButton>
                <PlaceholderContainer />
              </WelcomeContent>
            </WelcomeContainer>
          );
          else return (
            <div style={{ flex: 1, display: 'flex', flexDirection: 'column', minHeight: 0, marginTop: '4px' }}>
              <ConnectList
                connects={connectConfigs}
                activeTab={activeTab}
                categories={categories}
                onConnectClick={handleConnectClick}
                onDeleteConnect={handleDeleteConnect}
                onEditConnect={handleEditConnectFromList}
                onDebugConnect={handleDebugConnect}
              />
            </div>
          );
        })()}
      </GlassMain>

      {/* 连接配置弹窗 */}
      <ConnectConfigModal
        isOpen={isConnectModalOpen}
        onClose={handleConnectModalClose}
        onSave={handleConnectSave}
        onTest={onTest}
        onStreamTest={onStreamTest}
        onFetchConnects={onFetchConnects}
        onFetchConnectDetails={onFetchConnectDetails}
        categories={categories}
        editMode={!!editingConnect}
        editData={editingConnect}
      />
    </GlassContainer>
  )
}; 