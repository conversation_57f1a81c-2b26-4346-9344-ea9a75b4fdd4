import { Ollama } from 'ollama';
import { AiAgentOptions } from "../types";

export default async function ollama(opts : AiAgentOptions) {
    const ollama = new Ollama({
        host: opts.baseUrl??'http://localhost:11434'
    });
    const response = await ollama.chat({
        model: opts.modelTag,
        messages: [{ role: 'user', content: opts.systemMessage + "\r\n" + JSON.stringify(opts.contents) }],
    })
    return response.message.content;
}