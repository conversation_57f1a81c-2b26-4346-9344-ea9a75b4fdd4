import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@repo/db';

/**
 * 获取智能体列表
 * GET /api/agents
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const createUser = searchParams.get('createUser');
    const excludeFields = searchParams.get('excludeFields')?.split(',') || [];
    
    const where: any = {};
    if (createUser) {
      where.createUser = createUser;
    }
    
    // 根据excludeFields动态构建查询
    const includePrompt = !excludeFields.includes('prompt');
    
    // 获取智能体基本信息，包含连接配置
    const agents = await prisma.aiAgent.findMany({
      where,
      select: {
        id: true,
        name: true,
        description: true,
        avatar: true,
        modelId: true,
        modelName: true,
        connectid: true,
        toolmode: true,
        createUser: true,
        createdAt: true,
        updatedAt: true,
        ...(includePrompt && { prompt: true }),
        connectConfig: true // 包含连接配置信息
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // 获取每个智能体的 MCP 关联，包含 MCP 名称信息
    const agentData = await Promise.all(agents.map(async (agent) => {
      const mcpRelations = await prisma.agentMcp.findMany({
        where: { agentId: agent.id },
        include: {
          mcp: {
            select: {
              id: true,
              name: true,
              type: true
            }
          }
        }
      });

      const baseData = {
        id: agent.id,
        name: agent.name,
        description: agent.description,
        avatar: agent.avatar,
        modelId: agent.modelId || agent.connectid, // 临时兼容处理
        modelName: agent.modelName || '未选择模型',
        connectid: agent.connectid,
        connectConfig: agent.connectConfig, // 添加连接配置信息
        toolmode: agent.toolmode || 'function', // 添加工具模式字段
        mcpIds: mcpRelations.map(rel => rel.mcpId),
        // 添加 MCP 工具信息
        mcpTools: mcpRelations.map(rel => ({
          id: rel.mcp.id,
          name: rel.mcp.name,
          type: rel.mcp.type
        })),
        createUser: agent.createUser
      };

      // 只有查询了prompt字段才包含
      if (includePrompt && 'prompt' in agent) {
        return { ...baseData, prompt: agent.prompt };
      }

      return baseData;
    }));

    return NextResponse.json({
      success: true,
      data: agentData,
      total: agentData.length
    });

  } catch (error) {
    console.error('🤖 GET /api/agents 异常:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取智能体失败'
    }, { status: 500 });
  }
}

/**
 * 创建智能体
 * POST /api/agents
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🤖 POST /api/agents 开始');
    
    const body = await request.json();
    console.log('🤖 接收到的请求体:', body);
    
    const { name, description, prompt, avatar, modelId, modelName, connectId, mcpIds, toolmode, createUser } = body;

    // 验证必填字段
    if (!name || !description || !modelId || !connectId) {
      const error = '缺少必填字段：name、description、modelId 或 connectId';
      console.error('❌ 验证失败:', error);
      return NextResponse.json({
        success: false,
        error
      }, { status: 400 });
    }

    console.log('✅ 字段验证通过');

    // 验证连接配置是否存在
    const connectConfig = await prisma.connectConfig.findUnique({
      where: { id: connectId }
    });

    if (!connectConfig) {
      return NextResponse.json({
        success: false,
        error: '指定的模型配置不存在'
      }, { status: 400 });
    }

    // 如果指定了MCP，验证所有MCP是否存在
    if (mcpIds && mcpIds.length > 0) {
      const mcpConfigs = await prisma.aiMcp.findMany({
        where: { id: { in: mcpIds } }
      });

      if (mcpConfigs.length !== mcpIds.length) {
        return NextResponse.json({
          success: false,
          error: '部分指定的MCP配置不存在'
        }, { status: 400 });
      }
    }

    console.log('💾 准备插入数据库...');

    // 使用事务保存智能体和MCP关联
    const result = await prisma.$transaction(async (tx) => {
      // 保存智能体
      const savedAgent = await tx.aiAgent.create({
        data: {
          name: name,
          description: description,
          prompt: prompt,
          avatar: avatar,
          modelId: modelId,
          modelName: modelName,
          connectid: connectId,
          toolmode: toolmode || 'function',
          createUser: createUser || 'system'
        }
      });

      // 如果有MCP，创建关联关系
      if (mcpIds && mcpIds.length > 0) {
        await tx.agentMcp.createMany({
          data: mcpIds.map((mcpId: string) => ({
            agentId: savedAgent.id,
            mcpId: mcpId
          }))
        });
      }

      return savedAgent;
    });

    console.log('✅ 智能体保存成功:', result.id);

    return NextResponse.json({
      success: true,
      data: {
        id: result.id,
        name: result.name,
        description: result.description,
        prompt: result.prompt,
        avatar: result.avatar,
        modelId: result.modelId,
        modelName: result.modelName,
        connectid: result.connectid,
        toolmode: result.toolmode || undefined,
        mcpIds: mcpIds || [],
        createUser: result.createUser
      },
      message: '智能体配置保存成功'
    });

  } catch (error) {
    console.error('🤖 POST /api/agents 异常:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '创建智能体失败'
    }, { status: 500 });
  }
}

/**
 * 删除智能体
 * DELETE /api/agents
 */
export async function DELETE(request: NextRequest) {
  try {
    console.log('🤖 DELETE /api/agents 开始');
    
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({
        success: false,
        error: '缺少智能体 ID'
      }, { status: 400 });
    }
    
    console.log('🤖 要删除的智能体 ID:', id);
    
    // 检查智能体是否存在
    const existingAgent = await prisma.aiAgent.findUnique({
      where: { id }
    });

    if (!existingAgent) {
      return NextResponse.json({
        success: false,
        error: '智能体不存在'
      }, { status: 404 });
    }

    // 使用事务删除
    await prisma.$transaction(async (tx) => {
      // 删除 MCP 关联
      await tx.agentMcp.deleteMany({
        where: { agentId: id }
      });

      // 删除智能体
      await tx.aiAgent.delete({
        where: { id }
      });
    });

    return NextResponse.json({
      success: true,
      message: '智能体删除成功'
    });

  } catch (error) {
    console.error('🤖 DELETE /api/agents 异常:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '删除智能体失败'
    }, { status: 500 });
  }
}