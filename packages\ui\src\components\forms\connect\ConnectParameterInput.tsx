"use client";

import React from 'react';
import { UnifiedParameterInput, type UnifiedParameterField } from '../unified/UnifiedParameterInput';

interface ConnectParameterInputProps {
  field: {
    name: string;
    displayName: string;
    type: string;
    controlType?: string;
    default?: any;
    options?: { name?: string; value: any }[];
    description?: string;
    hint?: string;
    placeholder?: string;
    required?: boolean;
    isSecure?: boolean;
    typeOptions?: any;
    displayOptions?: {
      show?: {
        [key: string]: string[];
      };
    };
  };
  value: any;
  onChange: (name: string, value: any) => void;
  formValues?: Record<string, any>;
}

export const ConnectParameterInput: React.FC<ConnectParameterInputProps> = ({
  field,
  value,
  onChange,
  formValues
}) => {
  // 将原始 field 转换为统一的 field 格式，补充缺失的属性
  const unifiedField: UnifiedParameterField = {
    name: field.name,
    displayName: field.displayName,
    type: field.type,
    controlType: field.controlType,
    default: field.default,
    options: field.options?.map(opt => ({
      name: opt.name,
      value: opt.value,
      description: undefined // 原始接口不支持 description
    })),
    description: field.description,
    hint: field.hint,
    placeholder: field.placeholder,
    required: field.required,
    isSecure: field.isSecure,
    typeOptions: {
      password: field.typeOptions?.password,
      minValue: field.typeOptions?.minValue,
      maxValue: field.typeOptions?.maxValue,
      numberPrecision: field.typeOptions?.numberPrecision
    },
    displayOptions: field.displayOptions
  };

  return (
    <UnifiedParameterInput
      variant="connect"
      field={unifiedField}
      value={value}
      onChange={onChange}
      formValues={formValues}
      onExpandModeChange={undefined} // Connect变体不支持展开模式
    />
  );
}; 