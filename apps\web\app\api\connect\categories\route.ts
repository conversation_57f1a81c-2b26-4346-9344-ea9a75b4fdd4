import { NextRequest, NextResponse } from 'next/server';
import { ALL_CATEGORIES } from '@repo/connect-set';

/**
 * GET /api/connect/categories
 * 获取所有连接分类
 */
export async function GET(request: NextRequest) {
  try {
    // 格式化分类数据，去除重复项并按类型分组
    const categoriesMap = new Map();
    
    ALL_CATEGORIES.forEach(category => {
      const key = category.type;
      if (!categoriesMap.has(key)) {
        categoriesMap.set(key, {
          id: category.id,
          name: category.name,
          description: category.description,
          type: category.type
        });
      }
    });
    
    const uniqueCategories = Array.from(categoriesMap.values());
    
    return NextResponse.json({
      success: true,
      data: uniqueCategories,
      total: uniqueCategories.length
    });
  } catch (error) {
    console.error('获取连接分类失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '获取连接分类失败',
        message: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
} 