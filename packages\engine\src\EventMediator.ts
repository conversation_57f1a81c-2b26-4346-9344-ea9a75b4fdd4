import { inngest } from "@/index";

export interface EventResult {
    eventId: string;
    runData: any[];
}

export class EventMediator {
    static async getEventRuns(eventId: string) {

        const apiUrl = `http://127.0.0.1:8288/v1/events/${eventId}/runs?date=${Date.now()}`;
        const inputs = {
            headers: {
                //Authorization: `Bearer ${process.env.INNGEST_SIGNING_KEY}`,
                'Content-Type': 'application/json'
            },
        };
        const response = await fetch(apiUrl, inputs);
        const json = await response.json();
        return json.data;
    }

    static async getEventOutput(eventId: string) {

        let timeout = 0;
        let runs = null;
        while (true) {
            runs = await EventMediator.getEventRuns(eventId);

            if (runs.length == 0) {
                timeout++;
                if (timeout > 200) {
                    console.log(`Function run error: ${eventId}`);
                    break;
                }
            } else {
                if (runs[0].status === "Completed") {
                    break;
                } else if (runs[0].status === "Failed" || runs[0].status === "Cancelled") {
                    console.log(`Function run stoped: ${eventId}`);
                    break;
                }
            }

            await new Promise((resolve) => setTimeout(resolve, 100));
        }
        return runs[0]?.output || [];
    }

    static async sendEvent(triggerId: string, data?: any, waitOutput: boolean = false) {

        const result = await inngest.send({
            name: triggerId,
            data: data
        });

        const json = {
            eventId: result?.ids?.length > 0 ? result.ids[0] as string : undefined,
            runData: [] as any[]
        } as EventResult;

        if (json.eventId !== undefined && waitOutput) {
            try {
                const runData = await EventMediator.getEventOutput(json.eventId);
                json.runData.push(runData);
            } catch (outputError) {
                throw outputError;
            }
        }

        return json;
    }
}