/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  compiler: {
    styledComponents: true,
  },
  transpilePackages: ["@repo/ui", "@repo/db", "@repo/node-set", "@repo/engine"],
  eslint: {
    // 在生产构建时忽略 ESLint 错误
    ignoreDuringBuilds: process.env.NODE_ENV === 'production',
  },
  typescript: {
    // 在生产构建时忽略 TypeScript 错误
    ignoreBuildErrors: process.env.NODE_ENV === 'production',
  },
  images: {
    domains: [],
  },
  // 配置环境变量前缀
  env: {
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
  },
  experimental: {
    // Remove appDir as it's now stable in Next.js 15
  }
};

export default nextConfig;
