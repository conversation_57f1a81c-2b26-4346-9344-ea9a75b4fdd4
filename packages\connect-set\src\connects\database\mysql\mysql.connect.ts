import { IDatabaseConnect, ConnectTestResult } from '@repo/common';


/**
 * MySQL 数据库连接定义
 */
export const MySQLConnect: IDatabaseConnect = {
    overview: {
        id: 'mysql',
        name: 'MySQL',
        type: 'rd-db' as const,
        provider: 'mysql' as const,
        icon: 'mysql.svg',
        description: 'MySQL关系型数据库连接',
        version: '1.0.0'
    },

    detail: {
        defaultPort: 3306,
        supportedFeatures: [
            'transactions' as const,
            'stored_procedures' as const,
            'views' as const,
            'triggers' as const,
            'full_text_search' as const,
            'json_support' as const
        ],
        fields: [
            {
                displayName: '主机地址',
                name: 'host',
                type: 'string' as const,
                default: 'localhost',
                description: 'MySQL服务器的主机地址',
                placeholder: 'localhost 或 IP地址',
                required: true,
                controlType: "input"
            },
            {
                displayName: '数据库名',
                name: 'database',
                type: 'string' as const,
                default: '',
                description: '要连接的数据库名称',
                required: true,
                controlType: "input"
            },
            {
                displayName: '用户名',
                name: 'username',
                type: 'string' as const,
                default: '',
                placeholder: "请输入数据库用户名",
                description: '数据库用户名',
                required: true,
                controlType: "input"
            },
            {
                displayName: '密码',
                name: 'password',
                type: 'string' as const,
                default: '',
                description: '数据库密码',
                placeholder: "请输入数据库密码",
                typeOptions: {
                    password: true
                },
                isSecure: true,
                controlType: "input"
            },
            {
                displayName: '端口',
                name: 'port',
                type: 'number' as const,
                default: 3306,
                description: 'MySQL服务器端口号',
                typeOptions: {
                    minValue: 1,
                    maxValue: 65535
                },
                required: true,
                controlType: "input"
            },
            {
                displayName: '查询超时(秒)',
                name: 'queryTimeout',
                type: 'number' as const,
                default: 30,
                description: '查询超时时间，单位：秒',
                typeOptions: {
                    minValue: 1,
                    maxValue: 3600
                },
                controlType: "input"
            },
            {
                displayName: '启用SSL',
                name: 'ssl',
                type: 'boolean' as const,
                default: false,
                description: '是否启用SSL连接',
                controlType: "CheckBox"
            },
            {
                displayName: '字符集',
                name: 'charset',
                type: 'options' as const,
                default: 'utf8mb4',
                description: '数据库字符集',
                options: [
                    { name: 'UTF-8 (推荐)', value: 'utf8mb4' },
                    { name: 'UTF-8', value: 'utf8' },
                    { name: 'Latin1', value: 'latin1' },
                    { name: 'ASCII', value: 'ascii' }
                ],
                controlType: "select"
            },

            {
                displayName: '连接池大小',
                name: 'poolSize',
                type: 'number' as const,
                default: 10,
                description: '连接池最大连接数',
                typeOptions: {
                    minValue: 1,
                    maxValue: 100
                },
                controlType: "input"
            }
        ],
        validateConnection: true,
        connectionTimeout: 10000
    },

    /**
     * 测试MySQL连接
     */
    async test(config: Record<string, any>): Promise<ConnectTestResult> {
        const startTime = Date.now();
        try {
            // 验证必填字段
            const requiredFields = ['host', 'port', 'username', 'database'];
            for (const field of requiredFields) {
                if (!config[field]) {
                    return {
                        success: false,
                        message: `缺少必填字段: ${field}`
                    };
                }
            }

            // 尝试使用mysql2或mysql驱动
            let connectionResult;

            try {
                // 首先尝试使用推荐的mysql2驱动
                connectionResult = await testWithMysql2Driver(config);
            } catch (mysql2Error) {
                try {
                    // 如果mysql2不可用，尝试使用经典mysql驱动
                    connectionResult = await testWithMysqlDriver(config);
                } catch (mysqlError) {
                    // 如果两个驱动都不可用，返回详细错误信息
                    return {
                        success: false,
                        message: `MySQL连接失败。尝试的驱动程序都不可用:\n` +
                            `MySQL2驱动(mysql2): ${mysql2Error instanceof Error ? mysql2Error.message : String(mysql2Error)}\n` +
                            `MySQL经典驱动(mysql): ${mysqlError instanceof Error ? mysqlError.message : String(mysqlError)}`,
                        details: {
                            mysql2DriverError: mysql2Error instanceof Error ? mysql2Error.message : String(mysql2Error),
                            mysqlDriverError: mysqlError instanceof Error ? mysqlError.message : String(mysqlError),
                            suggestion: "请安装MySQL2驱动 'npm install mysql2' 或经典MySQL驱动 'npm install mysql'"
                        }
                    };
                }
            }

            const latency = Date.now() - startTime;

            return {
                success: true,
                message: `MySQL连接测试成功 (${connectionResult.driverUsed})`,
                latency,
                details: {
                    host: config.host,
                    port: config.port,
                    database: config.database,
                    charset: config.charset || 'utf8mb4',
                    ssl: config.ssl || false,
                    poolSize: config.poolSize || 10,
                    connectionTimeout: config.connectionTimeout || 10,
                    queryTimeout: config.queryTimeout || 30,
                    driverUsed: connectionResult.driverUsed,
                    serverVersion: connectionResult.serverVersion
                }
            };

        } catch (error) {
            return {
                success: false,
                message: `MySQL连接失败: ${error instanceof Error ? error.message : String(error)}`,
                latency: Date.now() - startTime
            };
        }
    }
};

/**
 * 使用mysql2驱动测试连接
 */
async function testWithMysql2Driver(config: Record<string, any>): Promise<{ driverUsed: string; serverVersion?: string }> {
    // 动态导入mysql2驱动
    let mysql: any;
    try {
        mysql = await import('mysql2/promise');
    } catch (error) {
        throw new Error(`MySQL2驱动(mysql2)未安装: ${error instanceof Error ? error.message : String(error)}`);
    }

    const connectionConfig = {
        host: config.host,
        port: parseInt(config.port),
        user: config.username,
        password: config.password,
        database: config.database,
        charset: config.charset || 'utf8mb4',
        ssl: config.ssl ? {} : false,
        connectTimeout: (config.connectionTimeout || 10) * 1000,
        acquireTimeout: (config.connectionTimeout || 10) * 1000,
        timeout: (config.queryTimeout || 30) * 1000,
        // 防止连接挂起
        socketPath: undefined
    };

    let connection: any = null;
    try {
        // 创建连接
        connection = await mysql.createConnection(connectionConfig);

        // 执行简单查询测试连接并获取版本信息
        const [rows] = await connection.execute('SELECT VERSION() as version');

        return {
            driverUsed: 'MySQL2驱动(mysql2)',
            serverVersion: rows[0]?.version || '未知版本'
        };
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

/**
 * 使用mysql经典驱动测试连接
 */
async function testWithMysqlDriver(config: Record<string, any>): Promise<{ driverUsed: string; serverVersion?: string }> {
    // 动态导入mysql驱动
    let mysql: any;
    try {
        mysql = await import('mysql');
    } catch (error) {
        throw new Error(`MySQL经典驱动(mysql)未安装: ${error instanceof Error ? error.message : String(error)}`);
    }

    const connectionConfig = {
        host: config.host,
        port: parseInt(config.port),
        user: config.username,
        password: config.password,
        database: config.database,
        charset: config.charset || 'utf8mb4',
        ssl: config.ssl ? {} : false,
        connectTimeout: (config.connectionTimeout || 10) * 1000,
        acquireTimeout: (config.connectionTimeout || 10) * 1000,
        timeout: (config.queryTimeout || 30) * 1000
    };

    return new Promise((resolve, reject) => {
        const connection = mysql.createConnection(connectionConfig);

        // 设置总超时时间
        const timeout = setTimeout(() => {
            connection.destroy();
            reject(new Error('连接超时'));
        }, (config.connectionTimeout || 10) * 1000);

        connection.connect((err: any) => {
            if (err) {
                clearTimeout(timeout);
                connection.destroy();
                reject(new Error(`MySQL经典驱动连接失败: ${err.message}`));
                return;
            }

            // 执行简单查询测试连接
            connection.query('SELECT VERSION() as version', (queryErr: any, results: any) => {
                clearTimeout(timeout);
                connection.end();

                if (queryErr) {
                    reject(new Error(`查询失败: ${queryErr.message}`));
                    return;
                }

                resolve({
                    driverUsed: 'MySQL经典驱动(mysql)',
                    serverVersion: results[0]?.version || '未知版本'
                });
            });
        });
    });
}


export default MySQLConnect; 