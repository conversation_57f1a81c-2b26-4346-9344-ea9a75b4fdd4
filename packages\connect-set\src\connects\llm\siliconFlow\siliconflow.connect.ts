import { ILLMConnect, ConnectTestResult, ILLMOverview } from '@repo/common';
import { LLMTester } from '../utils/llm-tester';
import {
    createApiKeyField,
    createBaseUrlField,
    testLLMConnection
} from '../utils/llm-fields';

const ConnectConfig: ILLMOverview = {
    id: 'siliconflow',
    name: '硅基流动',
    type: 'llm',
    provider: 'siliconflow',
    tags: ['domestic'],
    icon: 'siliconflow.svg',
    description: '硅基流动AI大模型连接',
    version: '1.0.0',
    api: { url: 'https://api.siliconflow.cn/v1', suffix: '/chat/completions' },
    driver: 'openai',
    about: {
        apiHost: 'https://api.siliconflow.cn',
        docUrl: 'https://docs.siliconflow.cn/',
        modelUrl: 'https://cloud.siliconflow.cn/models',
        getKeyUrl: 'https://account.siliconflow.cn/'
    }
};
/**
 * 硅基流动 LLM 连接定义
 */
export const SiliconFlowConnect: ILLMConnect = {
    overview: ConnectConfig,
    detail: {
        supportedModels: [
            { id: 'deepseek-ai/DeepSeek-R1', name: 'DeepSeek-R1', group: 'DeepSeek' },
            { id: 'deepseek-ai/DeepSeek-V3', name: 'DeepSeek-V3', group: 'DeepSeek' },
            { id: 'Qwen/Qwen2.5-72B-Instruct', name: 'Qwen2.5-72B-Instruct', group: 'Qwen' },
            { id: 'Qwen/Qwen2.5-32B-Instruct', name: 'Qwen2.5-32B-Instruct', group: 'Qwen' },
            { id: 'Qwen/Qwen2.5-14B-Instruct', name: 'Qwen2.5-14B-Instruct', group: 'Qwen' },
            { id: 'Qwen/Qwen2.5-7B-Instruct', name: 'Qwen2.5-7B-Instruct', group: 'Qwen' },
            { id: 'Qwen/Qwen2.5-3B-Instruct', name: 'Qwen2.5-3B-Instruct', group: 'Qwen' },
            { id: 'Qwen/Qwen2.5-1B-Instruct', name: 'Qwen2.5-1B-Instruct', group: 'Qwen' },
            { id: 'Qwen/Qwen2.5-0.5B-Instruct', name: 'Qwen2.5-0.5B-Instruct', group: 'Qwen' }
        ],
        // modelTypes: ['chat', 'completion'],
        // maxContextLength: 131072,
        // supportedFeatures: [
        //     'streaming',
        //     'function_calling',
        //     'system_message'
        // ],
        fields: [
            createApiKeyField(),
            createBaseUrlField(ConnectConfig.api.url),
            // {
            //     displayName: '模型类型',
            //     name: 'modelType',
            //     type: 'options',
            //     default: 'chat',
            //     description: '模型类型',
            //     options: [
            //         { name: '对话模型', value: 'chat' },
            //         { name: '补全模型', value: 'completion' }
            //     ],
            //     required: true,
            //     controlType: 'Select'
            // },
            // {
            //     displayName: '最大Token数',
            //     name: 'maxTokens',
            //     type: 'number',
            //     default: 800,
            //     description: '生成内容的最大Token数（建议500-1000，过多会影响响应速度）',
            //     typeOptions: {
            //         minValue: 1,
            //         maxValue: 4096
            //     },
            //     //只有当 modelType 字段的值在 ['chat', 'completion'] 数组中时，相关字段才会显示
            //     displayOptions: {
            //         show: {
            //             modelType: ['chat', 'completion']
            //         }
            //     },
            //     controlType: 'input'
            // },
            // {
            //     displayName: '温度',
            //     name: 'temperature',
            //     type: 'number',
            //     default: 0.7,
            //     description: '控制输出的随机性，范围0-2',
            //     typeOptions: {
            //         minValue: 0,
            //         maxValue: 2,
            //         numberPrecision: 1
            //     },
            //     displayOptions: {
            //         show: {
            //             modelType: ['chat', 'completion']
            //         }
            //     },
            //     controlType: 'input'
            // },
            // {
            //     displayName: 'Top P',
            //     name: 'topP',
            //     type: 'number',
            //     default: 1,
            //     description: '核采样参数，建议与temperature二选一',
            //     typeOptions: {
            //         minValue: 0,
            //         maxValue: 1,
            //         numberPrecision: 2
            //     },
            //     displayOptions: {
            //         show: {
            //             modelType: ['chat', 'completion']
            //         }
            //     },
            //     controlType: 'input'
            // },
            // {
            //     displayName: '频率惩罚',
            //     name: 'frequencyPenalty',
            //     type: 'number',
            //     default: 0,
            //     description: '降低重复内容的概率，范围-2到2',
            //     typeOptions: {
            //         minValue: -2,
            //         maxValue: 2,
            //         numberPrecision: 1
            //     },
            //     displayOptions: {
            //         show: {
            //             modelType: ['chat', 'completion']
            //         }
            //     }, controlType: 'input'
            // },
            // {
            //     displayName: '存在惩罚',
            //     name: 'presencePenalty',
            //     type: 'number',
            //     default: 0,
            //     description: '增加讨论新话题的可能性，范围-2到2',
            //     typeOptions: {
            //         minValue: -2,
            //         maxValue: 2,
            //         numberPrecision: 1
            //     },
            //     displayOptions: {
            //         show: {
            //             modelType: ['chat', 'completion']
            //         }
            //     },
            //     controlType: 'input'
            // },
            // {
            //     displayName: '请求超时(秒)',
            //     name: 'timeout',
            //     type: 'number',
            //     default: 30,
            //     description: '请求超时时间，单位：秒（流式输出建议30-60秒）',
            //     typeOptions: {
            //         minValue: 1,
            //         maxValue: 300
            //     },
            //     controlType: 'input'
            // },
            // {
            //     displayName: '重试次数',
            //     name: 'retries',
            //     type: 'number',
            //     default: 3,
            //     description: '请求失败时的重试次数',
            //     typeOptions: {
            //         minValue: 0,
            //         maxValue: 5
            //     },
            //      controlType: 'input'
            // },
            // {
            //     displayName: '流式输出',
            //     name: 'stream',
            //     type: 'boolean',
            //     default: false,
            //     description: '是否启用流式输出',
            //     controlType: 'CheckBox'
            // },
            // {
            //     displayName: '对话测试',
            //     name: 'dialogue',
            //     type: 'custom',
            //     default: '',
            //     description: '与大模型进行对话测试',
            //     controlType: 'llmdialogue'
            // }
        ],
        validateConnection: true,
        connectionTimeout: 10
    },

    async test(config: Record<string, any>, message?: string): Promise<ConnectTestResult> {
        try {
            // 验证必填字段
            if (!config.apiKey) {
                return {
                    success: false,
                    message: '缺少必填字段: apiKey'
                };
            }

            if (!config.model) {
                return {
                    success: false,
                    message: '缺少必填字段: model'
                };
            }

            // 使用通用测试器
            return await LLMTester.testConnection('siliconflow', {
                apiKey: config.apiKey,
                baseUrl: config.baseUrl || ConnectConfig.api.url,
                model: config.model,
                timeout: (config.timeout || 30) * 1000
            }, message);

        } catch (error) {
            return {
                success: false,
                message: `连接失败: ${error instanceof Error ? error.message : String(error)}`
            };
        }
    }

    // /**
    //  * 测试硅基流动连接
    //  */
    // async test(config: Record<string, any>, message?: string): Promise<ConnectTestResult> {
    //     const startTime = Date.now();

    //     try {
    //         // 验证必填字段
    //         if (!config.apiKey) {
    //             return {
    //                 success: false,
    //                 message: '缺少必填字段: apiKey'
    //             };
    //         }

    //         if (!config.model) {
    //             return {
    //                 success: false,
    //                 message: '缺少必填字段: model'
    //             };
    //         }

    //         // 验证API Key格式
    //         if (!config.apiKey.startsWith('sk-')) {
    //             return {
    //                 success: false,
    //                 message: 'API Key格式不正确，应该以"sk-"开头'
    //             };
    //         }

    //         // 验证baseUrl格式
    //         if (config.baseUrl) {
    //             try {
    //                 new URL(config.baseUrl);
    //             } catch {
    //                 return {
    //                     success: false,
    //                     message: 'baseUrl格式不正确，请输入有效的URL'
    //                 };
    //             }
    //         }

    //         // 验证模型是否受支持
    //         const supportedModelIds = this.detail.supportedModels.map(m => m.id);
    //         if (!supportedModelIds.includes(config.model)) {
    //             return {
    //                 success: false,
    //                 message: `不支持的模型: ${config.model}，支持的模型: ${supportedModelIds.join(', ')}`
    //             };
    //         }

    //         // 如果有消息，进行实际的对话测试，直接返回模型回复
    //         if (message && message.trim()) {
    //             const chatResult = await (this as any).chat(config, message);

    //             if (chatResult.success) {
    //                 // 对话成功时，返回模型的实际回复内容
    //                 return {
    //                     success: true,
    //                     message: chatResult.response || '模型回复为空',
    //                     response: chatResult.response,
    //                     latency: chatResult.latency,
    //                     details: {
    //                         ...chatResult.details,
    //                         isDialogue: true,
    //                         userMessage: message
    //                     }
    //                 };
    //             } else {
    //                 // 对话失败时，返回错误信息
    //                 return chatResult;
    //             }
    //         }

    //         // 没有消息时，进行连接测试
    //         const testMessage = "Hello";
    //         const testResult = await (this as any).chat(config, testMessage);

    //         if (testResult.success) {
    //             return {
    //                 success: true,
    //                 message: '硅基流动连接测试成功',
    //                 latency: testResult.latency,
    //                 details: {
    //                     model: config.model,
    //                     modelType: config.modelType,
    //                     baseUrl: config.baseUrl || 'https://api.siliconflow.cn/v1',
    //                     maxTokens: config.maxTokens,
    //                     temperature: config.temperature,
    //                     stream: config.stream,
    //                     supportedFeatures: this.detail.supportedFeatures,
    //                     modelInfo: this.detail.supportedModels.find(m => m.id === config.model),
    //                     testResponse: testResult.response,
    //                     isConnectionTest: true
    //                 }
    //             };
    //         } else {
    //             return {
    //                 success: false,
    //                 message: `连接测试失败: ${testResult.message}`,
    //                 latency: testResult.latency
    //             };
    //         }

    //     } catch (error) {
    //         return {
    //             success: false,
    //             message: `连接失败: ${error instanceof Error ? error.message : String(error)}`,
    //             latency: Date.now() - startTime
    //         };
    //     }
    // },

    // /**
    //  * 与硅基流动模型进行对话
    //  */
    // async chat(config: Record<string, any>, message: string): Promise<ConnectTestResult> {
    //     const startTime = Date.now();
    //     const useStream = config.stream || false;

    //     try {
    //         const baseUrl = config.baseUrl || 'https://api.siliconflow.cn/v1';
    //         const endpoint = `${baseUrl}/chat/completions`;

    //         const requestBody = {
    //             model: config.model,
    //             messages: [
    //                 {
    //                     role: 'user',
    //                     content: message
    //                 }
    //             ],
    //             max_tokens: config.maxTokens || 1000,
    //             temperature: config.temperature || 0.7,
    //             top_p: config.topP || 1,
    //             frequency_penalty: config.frequencyPenalty || 0,
    //             presence_penalty: config.presencePenalty || 0,
    //             stream: useStream
    //         };

    //         // 优化的请求头
    //         const headers: Record<string, string> = {
    //             'Content-Type': 'application/json',
    //             'Authorization': `Bearer ${config.apiKey}`,
    //             'Accept': 'application/json',
    //             'User-Agent': 'Cofly/1.0',
    //         };

    //         // 如果是流式请求，添加流式头部
    //         if (useStream) {
    //             headers['Accept'] = 'text/event-stream';
    //             headers['Cache-Control'] = 'no-cache';
    //             headers['Connection'] = 'keep-alive';
    //         }

    //         const response = await fetch(endpoint, {
    //             method: 'POST',
    //             headers,
    //             body: JSON.stringify(requestBody),
    //             signal: AbortSignal.timeout((config.timeout || 30) * 1000),
    //             keepalive: true,
    //         });

    //         if (!response.ok) {
    //             const errorData = await response.text();
    //             let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    //             try {
    //                 const errorJson = JSON.parse(errorData);
    //                 errorMessage = errorJson.error?.message || errorJson.message || errorMessage;

    //                 // 处理常见错误
    //                 if (response.status === 401) {
    //                     errorMessage = 'API Key无效，请检查您的硅基流动API密钥';
    //                 } else if (response.status === 403) {
    //                     errorMessage = 'API访问被拒绝，请检查您的账户权限';
    //                 } else if (response.status === 404) {
    //                     errorMessage = `模型 "${config.model}" 不存在或不可用`;
    //                 } else if (response.status === 429) {
    //                     errorMessage = '请求过于频繁，请稍后重试';
    //                 }
    //             } catch {
    //                 if (errorData) {
    //                     errorMessage = errorData.substring(0, 200);
    //                 }
    //             }

    //             return {
    //                 success: false,
    //                 message: `API请求失败: ${errorMessage}`,
    //                 latency: Date.now() - startTime
    //             };
    //         }

    //         const latency = Date.now() - startTime;

    //         if (useStream) {
    //             // 流式响应处理
    //             if (!response.body) {
    //                 return {
    //                     success: false,
    //                     message: '流式响应体为空',
    //                     latency
    //                 };
    //             }

    //             let fullContent = '';
    //             let usage: any = null;
    //             let buffer = '';  // 添加缓冲区来处理不完整的数据
    //             const reader = response.body.getReader();
    //             const decoder = new TextDecoder();

    //             try {
    //                 while (true) {
    //                     const { done, value } = await reader.read();
    //                     if (done) break;

    //                     const chunk = decoder.decode(value, { stream: true });
    //                     buffer += chunk;  // 将新数据添加到缓冲区

    //                     // 按行分割并处理完整的行
    //                     const lines = buffer.split('\n');
    //                     buffer = lines.pop() || '';  // 保留最后一个可能不完整的行

    //                     for (const line of lines) {
    //                         if (line.startsWith('data: ')) {
    //                             const data = line.slice(6).trim();
    //                             if (data === '[DONE]') {
    //                                 break;
    //                             }

    //                             if (data) {  // 确保数据不为空
    //                                 try {
    //                                     const parsed = JSON.parse(data);

    //                                     // 添加详细调试信息
    //                                     console.log('🔍 硅基流动API响应数据结构:', {
    //                                         hasChoices: !!parsed.choices,
    //                                         choicesLength: parsed.choices?.length,
    //                                         firstChoice: parsed.choices?.[0],
    //                                         delta: parsed.choices?.[0]?.delta,
    //                                         deltaKeys: parsed.choices?.[0]?.delta ? Object.keys(parsed.choices[0].delta) : [],
    //                                         fullParsed: parsed
    //                                     });

    //                                     if (parsed.choices && parsed.choices[0]) {
    //                                         const choice = parsed.choices[0];
    //                                         const delta = choice.delta;

    //                                         if (delta) {
    //                                             let newContent = '';

    //                                             // 检查可能的推理过程字段
    //                                             console.log('🧠 检查推理字段:', {
    //                                                 hasReasoning: !!delta.reasoning,
    //                                                 hasReasoningContent: !!delta.reasoning_content,
    //                                                 hasThinking: !!delta.thinking,
    //                                                 hasThought: !!delta.thought,
    //                                                 hasReflection: !!delta.reflection,
    //                                                 hasProcess: !!delta.process,
    //                                                 deltaContent: delta.content?.substring(0, 100),
    //                                                 reasoningContent: delta.reasoning_content?.substring(0, 100),
    //                                                 allDeltaKeys: Object.keys(delta)
    //                                             });

    //                                             // 处理推理过程 (DeepSeek-R1 系列模型) - 硅基流动使用reasoning_content字段
    //                                             if (delta.reasoning_content) {
    //                                                 // 直接输出推理内容，不添加重复的前缀
    //                                                 newContent += delta.reasoning_content;
    //                                                 console.log('💭 推理过程片段:', delta.reasoning_content.substring(0, 50) + '...');

    //                                                 // chat方法无onChunk参数，推理过程记录在控制台
    //                                             } else if (delta.reasoning) {
    //                                                 newContent += delta.reasoning;
    //                                                 console.log('💭 推理过程片段(reasoning):', delta.reasoning.substring(0, 50) + '...');

    //                                                 // chat方法无onChunk参数，推理过程记录在控制台
    //                                             } else if (delta.thinking) {
    //                                                 newContent += delta.thinking;
    //                                                 console.log('💭 思考过程片段(thinking):', delta.thinking.substring(0, 50) + '...');

    //                                                 // chat方法无onChunk参数，推理过程记录在控制台
    //                                             } else if (delta.thought) {
    //                                                 newContent += delta.thought;
    //                                                 console.log('💭 思维过程片段(thought):', delta.thought.substring(0, 50) + '...');

    //                                                 // chat方法无onChunk参数，推理过程记录在控制台
    //                                             }

    //                                             // 处理常规内容
    //                                             if (delta.content) {
    //                                                 newContent += delta.content;
    //                                                 console.log('📝 常规内容片段:', delta.content.substring(0, 50) + '...');

    //                                                 // chat方法无onChunk参数
    //                                             }

    //                                             // 如果有任何内容，就累积到全文
    //                                             if (newContent) {
    //                                                 fullContent += newContent;
    //                                             }
    //                                         }

    //                                         // 处理完成信息和usage
    //                                         if (choice.finish_reason) {
    //                                             console.log('🏁 生成完成，原因:', choice.finish_reason);
    //                                         }

    //                                         if (parsed.usage) {
    //                                             usage = parsed.usage;
    //                                         }
    //                                     }
    //                                 } catch (parseError) {
    //                                     // 记录解析错误但不中断流式处理
    //                                     console.warn('硅基流动 - 解析流式数据失败:', {
    //                                         error: parseError,
    //                                         data: data.substring(0, 100) + (data.length > 100 ? '...' : ''),
    //                                         dataLength: data.length
    //                                     });
    //                                 }
    //                             }
    //                         }
    //                     }
    //                 }

    //                 // 处理缓冲区中剩余的数据
    //                 if (buffer.trim()) {
    //                     const lines = buffer.split('\n');
    //                     for (const line of lines) {
    //                         if (line.startsWith('data: ')) {
    //                             const data = line.slice(6).trim();
    //                             if (data && data !== '[DONE]') {
    //                                 try {
    //                                     const parsed = JSON.parse(data);
    //                                     if (parsed.choices && parsed.choices[0]) {
    //                                         const choice = parsed.choices[0];
    //                                         const delta = choice.delta;

    //                                         if (delta) {
    //                                             let newContent = '';

    //                                             // 检查可能的推理过程字段
    //                                             console.log('🧠 检查推理字段:', {
    //                                                 hasReasoning: !!delta.reasoning,
    //                                                 hasReasoningContent: !!delta.reasoning_content,
    //                                                 hasThinking: !!delta.thinking,
    //                                                 hasThought: !!delta.thought,
    //                                                 hasReflection: !!delta.reflection,
    //                                                 hasProcess: !!delta.process,
    //                                                 deltaContent: delta.content?.substring(0, 100),
    //                                                 reasoningContent: delta.reasoning_content?.substring(0, 100),
    //                                                 allDeltaKeys: Object.keys(delta)
    //                                             });

    //                                             // 处理推理过程 (DeepSeek-R1 系列模型) - 硅基流动使用reasoning_content字段
    //                                             if (delta.reasoning_content) {
    //                                                 // 直接输出推理内容，不添加重复的前缀
    //                                                 newContent += delta.reasoning_content;
    //                                                 console.log('💭 推理过程片段:', delta.reasoning_content.substring(0, 50) + '...');

    //                                                 // chat方法无onChunk参数，推理过程记录在控制台
    //                                             } else if (delta.reasoning) {
    //                                                 newContent += delta.reasoning;
    //                                                 console.log('💭 推理过程片段(reasoning):', delta.reasoning.substring(0, 50) + '...');

    //                                                 // chat方法无onChunk参数，推理过程记录在控制台
    //                                             } else if (delta.thinking) {
    //                                                 newContent += delta.thinking;
    //                                                 console.log('💭 思考过程片段(thinking):', delta.thinking.substring(0, 50) + '...');

    //                                                 // chat方法无onChunk参数，推理过程记录在控制台
    //                                             } else if (delta.thought) {
    //                                                 newContent += delta.thought;
    //                                                 console.log('💭 思维过程片段(thought):', delta.thought.substring(0, 50) + '...');

    //                                                 // chat方法无onChunk参数，推理过程记录在控制台
    //                                             }

    //                                             // 处理常规内容
    //                                             if (delta.content) {
    //                                                 newContent += delta.content;
    //                                                 console.log('📝 常规内容片段:', delta.content.substring(0, 50) + '...');

    //                                                 // chat方法无onChunk参数
    //                                             }

    //                                             // 如果有任何内容，就累积到全文
    //                                             if (newContent) {
    //                                                 fullContent += newContent;
    //                                             }
    //                                         }

    //                                         // 处理完成信息和usage
    //                                         if (choice.finish_reason) {
    //                                             console.log('🏁 生成完成，原因:', choice.finish_reason);
    //                                         }

    //                                         if (parsed.usage) {
    //                                             usage = parsed.usage;
    //                                         }
    //                                     }
    //                                 } catch (parseError) {
    //                                     console.warn('硅基流动 - 解析最终缓冲数据失败:', parseError);
    //                                 }
    //                             }
    //                         }
    //                     }
    //                 }
    //             } finally {
    //                 reader.releaseLock();
    //             }

    //             return {
    //                 success: true,
    //                 message: '流式对话成功',
    //                 response: fullContent || '模型未返回内容',
    //                 latency,
    //                 details: {
    //                     model: config.model,
    //                     usage: usage,
    //                     tokens: usage?.total_tokens,
    //                     inputTokens: usage?.prompt_tokens,
    //                     outputTokens: usage?.completion_tokens,
    //                     isStreaming: true
    //                 }
    //             };
    //         } else {
    //             // 非流式响应处理
    //             const data = await response.json();

    //             if (data.choices && data.choices.length > 0) {
    //                 const assistantMessage = data.choices[0].message?.content || '模型未返回内容';

    //                 return {
    //                     success: true,
    //                     message: '对话成功',
    //                     response: assistantMessage,
    //                     latency,
    //                     details: {
    //                         model: config.model,
    //                         usage: data.usage,
    //                         tokens: data.usage?.total_tokens,
    //                         inputTokens: data.usage?.prompt_tokens,
    //                         outputTokens: data.usage?.completion_tokens,
    //                         finishReason: data.choices[0].finish_reason,
    //                         isStreaming: false
    //                     }
    //                 };
    //             } else {
    //                 return {
    //                     success: false,
    //                     message: '模型响应格式异常：没有返回有效的回复内容',
    //                     latency,
    //                     details: { rawResponse: data }
    //                 };
    //             }
    //         }

    //     } catch (error) {
    //         let errorMessage = '未知错误';
    //         if (error instanceof Error) {
    //             if (error.name === 'AbortError') {
    //                 errorMessage = '请求超时，请检查网络连接或增加超时时间';
    //             } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
    //                 errorMessage = '网络连接失败，请检查您的网络连接';
    //             } else {
    //                 errorMessage = error.message;
    //             }
    //         }

    //         return {
    //             success: false,
    //             message: `对话失败: ${errorMessage}`,
    //             latency: Date.now() - startTime
    //         };
    //     }
    // },

    // /**
    //  * 流式对话方法 - 支持分阶段输出：先推理内容，后常规内容
    //  */
    // async streamChat(config: Record<string, any>, message: string, onChunk?: (chunk: string, type?: 'reasoning' | 'content') => void): Promise<ConnectTestResult> {
    //     const startTime = Date.now();

    //     try {
    //         const baseUrl = config.baseUrl || 'https://api.siliconflow.cn/v1';
    //         const endpoint = `${baseUrl}/chat/completions`;

    //         const requestBody = {
    //             model: config.model,
    //             messages: [
    //                 {
    //                     role: 'user',
    //                     content: message
    //                 }
    //             ],
    //             max_tokens: Math.min(config.maxTokens || 1000, 2000),
    //             temperature: config.temperature || 0.7,
    //             top_p: config.topP || 1,
    //             frequency_penalty: config.frequencyPenalty || 0,
    //             presence_penalty: config.presencePenalty || 0,
    //             stream: true
    //         };

    //         const headers = {
    //             'Content-Type': 'application/json',
    //             'Authorization': `Bearer ${config.apiKey}`,
    //             'Accept': 'text/event-stream',
    //             'Cache-Control': 'no-cache',
    //             'Connection': 'keep-alive',
    //             'User-Agent': 'Cofly/1.0'
    //         };

    //         const response = await fetch(endpoint, {
    //             method: 'POST',
    //             headers,
    //             body: JSON.stringify(requestBody),
    //             signal: AbortSignal.timeout((config.timeout || 30) * 1000),
    //             keepalive: true,
    //         });

    //         if (!response.ok) {
    //             const errorData = await response.text();
    //             let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

    //             if (response.status === 429) {
    //                 errorMessage = '硅基流动API限流，请稍后重试（建议间隔30-60秒）';
    //             } else if (response.status === 503) {
    //                 errorMessage = '硅基流动服务暂时不可用，服务器可能过载';
    //             } else if (response.status === 524) {
    //                 errorMessage = '硅基流动服务器超时，建议使用更小的模型或减少token数量';
    //             }

    //             try {
    //                 const errorJson = JSON.parse(errorData);
    //                 errorMessage = errorJson.error?.message || errorJson.message || errorMessage;
    //             } catch {
    //                 if (errorData) {
    //                     errorMessage = errorData.substring(0, 200);
    //                 }
    //             }

    //             return {
    //                 success: false,
    //                 message: `流式API请求失败: ${errorMessage}`,
    //                 latency: Date.now() - startTime
    //             };
    //         }

    //         if (!response.body) {
    //             return {
    //                 success: false,
    //                 message: '流式响应体为空',
    //                 latency: Date.now() - startTime
    //             };
    //         }

    //         // 分别收集推理和常规内容
    //         let fullReasoningContent = '';
    //         let fullRegularContent = '';
    //         let reasoningCache = '';
    //         let regularCache = '';
    //         let usage: any = null;
    //         let firstChunkTime: number | null = null;
    //         let chunkCount = 0;
    //         let buffer = '';
    //         let isReasoningPhase = true; // 标记当前是否为推理阶段
    //         let hasStartedOutput = false; // 标记是否已开始输出
    //         const reader = response.body.getReader();
    //         const decoder = new TextDecoder();

    //         // 流式输出推理内容的函数
    //         const streamReasoningContent = async () => {
    //             if (reasoningCache && onChunk) {
    //                 console.log('🚀 开始流式输出推理内容...');
    //                 // 将推理内容分块流式输出
    //                 const chunks = reasoningCache.match(/.{1,10}/g) || [reasoningCache];
    //                 for (const chunk of chunks) {
    //                     onChunk(chunk, 'reasoning');
    //                     await new Promise(resolve => setTimeout(resolve, 20)); // 20ms延迟模拟流式效果
    //                 }
    //                 console.log('✅ 推理内容输出完毕');
    //             }
    //         };

    //         // 流式输出常规内容的函数
    //         const streamRegularContent = async () => {
    //             if (regularCache && onChunk) {
    //                 console.log('🚀 开始流式输出常规内容...');
    //                 // 将常规内容分块流式输出
    //                 const chunks = regularCache.match(/.{1,5}/g) || [regularCache];
    //                 for (const chunk of chunks) {
    //                     onChunk(chunk, 'content');
    //                     await new Promise(resolve => setTimeout(resolve, 30)); // 30ms延迟模拟流式效果
    //                 }
    //                 console.log('✅ 常规内容输出完毕');
    //             }
    //         };

    //         try {
    //             while (true) {
    //                 const { done, value } = await reader.read();
    //                 if (done) break;

    //                 if (firstChunkTime === null) {
    //                     firstChunkTime = Date.now();
    //                 }

    //                 const chunk = decoder.decode(value, { stream: true });
    //                 buffer += chunk;
    //                 chunkCount++;

    //                 // 按行分割并处理完整的行
    //                 const lines = buffer.split('\n');
    //                 buffer = lines.pop() || '';

    //                 for (const line of lines) {
    //                     if (line.startsWith('data: ')) {
    //                         const data = line.slice(6).trim();
    //                         if (data === '[DONE]') {
    //                             // 流结束，先输出推理内容，再输出常规内容
    //                             if (!hasStartedOutput && (reasoningCache || regularCache)) {
    //                                 hasStartedOutput = true;
    //                                 await streamReasoningContent();
    //                                 await streamRegularContent();
    //                             }
    //                             break;
    //                         }

    //                         if (data) {
    //                             try {
    //                                 const parsed = JSON.parse(data);

    //                                 if (parsed.choices && parsed.choices[0]) {
    //                                     const choice = parsed.choices[0];
    //                                     const delta = choice.delta;

    //                                     if (delta) {
    //                                         // 处理推理过程
    //                                         if (delta.reasoning_content) {
    //                                             reasoningCache += delta.reasoning_content;
    //                                             fullReasoningContent += delta.reasoning_content;
    //                                             console.log('💭 收集推理内容:', delta.reasoning_content.substring(0, 30) + '...');
    //                                         } else if (delta.reasoning) {
    //                                             reasoningCache += delta.reasoning;
    //                                             fullReasoningContent += delta.reasoning;
    //                                             console.log('💭 收集推理内容(reasoning):', delta.reasoning.substring(0, 30) + '...');
    //                                         } else if (delta.thinking) {
    //                                             reasoningCache += delta.thinking;
    //                                             fullReasoningContent += delta.thinking;
    //                                             console.log('💭 收集思考内容(thinking):', delta.thinking.substring(0, 30) + '...');
    //                                         } else if (delta.thought) {
    //                                             reasoningCache += delta.thought;
    //                                             fullReasoningContent += delta.thought;
    //                                             console.log('💭 收集思维内容(thought):', delta.thought.substring(0, 30) + '...');
    //                                         }

    //                                         // 处理常规内容
    //                                         if (delta.content) {
    //                                             regularCache += delta.content;
    //                                             fullRegularContent += delta.content;
    //                                             console.log('📝 缓存常规内容:', delta.content.substring(0, 30) + '...');

    //                                             // 如果推理阶段结束且有推理内容，先输出推理内容
    //                                             if (!hasStartedOutput && reasoningCache && isReasoningPhase) {
    //                                                 hasStartedOutput = true;
    //                                                 isReasoningPhase = false;
    //                                                 await streamReasoningContent();
    //                                                 // 开始流式输出常规内容的第一部分
    //                                                 if (onChunk) {
    //                                                     onChunk(delta.content, 'content');
    //                                                 }
    //                                             } else if (!hasStartedOutput) {
    //                                                 // 没有推理内容，直接输出常规内容
    //                                                 hasStartedOutput = true;
    //                                                 if (onChunk) {
    //                                                     onChunk(delta.content, 'content');
    //                                                 }
    //                                             } else if (!isReasoningPhase) {
    //                                                 // 推理阶段已结束，直接流式输出常规内容
    //                                                 if (onChunk) {
    //                                                     onChunk(delta.content, 'content');
    //                                                 }
    //                                             }
    //                                         }
    //                                     }

    //                                     // 处理完成信息和usage
    //                                     if (choice.finish_reason) {
    //                                         console.log('🏁 生成完成，原因:', choice.finish_reason);
    //                                     }

    //                                     if (parsed.usage) {
    //                                         usage = parsed.usage;
    //                                     }
    //                                 }
    //                             } catch (parseError) {
    //                                 console.warn('硅基流动 - 解析流式数据失败:', {
    //                                     error: parseError,
    //                                     data: data.substring(0, 100) + (data.length > 100 ? '...' : ''),
    //                                     dataLength: data.length
    //                                 });
    //                             }
    //                         }
    //                     }
    //                 }
    //             }

    //             // 处理缓冲区中剩余的数据
    //             if (buffer.trim()) {
    //                 const lines = buffer.split('\n');
    //                 for (const line of lines) {
    //                     if (line.startsWith('data: ')) {
    //                         const data = line.slice(6).trim();
    //                         if (data && data !== '[DONE]') {
    //                             try {
    //                                 const parsed = JSON.parse(data);
    //                                 if (parsed.choices && parsed.choices[0]) {
    //                                     const choice = parsed.choices[0];
    //                                     const delta = choice.delta;

    //                                     if (delta) {
    //                                         // 处理推理过程
    //                                         if (delta.reasoning_content) {
    //                                             reasoningCache += delta.reasoning_content;
    //                                             fullReasoningContent += delta.reasoning_content;
    //                                         } else if (delta.reasoning) {
    //                                             reasoningCache += delta.reasoning;
    //                                             fullReasoningContent += delta.reasoning;
    //                                         } else if (delta.thinking) {
    //                                             reasoningCache += delta.thinking;
    //                                             fullReasoningContent += delta.thinking;
    //                                         } else if (delta.thought) {
    //                                             reasoningCache += delta.thought;
    //                                             fullReasoningContent += delta.thought;
    //                                         }

    //                                         // 处理常规内容
    //                                         if (delta.content) {
    //                                             regularCache += delta.content;
    //                                             fullRegularContent += delta.content;

    //                                             if (!isReasoningPhase && onChunk) {
    //                                                 onChunk(delta.content, 'content');
    //                                             }
    //                                         }
    //                                     }

    //                                     if (parsed.usage) {
    //                                         usage = parsed.usage;
    //                                     }
    //                                 }
    //                             } catch (parseError) {
    //                                 console.warn('硅基流动 - 解析最终缓冲数据失败:', parseError);
    //                             }
    //                         }
    //                     }
    //                 }
    //             }

    //             // 确保所有内容都已输出
    //             if (!hasStartedOutput) {
    //                 if (reasoningCache) {
    //                     await streamReasoningContent();
    //                 }
    //                 if (regularCache) {
    //                     await streamRegularContent();
    //                 }
    //             }

    //         } finally {
    //             reader.releaseLock();
    //         }

    //         // 组合完整内容
    //         const fullContent = fullReasoningContent + fullRegularContent;

    //         return {
    //             success: true,
    //             message: '分阶段流式对话成功',
    //             response: fullContent || '模型未返回内容',
    //             latency: Date.now() - startTime,
    //             details: {
    //                 model: config.model,
    //                 usage: usage,
    //                 tokens: usage?.total_tokens,
    //                 inputTokens: usage?.prompt_tokens,
    //                 outputTokens: usage?.completion_tokens,
    //                 isStreaming: true,
    //                 streamComplete: true,
    //                 firstChunkLatency: firstChunkTime ? firstChunkTime - startTime : null,
    //                 chunkCount,
    //                 reasoningLength: fullReasoningContent.length,
    //                 regularLength: fullRegularContent.length,
    //                 hasReasoning: fullReasoningContent.length > 0
    //             }
    //         };

    //     } catch (error) {
    //         let errorMessage = '未知错误';
    //         let suggestions: string[] = [];

    //         if (error instanceof Error) {
    //             if (error.name === 'AbortError') {
    //                 errorMessage = '请求超时，硅基流动服务器响应过慢';
    //                 suggestions = [
    //                     '尝试使用更小的模型（如7B版本）',
    //                     '减少max_tokens设置',
    //                     '检查网络连接',
    //                     '稍后重试'
    //                 ];
    //             } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
    //                 errorMessage = '网络连接失败，无法连接到硅基流动服务器';
    //                 suggestions = [
    //                     '检查网络连接',
    //                     '确认硅基流动服务状态',
    //                     '尝试使用VPN'
    //                 ];
    //             } else {
    //                 errorMessage = error.message;
    //             }
    //         }

    //         return {
    //             success: false,
    //             message: `分阶段流式对话失败: ${errorMessage}`,
    //             latency: Date.now() - startTime,
    //             details: {
    //                 errorType: error instanceof Error ? error.name : 'unknown',
    //                 suggestions
    //             }
    //         };
    //     }
    // }
};

export default SiliconFlowConnect; 