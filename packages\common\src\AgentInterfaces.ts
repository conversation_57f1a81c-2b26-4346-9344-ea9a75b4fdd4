// Agent and MCP related interfaces

// 智能体配置接口，基于数据库结构
export interface AgentConfig {
  id: string;
  name: string;
  description: string;
  prompt?: string;
  avatar?: string | { name: string; color: string } | null;
  connectid: string;
  modelId?: string; // 选择的具体模型ID
  modelName?: string; // 选择的模型名称，用于显示
  createdAt: Date;
  updatedAt: Date;
  createUser: string;
  // 关联的MCP工具
  mcpTools?: Array<{
    id: string;
    name: string;
    type: string;
  }>;
  // 关联的连接配置信息
  connectConfig?: {
    id: string;
    name: string;
    ctype: string;
    mtype: string;
  };
}

// MCP配置接口
export interface McpConfig {
  id: string;
  name: string;
  type: string;
  mcpinfo: string;
  createdAt: Date;
  updatedAt: Date;
}

// 模型信息接口
export interface ModelInfo {
  id: string;
  name: string;
  group?: string;
  provider?: string;
  description?: string;
  maxTokens?: number;
  supportedFeatures?: string[];
  tags?: string[];
}