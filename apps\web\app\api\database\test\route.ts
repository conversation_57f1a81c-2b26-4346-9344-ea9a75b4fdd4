import { NextRequest, NextResponse } from 'next/server';
import { MySQLConnect, PostgreSQLConnect, SQLServerConnect, OracleConnect, DB2Connect, KingbaseConnect, DamengConnect } from '@repo/connect-set/connects/database';

// 支持的数据库提供商配置
const DATABASE_PROVIDERS = {
    mysql: MySQLConnect,
    postgresql: PostgreSQLConnect,
    sqlserver: SQLServerConnect,
    oracle: OracleConnect,
    db2: DB2Connect,
    kingbase: KingbaseConnect,
    dameng: DamengConnect
};

/**
 * POST /api/database/test
 * 批量测试数据库连接
 * 请求体:
 * {
 *   "connections": [
 *     {
 *       "id": "mysql-1",
 *       "provider": "mysql",
 *       "config": { ... }
 *     }
 *   ]
 * }
 */
export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        const { connections } = body;

        if (!connections || !Array.isArray(connections)) {
            return NextResponse.json(
                {
                    success: false,
                    error: '参数错误',
                    message: 'connections 必须是数组'
                },
                { status: 400 }
            );
        }

        if (connections.length === 0) {
            return NextResponse.json(
                {
                    success: false,
                    error: '参数错误',
                    message: 'connections 不能为空'
                },
                { status: 400 }
            );
        }

        // 并行测试所有连接
        const testPromises = connections.map(async (conn: any) => {
            const { id, provider, config } = conn;

            if (!provider || !config) {
                return {
                    id,
                    provider,
                    success: false,
                    error: '配置不完整',
                    message: 'provider 和 config 是必填字段'
                };
            }

            // 检查提供商是否支持
            const dbConnect = DATABASE_PROVIDERS[provider as keyof typeof DATABASE_PROVIDERS];
            if (!dbConnect) {
                return {
                    id,
                    provider,
                    success: false,
                    error: '不支持的提供商',
                    message: `数据库提供商 "${provider}" 不受支持`,
                    supportedProviders: Object.keys(DATABASE_PROVIDERS)
                };
            }

            try {
                // 执行连接测试
                const result = await dbConnect.test(config);
                return {
                    id,
                    provider,
                    ...result
                };
            } catch (error) {
                return {
                    id,
                    provider,
                    success: false,
                    error: '测试异常',
                    message: error instanceof Error ? error.message : String(error)
                };
            }
        });

        const results = await Promise.all(testPromises);

        // 统计结果
        const successful = results.filter(r => r.success).length;
        const failed = results.length - successful;

        return NextResponse.json({
            success: true,
            data: {
                total: results.length,
                successful,
                failed,
                results
            },
            message: `批量测试完成: ${successful} 成功, ${failed} 失败`
        });

    } catch (error) {
        console.error('数据库批量测试失败:', error);
        return NextResponse.json(
            {
                success: false,
                error: '数据库批量测试失败',
                message: error instanceof Error ? error.message : '未知错误'
            },
            { status: 500 }
        );
    }
}

/**
 * GET /api/database/test
 * 获取支持的数据库提供商列表
 */
export async function GET() {
    try {
        const providers = Object.keys(DATABASE_PROVIDERS);
        const providerDetails = Object.entries(DATABASE_PROVIDERS).map(([key, connect]) => ({
            provider: key,
            name: connect.connect.name,
            description: connect.connect.description,
            defaultPort: connect.detail.defaultPort,
            supportedFeatures: connect.detail.supportedFeatures
        }));

        return NextResponse.json({
            success: true,
            data: {
                providers,
                total: providers.length,
                details: providerDetails
            },
            message: '获取支持的数据库提供商列表成功'
        });

    } catch (error) {
        console.error('获取数据库提供商列表失败:', error);
        return NextResponse.json(
            {
                success: false,
                error: '获取数据库提供商列表失败',
                message: error instanceof Error ? error.message : '未知错误'
            },
            { status: 500 }
        );
    }
} 