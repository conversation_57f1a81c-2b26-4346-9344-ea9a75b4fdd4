export enum MapServerType {
    STDIO = 'stdio',
    SSE = 'sse',
    Streamable = 'streamable',
}

export interface McpServerConfig {
    id: string;
    name: string;
    description: string;
    type: MapServerType;
    isActive: boolean;
    timeout?: number;
}

export interface StdioMcpServerConfig extends McpServerConfig {
    command: string;
    args?: string[];
    env?: Record<string, string>;
}

export interface HttpMcpServerConfig extends McpServerConfig {
    url: string;
    headers?: Record<string, string>;
}