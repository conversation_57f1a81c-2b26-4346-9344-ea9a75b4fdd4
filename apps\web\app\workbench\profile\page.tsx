"use client";

import React from 'react';
import {
  Header,
  Title,
  SubTitle,
  ContentArea
} from '@repo/ui';
import { useAuth } from '../../../src/hooks/useAuth';

export default function ProfilePage() {
  const { user, logout } = useAuth();

  return (
    <>
      <Header>
        <div>
          <Title>个人资料</Title>
          <SubTitle>管理您的个人信息和偏好设置</SubTitle>
        </div>
      </Header>
      <ContentArea>
        <div style={{ padding: '20px' }}>
          <h2>用户信息</h2>
          <p>用户名: {user?.name || '未登录'}</p>
          <p>邮箱: {user?.email || '未设置'}</p>

          <button
            style={{
              marginTop: '20px',
              padding: '8px 16px',
              background: '#f44336',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
            onClick={logout}
          >
            退出登录
          </button>
        </div>
      </ContentArea>
    </>
  );
}