import { ILLMConnect, ILLMOverview, ConnectTestResult } from '@repo/common';
import { 
    createApiKeyField, 
    createBaseUrlField, 
    testLLMConnection
} from '../utils/llm-fields';

const ConnectConfig: ILLMOverview = {
    id: 'baichuan',
    name: '百川大模型',
    type: 'llm' as const,
    provider: 'baichuan',
    icon: 'baichuan.svg',
    tags: ["domestic"],
    description: '百川大模型AI连接',
    version: '1.0.0',
    api: { url: 'https://api.baichuan-ai.com', suffix: '/v1/chat/completions' },
    driver: 'openai',
    about: {
        apiHost: 'https://api.baichuan-ai.com',
        docUrl: 'https://platform.baichuan-ai.com/docs',
        modelUrl: 'https://platform.baichuan-ai.com/price',
        getKeyUrl: 'https://platform.baichuan-ai.com/'
    }
};

export const BaichuanConnect: ILLMConnect = {
    overview: ConnectConfig,
    detail: {
        supportedModels: [
            {id: 'baichuan2-turbo', name: 'baichuan2-turbo',group: 'Baichuan'},
            {id: 'baichuan2-turbo-192k', name: 'baichuan2-turbo-192k',group: 'Baichuan'},
            {id: 'baichuan3-turbo', name: 'baichuan3-turbo',group: 'Baichuan'},
            {id: 'baichuan3-turbo-128k', name: 'baichuan3-turbo-128k',group: 'Baichuan'},
        ],
        fields: [
            createApiKeyField('sk-...'),
            createBaseUrlField(ConnectConfig.api.url),
        ],
        validateConnection: true,
        connectionTimeout: 10
    },

    async test(config: Record<string, any>, message?: string): Promise<ConnectTestResult> {
        return testLLMConnection(
            ConnectConfig.id,
            config,
            ConnectConfig.api.url+ConnectConfig.api.suffix||'',
            message
        );
    }
}; 