import { ILLMConnect, ILLMOverview, ConnectTestResult } from '@repo/common';
import { 
    createApiKeyField, 
    createBaseUrlField, 
    testLLMConnection
} from '../utils/llm-fields';

const ConnectConfig: ILLMOverview = {
    id: 'anthropic',
    name: 'Anthropic',
    type: 'llm' as const,
    provider: 'anthropic',
    icon: 'anthropic.svg',
    tags: ["international"],
    description: 'Anthropic Claude AI模型连接',
    version: '1.0.0',
    api: { url: 'https://api.anthropic.com/'},
    driver: 'anthropic',
    about: {
        apiHost: 'https://api.anthropic.com/',
        docUrl: 'https://docs.anthropic.com/en/docs',
        modelUrl: 'https://docs.anthropic.com/en/docs/about-claude/models',
        getKeyUrl: 'https://console.anthropic.com/'
    }
};

export const AnthropicConnect: ILLMConnect = {
    overview: ConnectConfig,
    detail: {
        supportedModels: [
            {id: 'claude-sonnet-4-20250514', name: '<PERSON> 4', group: '<PERSON> 4'},
            {id: 'claude-opus-4-20250514', name: '<PERSON> 4', group: '<PERSON> 4'},
            {id: 'claude-3-7-sonnet-20250219', name: '<PERSON> 3.7 Sonnet', group: '<PERSON> 3.7'},
            {id: 'claude-3-7-sonnet-20250219', name: '<PERSON> 3.7 Sonnet', group: '<PERSON> 3.7'},
            {id: 'claude-3-7-sonnet-20250219', name: '<PERSON> 3.7 Sonnet', group: 'Claude 3.7'}, 
            {id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet', group: 'Claude 3.5'},
            {id: 'claude-3-5-haiku-20241022', name: 'Claude 3.5 Haiku', group: 'Claude 3.5'},
            {id: 'claude-3-5-sonnet-20240620', name: 'Claude 3.5 Sonnet (Legacy)', group: 'Claude 3.5'},
            {id: 'claude-3-5-haiku-20240307', name: 'Claude 3.5 Haiku (Legacy)', group: 'Claude 3.5'},
            {id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', group: 'Claude 3'},
            {id: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku', group: 'Claude 3'},
        ],
        fields: [
            createApiKeyField('sk-ant-...'),
            createBaseUrlField(ConnectConfig.api.url),
        ],
        validateConnection: true,
        connectionTimeout: 10
    },

    async test(config: Record<string, any>, message?: string): Promise<ConnectTestResult> {
        return testLLMConnection(
            ConnectConfig.id,
            config,
            ConnectConfig.api.url,
            message
        );
    }
}; 