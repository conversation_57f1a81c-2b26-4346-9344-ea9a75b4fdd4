import { injectable } from 'inversify';
import { INode, INodeBasic, INodeDetail, CategoryType, IExecuteOptions, IconName } from '@repo/common';
import config from "./node.json"
import aiAgent from "./AiAgent"

@injectable()
export class AiAgent implements INode {
    node: INodeBasic = {
        kind: config.kind,
        name: config.name,
        categories: config.categories as CategoryType[],
        version: config.version,
        position: config.position as [number, number],
        description: config.description,
        icon: config.icon as IconName,
    };
    detail: INodeDetail = {
        // displayName: config.displayName,
        // name: config.name,
        // group: config.group as NodeGroupType[],
        // subtitle: config.subtitle,
        // description: config.description,
        // inputs: [NodeConnectionTypes.Main],
        // outputs: [NodeConnectionTypes.Main],
        fields: [
            {
                displayName: 'Chat Model',
                name: 'chatModel',
                type: 'options',
                options: [
                    {
                        name: 'Gemini',
                        value: 'gemini',
                    },
                    {
                        name: 'Ollama',
                        value: 'ollama',
                    },
                    {
                        name: 'Silicon Flow',
                        value: 'siliconflow',
                    },
                ],
                default: 'siliconflow',
                controlType: 'options'
            },
            {
                displayName: 'API Key',
                name: 'apiKey',
                type: 'string',
                default: '',
                required: true,
                placeholder: '',
                description:
                    'Chat Model API Key.',
                controlType: 'input'
            },
            {
                displayName: 'Base Url',
                name: 'baseUrl',
                type: 'string',
                default: "",
                required: true,
                placeholder: 'http://127.0.0.1:11434',
                description:
                    'Chat Model Base Url.',
                controlType: 'input'
            },
            {
                displayName: 'Model Version or Tag',
                name: 'modelTag',
                type: 'string',
                default: "",
                required: true,
                placeholder: '',
                description:
                    'Chat Model Version or Tag.',
                controlType: 'input'
            },
            {
                displayName: 'System message',
                name: 'systemMessage',
                type: 'string',
                default: '',
                required: true,
                placeholder: '',
                description:
                    'Chat Model System Message.',
                controlType: 'input'
            },
            {
                displayName: 'Contents',
                name: 'contents',
                type: 'string',
                default: '',
                required: true,
                placeholder: '',
                description:
                    'Contents.',
                controlType: 'input'
            },
        ],
    };

    async execute(opts: IExecuteOptions): Promise<any> {
        const inputs = opts?.inputs
        return await aiAgent({
            chatModel: inputs?.chatModel,
            apiKey: inputs?.apiKey,
            baseUrl: inputs?.baseUrl,
            modelTag: inputs?.modelTag,
            systemMessage: inputs?.systemMessage,
            contents: inputs?.contents
        });
    }
}
