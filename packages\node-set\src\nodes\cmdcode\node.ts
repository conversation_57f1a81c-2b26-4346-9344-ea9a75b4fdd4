﻿import { injectable } from 'inversify';
import type { IExecuteOptions, INode, INodeBasic, INodeDetail } from '@repo/common';
import { NodeLink } from '@repo/common';
import { execSync } from 'child_process';
import iconv from 'iconv-lite';

@injectable()
export class Code implements INode {
	node: INodeBasic = {
		kind: 'cmdcode',
		name: '本地命令',
		event: "code",
		categories: ['general'],
		version: 1,
		position: [0, 0],
		description: "Command命令操作",
		icon: 'cmdcode.svg',
		nodeWidth: 700,
		link: {
			inputs: [NodeLink.Data],
			outputs: [NodeLink.Data]
		}
	};
	detail: INodeDetail = {
		fields: [
			// 模式选择器（核心联动字段）
			{
				displayName: 'Command命令',      // 显示名称
				name: 'command',                 // 字段名
				type: 'string',              // 字段类型
				placeholder: '请输入命令...\nwindows例如:\ndir && echo hello\nLinux例如:\necho "Hello World"\nls -la\nnpm install\ngit status',   // 描述
				default: ``,
				controlType: 'cmdcode'
			}
			// {
			// 	displayName: '字符编码',      // 显示名称
			// 	name: 'encoding',                 // 字段名
			// 	type: 'options',              // 字段类型
			// 	options: [                    // 选项列表
			// 		{
			// 			name: 'GBK',   // 选项显示名
			// 			value: 'gbk',  // 选项值
			// 		},
			// 		{
			// 			name: 'UTF-8',
			// 			value: 'utf8',
			// 		},
			// 	],
			// 	default: 'gbk',    // 默认值
			// 	controlType: 'select'        // 提示AI这是联动触发器
			// }
		],
	};

	async execute(opts: IExecuteOptions): Promise<any> {
		try {
			// 获取当前系统平台
			const isWindows = process.platform === 'win32';

			// 执行命令并获取原始缓冲区
			const buffer = execSync(opts.inputs?.command, {
				encoding: 'buffer', // 始终获取 Buffer
				windowsHide: true, // 避免在Windows上创建额外窗口
				stdio: ['inherit', 'pipe', 'pipe'] // 分离stdout/stderr
			});

			// 处理编码转换
			let stdout: string;
			if (isWindows) {
				/// 获取系统当前代码页
				let encoding = 'gbk'; // 默认值
				try {
					const chcpOutput = execSync('chcp', { encoding: 'ascii' });
					const match = chcpOutput.match(/: (\d+)/);
					if (match && match[1]) {
						encoding = `cp${match[1]}`;
					}
				} catch (e) {
				}
				stdout = iconv.decode(buffer, encoding);
			} else {
				// 非Windows系统使用UTF-8
				stdout = buffer.toString('utf8');
			}
			return {
				exitCode: 0,
				stdout: stdout
			};
		} catch (error: any) {
			// 错误处理
			let errorOutput = "";

			// 处理错误输出编码
			if (error.stderr) {
				try {
					if (process.platform === 'win32') {
						// 使用 iconv-lite 解码错误输出
						errorOutput = iconv.decode(error.stderr, 'gbk');
					} else {
						errorOutput = error.stderr.toString('utf8');
					}
				} catch (decodeError) {
					errorOutput = error.stderr.toString('utf8', { fatal: false });
				}
			} else {
				errorOutput = error.message || "未知错误";
			}

			console.error('命令执行错误:', errorOutput);

			return {
				exitCode: error.status || -1, // 保留原始退出码
				stderr: errorOutput,
				stdout: ""
			};
		}
	}
}

