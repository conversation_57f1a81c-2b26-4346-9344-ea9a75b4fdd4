{"name": "@repo/connect-set", "version": "1.0.0", "description": "Connection definitions and configurations for database, API, and LLM connections", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsup && pnpm run copy-files", "copy-files": "copyfiles -u 1 \"src/connects/**/*.{json,svg}\" dist", "dev": "tsup --watch", "clean": "<PERSON><PERSON><PERSON> dist", "typecheck": "tsc --noEmit"}, "dependencies": {"@repo/common": "workspace:*"}, "devDependencies": {"@types/node": "^18.19.3", "copyfiles": "^2.4.1", "rimraf": "^3.0.2", "tsup": "^8.0.1", "typescript": "^5.3.3"}, "peerDependencies": {"mysql2": "^3.6.0", "mysql": "^2.18.0", "pg": "^8.11.0", "oracledb": "^6.0.0", "mssql": "^10.0.0", "kb": "*", "ibm_db": "^3.0.0"}, "peerDependenciesMeta": {"mysql2": {"optional": true}, "mysql": {"optional": true}, "pg": {"optional": true}, "oracledb": {"optional": true}, "mssql": {"optional": true}, "kb": {"optional": true}, "ibm_db": {"optional": true}}, "files": ["dist/**/*"], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}, "./connects/database": {"types": "./dist/connects/database/index.d.ts", "import": "./dist/connects/database/index.js", "require": "./dist/connects/database/index.js"}, "./connects/llm": {"types": "./dist/connects/llm/index.d.ts", "import": "./dist/connects/llm/index.js", "require": "./dist/connects/llm/index.js"}, "./connects/llm/utils/llm-tester": {"types": "./dist/connects/llm/utils/llm-tester.d.ts", "import": "./dist/connects/llm/utils/llm-tester.js", "require": "./dist/connects/llm/utils/llm-tester.js"}, "./connects/database/utils/database-tester": {"types": "./dist/connects/database/utils/database-tester.d.ts", "import": "./dist/connects/database/utils/database-tester.js", "require": "./dist/connects/database/utils/database-tester.js"}, "./connects/*": {"types": "./dist/connects/*.d.ts", "import": "./dist/connects/*.js", "require": "./dist/connects/*.js"}}}