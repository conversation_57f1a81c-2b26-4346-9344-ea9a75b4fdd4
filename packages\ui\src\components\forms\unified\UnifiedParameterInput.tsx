"use client";

import React from 'react';
import styled from 'styled-components';
import {
  Button,
  Card,
  CheckBox,
  JsCode,
  CmdCode,
  Input,
  Select as SelectControl,
  SelectWithDesc,
  InputSelect,
  SQLText,
  FileUpload,
  Note,
  TextArea
} from '../../../controls';

// 变体配置类型定义
export type ParameterInputVariant = 'node' | 'connect';

// 样式配置接口
interface VariantConfig {
  container: {
    marginBottom: string;
  };
  label: {
    fontSize: string;
    fontWeight: string;
    marginBottom: string;
    color: (theme: any) => string;
  };
  description: {
    fontSize: string;
    color: (theme: any) => string;
    marginTop?: string;
    marginBottom?: string;
  };
  input: {
    height?: string;
    padding: string;
    fontSize: string;
    borderRadius: string;
    border: (theme: any) => string;
    background: (theme: any) => string;
    color: (theme: any) => string;
    placeholderColor: (theme: any) => string;
    focusBorderColor: string | ((theme: any) => string);
    focusBoxShadow?: string;
    hoverBorderColor?: (theme: any) => string;
    disabledBackground?: (theme: any) => string;
    disabledColor?: (theme: any) => string;
  };
  required: {
    color: string;
    marginLeft: string;
  };
}

// 变体样式配置
const variantConfigs: Record<ParameterInputVariant, VariantConfig> = {
  node: {
    container: {
      marginBottom: '14px'
    },
    label: {
      fontSize: '14px',
      fontWeight: 'normal',
      marginBottom: '8px',
      color: (theme: any) => theme.colors.textPrimary
    },
    description: {
      fontSize: '12px',
      color: (theme: any) => theme.colors.textTertiary
    },
    input: {
      padding: '8px',
      fontSize: '14px',
      borderRadius: '4px',
      border: (theme: any) => `1px solid ${theme.colors.border}`,
      background: (theme: any) => theme.colors.inputBg,
      color: (theme: any) => theme.colors.textPrimary,
      placeholderColor: (theme: any) => theme.colors.textTertiary,
      focusBorderColor: (theme: any) => theme.colors.accent
    },
    required: {
      color: '#ef4444',
      marginLeft: '4px'
    }
  },
  connect: {
    container: {
      marginBottom: '16px'
    },
    label: {
      fontSize: '14px',
      fontWeight: '500',
      marginBottom: '8px',
      color: (theme: any) => theme.mode === 'dark' ? '#e2e8f0' : '#374151'
    },
    description: {
      fontSize: '12px',
      color: (theme: any) => theme.mode === 'dark' ? 'rgba(255, 255, 255, 0.6)' : '#6b7280',
      marginTop: '4px',
      marginBottom: '4px'
    },
    input: {
      height: '32px',
      padding: '0 12px',
      fontSize: '14px',
      borderRadius: '6px',
      border: (theme: any) => theme.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid #e0e0e0',
      background: (theme: any) => theme.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : '#ffffff',
      color: (theme: any) => theme.mode === 'dark' ? '#e2e8f0' : '#374151',
      placeholderColor: (theme: any) => theme.mode === 'dark' ? 'rgba(255, 255, 255, 0.4)' : '#9ca3af',
      focusBorderColor: '#3b82f6',
      focusBoxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)',
      hoverBorderColor: (theme: any) => theme.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : '#d1d5db',
      disabledBackground: (theme: any) => theme.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : '#f9fafb',
      disabledColor: (theme: any) => theme.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : '#9ca3af'
    },
    required: {
      color: '#ef4444',
      marginLeft: '4px'
    }
  }
};

// 样式化组件
const InputContainer = styled.div<{ $variant: ParameterInputVariant }>`
  margin-bottom: ${props => variantConfigs[props.$variant].container.marginBottom};
`;

const Label = styled.label<{ $variant: ParameterInputVariant }>`
  display: block;
  margin-bottom: ${props => variantConfigs[props.$variant].label.marginBottom};
  font-size: ${props => variantConfigs[props.$variant].label.fontSize};
  font-weight: ${props => variantConfigs[props.$variant].label.fontWeight};
  color: ${props => variantConfigs[props.$variant].label.color(props.theme)};
`;

const Required = styled.span<{ $variant: ParameterInputVariant }>`
  color: ${props => variantConfigs[props.$variant].required.color};
  margin-left: ${props => variantConfigs[props.$variant].required.marginLeft};
`;

const Description = styled.p<{ $variant: ParameterInputVariant; $isHint?: boolean }>`
  margin: ${props => {
    const config = variantConfigs[props.$variant].description;
    if (props.$isHint) {
      return `${config.marginTop || '0'} 0 0 0`;
    }
    return `${config.marginTop || '0'} 0 ${config.marginBottom || '0'} 0`;
  }};
  font-size: ${props => variantConfigs[props.$variant].description.fontSize};
  color: ${props => variantConfigs[props.$variant].description.color(props.theme)};
  line-height: 1.4;
  font-style: ${props => props.$isHint ? 'italic' : 'normal'};
`;

const StyledTextInput = styled.input<{ $variant: ParameterInputVariant }>`
  width: 100%;
  ${props => props.$variant === 'connect' ? `height: ${variantConfigs[props.$variant].input.height};` : ''}
  padding: ${props => variantConfigs[props.$variant].input.padding};
  border: ${props => variantConfigs[props.$variant].input.border(props.theme)};
  border-radius: ${props => variantConfigs[props.$variant].input.borderRadius};
  font-size: ${props => variantConfigs[props.$variant].input.fontSize};
  background: ${props => variantConfigs[props.$variant].input.background(props.theme)};
  color: ${props => variantConfigs[props.$variant].input.color(props.theme)};
  transition: all 0.2s ease;
  outline: none;
  box-sizing: border-box;

  &::placeholder {
    color: ${props => variantConfigs[props.$variant].input.placeholderColor(props.theme)};
  }

  &:focus {
    border-color: ${props => {
      const focusColor = variantConfigs[props.$variant].input.focusBorderColor;
      return typeof focusColor === 'function' ? focusColor(props.theme) : focusColor;
    }};
    ${props => props.$variant === 'connect' && variantConfigs[props.$variant].input.focusBoxShadow ? 
      `box-shadow: ${variantConfigs[props.$variant].input.focusBoxShadow};` : ''}
  }

  ${props => props.$variant === 'connect' && variantConfigs[props.$variant].input.hoverBorderColor ? `
    &:hover {
      border-color: ${variantConfigs[props.$variant].input.hoverBorderColor!(props.theme)};
    }
  ` : ''}

  ${props => props.$variant === 'connect' && variantConfigs[props.$variant].input.disabledBackground ? `
    &:disabled {
      background: ${variantConfigs[props.$variant].input.disabledBackground!(props.theme)};
      color: ${variantConfigs[props.$variant].input.disabledColor!(props.theme)};
      cursor: not-allowed;
      
      &::placeholder {
        color: ${props.theme.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : '#d1d5db'};
      }
    }
  ` : ''}
  
  &[type="password"] {
    font-family: inherit;
  }
`;

const StyledTextArea = styled.textarea<{ $variant: ParameterInputVariant }>`
  width: 100%;
  ${props => props.$variant === 'connect' ? `min-height: 80px;` : ''}
  padding: ${props => variantConfigs[props.$variant].input.padding};
  border: ${props => variantConfigs[props.$variant].input.border(props.theme)};
  border-radius: ${props => variantConfigs[props.$variant].input.borderRadius};
  font-size: ${props => variantConfigs[props.$variant].input.fontSize};
  background: ${props => variantConfigs[props.$variant].input.background(props.theme)};
  color: ${props => variantConfigs[props.$variant].input.color(props.theme)};
  transition: all 0.2s ease;
  outline: none;
  box-sizing: border-box;
  resize: vertical;

  &::placeholder {
    color: ${props => variantConfigs[props.$variant].input.placeholderColor(props.theme)};
  }

  &:focus {
    border-color: ${props => {
      const focusColor = variantConfigs[props.$variant].input.focusBorderColor;
      return typeof focusColor === 'function' ? focusColor(props.theme) : focusColor;
    }};
    ${props => props.$variant === 'connect' && variantConfigs[props.$variant].input.focusBoxShadow ? 
      `box-shadow: ${variantConfigs[props.$variant].input.focusBoxShadow};` : ''}
  }

  ${props => props.$variant === 'connect' && variantConfigs[props.$variant].input.hoverBorderColor ? `
    &:hover {
      border-color: ${variantConfigs[props.$variant].input.hoverBorderColor!(props.theme)};
    }
  ` : ''}

  ${props => props.$variant === 'connect' && variantConfigs[props.$variant].input.disabledBackground ? `
    &:disabled {
      background: ${variantConfigs[props.$variant].input.disabledBackground!(props.theme)};
      color: ${variantConfigs[props.$variant].input.disabledColor!(props.theme)};
      cursor: not-allowed;
    }
  ` : ''}
`;

const StyledSelect = styled.select<{ $variant: ParameterInputVariant }>`
  width: 100%;
  ${props => props.$variant === 'connect' ? `height: ${variantConfigs[props.$variant].input.height};` : ''}
  padding: ${props => variantConfigs[props.$variant].input.padding};
  border: ${props => variantConfigs[props.$variant].input.border(props.theme)};
  border-radius: ${props => variantConfigs[props.$variant].input.borderRadius};
  font-size: ${props => variantConfigs[props.$variant].input.fontSize};
  background: ${props => variantConfigs[props.$variant].input.background(props.theme)};
  color: ${props => variantConfigs[props.$variant].input.color(props.theme)};
  transition: all 0.2s ease;
  outline: none;
  cursor: pointer;
  box-sizing: border-box;

  option {
    background: ${props => props.theme.mode === 'dark' ? '#1a1a1a' : '#ffffff'};
    color: ${props => variantConfigs[props.$variant].input.color(props.theme)};
    padding: 8px 12px;
  }

  &:focus {
    border-color: ${props => {
      const focusColor = variantConfigs[props.$variant].input.focusBorderColor;
      return typeof focusColor === 'function' ? focusColor(props.theme) : focusColor;
    }};
    ${props => props.$variant === 'connect' && variantConfigs[props.$variant].input.focusBoxShadow ? 
      `box-shadow: ${variantConfigs[props.$variant].input.focusBoxShadow};` : ''}
  }

  ${props => props.$variant === 'connect' && variantConfigs[props.$variant].input.hoverBorderColor ? `
    &:hover {
      border-color: ${variantConfigs[props.$variant].input.hoverBorderColor!(props.theme)};
    }
  ` : ''}

  ${props => props.$variant === 'connect' && variantConfigs[props.$variant].input.disabledBackground ? `
    &:disabled {
      background: ${variantConfigs[props.$variant].input.disabledBackground!(props.theme)};
      color: ${variantConfigs[props.$variant].input.disabledColor!(props.theme)};
      cursor: not-allowed;
    }
  ` : ''}
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
`;

const StyledCheckbox = styled.input<{ $variant: ParameterInputVariant }>`
  margin-right: 8px;
  ${props => props.$variant === 'connect' ? 'width: 16px; height: 16px;' : ''}
  accent-color: ${props => {
    const focusColor = variantConfigs[props.$variant].input.focusBorderColor;
    if (props.$variant === 'node') {
      return typeof focusColor === 'function' ? focusColor(props.theme) : focusColor;
    }
    return '#3b82f6';
  }};
`;

// 统一的参数接口
export interface UnifiedParameterField {
  name: string;
  displayName: string;
  type: string;
  controlType?: string;
  default?: any;
  options?: { name?: string; value: any; description?: string }[];
  description?: string;
  hint?: string;
  placeholder?: string;
  required?: boolean;
  isSecure?: boolean;
  typeOptions?: {
    password?: boolean;
    minValue?: number;
    maxValue?: number;
    numberPrecision?: number;
  };
  displayOptions?: {
    show?: {
      [key: string]: string[];
    };
  };
}

export interface UnifiedParameterInputProps {
  variant: ParameterInputVariant;
  field: UnifiedParameterField;
  value: any;
  onChange: (name: string, value: any) => void;
  formValues?: Record<string, any>;
  onExpandModeChange?: (expanded: boolean) => void;
}

export const UnifiedParameterInput: React.FC<UnifiedParameterInputProps> = ({
  variant,
  field,
  value,
  onChange,
  formValues = {},
  onExpandModeChange
}) => {
  // 检查是否应该显示此参数
  const shouldShow = () => {
    if (!field.displayOptions?.show) {
      return true;
    }
    
    return Object.entries(field.displayOptions.show).every(([fieldName, values]) => {
      const currentValue = formValues[fieldName];
      return values.includes(currentValue);
    });
  };

  if (!shouldShow()) {
    return null;
  }

  const handleChange = (event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    let newValue;
    
    if (field.type === 'boolean') {
      newValue = (event.target as HTMLInputElement).checked;
    } else if (field.type === 'number') {
      const inputValue = event.target.value;
      if (variant === 'node') {
        // node版本的处理逻辑：空字符串保持为空，否则转换为数字
        if (inputValue === '') {
          newValue = '';
        } else {
          const numValue = parseFloat(inputValue);
          newValue = isNaN(numValue) ? inputValue : numValue;
        }
      } else {
        // connect版本的处理逻辑：空值为undefined，否则转换为数字
        newValue = inputValue ? Number(inputValue) : undefined;
      }
    } else {
      newValue = event.target.value;
    }
    
    onChange(field.name, newValue);
  };

  const handleTextAreaChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(field.name, event.target.value);
  };

  const handleInputChange = (value: string) => {
    onChange(field.name, value);
  };

  const handleSelectChange = (value: string | number) => {
    onChange(field.name, value);
  };

  const handleCheckboxChange = (checked: boolean) => {
    onChange(field.name, checked);
  };

  const handleFileChange = (file: File | null) => {
    onChange(field.name, file);
  };

  const handleButtonClick = () => {
    // Button actions can be handled here
  };

  const renderInput = () => {
    const controlType = field.controlType || field.type;
    
    switch (controlType) {
      case 'button':
        // 仅node变体支持
        if (variant !== 'node') return null;
        return (
          <Button onClick={handleButtonClick}>
            {field.displayName}
          </Button>
        );

      case 'card':
        // 仅node变体支持
        if (variant !== 'node') return null;
        return (
          <Card
            title={field.displayName}
            href="#"
          >
            {field.description}
          </Card>
        );

      case 'checkbox':
        return (
          <CheckBox
            checked={value || false}
            onChange={handleCheckboxChange}
            label={field.displayName}
            disabled={false}
          />
        );

      case 'jscode':
        // 仅node变体支持
        if (variant !== 'node') return null;
        return (
          <>
            <Label $variant={variant}>{field.displayName}</Label>
            {React.createElement(JsCode, {
              value: value || '',
              onChange: (val: string) => onChange(field.name, val),
              height: "200px",
              placeholder: field.placeholder,
              onExpandModeChange: onExpandModeChange
            })}
          </>
        );

      case 'cmdcode':
        // 仅node变体支持
        if (variant !== 'node') return null;
        return (
          <>
            <Label $variant={variant}>{field.displayName}</Label>
            <CmdCode
              value={value || ''}
              onChange={(val) => onChange(field.name, val)}
              height="200px"
              placeholder={field.placeholder}
              onExpandModeChange={onExpandModeChange}
            />
          </>
        );

      case 'input':
      case 'string':
        if (variant === 'node') {
          return (
            <>
              <Label $variant={variant}>{field.displayName}</Label>
              <Input
                type="text"
                value={value || ''}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(field.name, e.target.value)}
                placeholder={field.description || field.placeholder}
              />
            </>
          );
        } else {
          return (
            <StyledTextInput
              $variant={variant}
              type={field.isSecure || field.typeOptions?.password ? "password" : "text"}
              value={value || ''}
              onChange={handleChange}
              placeholder={field.placeholder || field.description}
              min={field.typeOptions?.minValue}
              max={field.typeOptions?.maxValue}
            />
          );
        }

      case 'number':
        if (variant === 'node') {
          return (
            <>
              <Label $variant={variant}>{field.displayName}</Label>
              <Input
                type="number"
                value={value || ''}
                onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
                  const target = e.target as HTMLInputElement;
                  const inputValue = target.value;
                  // node版本的处理逻辑：空字符串保持为空，否则转换为数字
                  if (inputValue === '') {
                    onChange(field.name, '');
                  } else {
                    const numValue = parseFloat(inputValue);
                    onChange(field.name, isNaN(numValue) ? inputValue : numValue);
                  }
                }}
                placeholder={field.description || field.placeholder}
              />
            </>
          );
        } else {
          return (
            <StyledTextInput
              $variant={variant}
              type="number"
              value={value !== undefined ? value : ''}
              onChange={handleChange}
              placeholder={field.placeholder || field.description}
              min={field.typeOptions?.minValue}
              max={field.typeOptions?.maxValue}
              step={field.typeOptions?.numberPrecision ? Math.pow(10, -field.typeOptions.numberPrecision) : undefined}
            />
          );
        }

      case 'boolean':
        return (
          <CheckboxContainer>
            <StyledCheckbox
              $variant={variant}
              type="checkbox"
              checked={value || false}
              onChange={handleChange}
            />
            <span>{field.displayName}</span>
          </CheckboxContainer>
        );

      case 'select':
        if (variant === 'node') {
          return (
            <>
              <Label $variant={variant}>{field.displayName}</Label>
              <SelectControl
                options={field.options?.map(opt => ({
                  value: opt.value,
                  label: opt.name || opt.value
                })) || []}
                value={value}
                onChange={handleSelectChange}
                placeholder={field.description || field.placeholder}
              />
            </>
          );
        } else {
          return (
            <StyledSelect $variant={variant} value={value || ''} onChange={handleChange}>
              <option value="">选择一个选项</option>
              {field.options?.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.name || option.value}
                </option>
              ))}
            </StyledSelect>
          );
        }

      case 'options':
        return (
          <StyledSelect $variant={variant} value={value || ''} onChange={handleChange}>
            <option value="">{variant === 'node' ? 'Select an option' : '选择一个选项'}</option>
            {field.options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.name || option.value}
              </option>
            ))}
          </StyledSelect>
        );

      case 'selectwithdesc':
        // 仅node变体支持
        if (variant !== 'node') return null;
        return (
          <>
            <Label $variant={variant}>{field.displayName}</Label>
            <SelectWithDesc
              datasource={field.options?.map(opt => ({
                value: opt.value,
                text: opt.name || opt.value,
                description: opt.description
              })) || []}
              value={value}
              onChange={(value: string | number) => onChange(field.name, value)}
              placeholder={field.description || field.placeholder}
            />
          </>
        );

      case 'inputselect':
        const smartOptions = field.options?.map(option => 
          typeof option === 'string' ? option : (option.name || option.value)
        ) || [];
        
        if (variant === 'node') {
          return (
            <>
              <Label $variant={variant}>{field.displayName}</Label>
              <InputSelect
                options={smartOptions}
                value={value || ''}
                onChange={handleInputChange}
                placeholder={field.description || field.placeholder}
              />
            </>
          );
        } else {
          return (
            <InputSelect
              value={value || ''}
              onChange={handleInputChange}
              options={smartOptions}
              placeholder={field.placeholder || field.description}
            />
          );
        }

      case 'sqltext':
        // 仅node变体支持
        if (variant !== 'node') return null;
        return (
          <>
            <Label $variant={variant}>{field.displayName}</Label>
            <SQLText
              value={value || ''}
              onChange={(val) => onChange(field.name, val)}
              height="200px"
            />
          </>
        );

      case 'file':
        // 仅node变体支持
        if (variant !== 'node') return null;
        return (
          <>
            <Label $variant={variant}>{field.displayName}</Label>
            <FileUpload
              onChange={handleFileChange}
              placeholder={field.description || field.placeholder || '请选择文件'}
            />
          </>
        );

      case 'note':
        // 仅node变体支持
        if (variant !== 'node') return null;
        return (
          <>
            <Label $variant={variant}>{field.displayName}</Label>
            <Note value={value || ''} />
          </>
        );

      case 'textarea':
        if (variant === 'node') {
          return (
            <>
              <Label $variant={variant}>{field.displayName}</Label>
              <TextArea
                value={value || ''}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => onChange(field.name, e.target.value)}
                placeholder={field.description || field.placeholder}
              />
            </>
          );
        } else {
          return (
            <StyledTextArea
              $variant={variant}
              value={value || ''}
              onChange={handleTextAreaChange}
              placeholder={field.placeholder || field.description}
            />
          );
        }

      default:
        // 默认文本输入
        if (variant === 'node') {
          return (
            <>
              <Label $variant={variant}>{field.displayName}</Label>
              <Input
                type="text"
                value={value || ''}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(field.name, e.target.value)}
                placeholder={field.description || field.placeholder}
              />
            </>
          );
        } else {
          return (
            <StyledTextInput
              $variant={variant}
              type="text"
              value={value || ''}
              onChange={handleChange}
              placeholder={field.placeholder || field.description}
            />
          );
        }
    }
  };

  const shouldShowLabel = field.type !== 'boolean' && 
                         field.controlType !== 'checkbox' && 
                         field.controlType !== 'CheckBox' &&
                         !['jscode', 'cmdcode', 'input', 'string', 'number', 'select', 'selectwithdesc', 'inputselect', 'sqltext', 'file', 'note', 'textarea'].includes(field.controlType || field.type);

  const shouldShowDescription = field.description && 
                               field.type !== 'boolean' && 
                               field.controlType !== 'checkbox' && 
                               field.controlType !== 'CheckBox';

  return (
    <InputContainer $variant={variant}>
      {variant === 'connect' && shouldShowLabel && (
        <Label $variant={variant}>
          {field.displayName}
          {field.required && <Required $variant={variant}>*</Required>}
        </Label>
      )}
      
      {variant === 'connect' && shouldShowDescription && (
        <Description $variant={variant}>{field.description}</Description>
      )}
      
      {renderInput()}
      
      {variant === 'connect' && field.hint && (
        <Description $variant={variant} $isHint style={{ marginTop: '4px' }}>
          {field.hint}
        </Description>
      )}
    </InputContainer>
  );
};