import { createGlobalStyle } from 'styled-components';

export const GlobalThemeStyles = createGlobalStyle`
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  body {
    font-family: 
      "PingFang SC", "Microsoft YaHei", "Hiragino Sans GB", 
      "WenQuanYi Micro Hei", "Noto Sans CJK SC", "Source Han Sans CN",
      -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: ${({ theme }) => theme.colors.primary};
    color: ${({ theme }) => theme.colors.textPrimary};
    transition: background-color 0.3s ease, color 0.3s ease;
    line-height: 1.6;
    overflow-x: hidden;
  }

  /* 液态玻璃效果支持检测 */
  @supports (backdrop-filter: blur(0)) {
    .liquid-glass {
      backdrop-filter: blur(6px) saturate(150%);
      background: ${({ theme }) => theme.mode === 'dark'
        ? 'rgba(15, 23, 42, 0.4)'
        : 'rgba(248, 250, 252, 0.6)'
    };
      border: 1px solid ${({ theme }) => theme.mode === 'dark'
        ? 'rgba(59, 130, 246, 0.2)'
        : 'rgba(59, 130, 246, 0.15)'
    };
    }
  }

  /* 平滑过渡效果 */
  button, .clickable {
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    cursor: pointer;
    
    &:active {
      transform: scale(0.98);
    }
  }

  /* 自定义滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: ${({ theme }) => theme.mode === 'dark'
        ? 'rgba(15, 23, 42, 0.3)'
        : 'rgba(248, 250, 252, 0.3)'
    };
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: ${({ theme }) => theme.mode === 'dark'
        ? 'rgba(59, 130, 246, 0.3)'
        : 'rgba(59, 130, 246, 0.2)'
    };
    border-radius: 4px;
    
    &:hover {
      background: ${({ theme }) => theme.mode === 'dark'
        ? 'rgba(59, 130, 246, 0.5)'
        : 'rgba(59, 130, 246, 0.4)'
    };
    }
  }

  /* 选中文本样式 */
  ::selection {
    background: ${({ theme }) => theme.mode === 'dark'
        ? 'rgba(59, 130, 246, 0.3)'
        : 'rgba(59, 130, 246, 0.2)'
    };
    color: ${({ theme }) => theme.colors.textPrimary};
  }

  /* ReactFlow 控件主题适配 */
  .react-flow__controls {
    background-color: ${({ theme }) => theme.colors.tertiary} !important;
    border: 1px solid ${({ theme }) => theme.colors.border} !important;
    border-radius: 1px !important;
  }

  .react-flow__controls-button {
    background-color: ${({ theme }) => theme.colors.buttonBg} !important;
    color: ${({ theme }) => theme.colors.textPrimary} !important;
    border: none !important;
    border-bottom: 1px solid ${({ theme }) => theme.colors.border} !important;
    
    &:hover {
      background-color: ${({ theme }) => theme.colors.buttonHover} !important;
      color: ${({ theme }) => theme.colors.accent} !important;
    }

    &:last-child {
      border-bottom: none !important;
    }

    svg {
      fill: ${({ theme }) => theme.colors.textPrimary} !important;
    }

    &:hover svg {
      fill: ${({ theme }) => theme.colors.accent} !important;
    }
  }

  /* ReactFlow 小地图主题适配 */
  .react-flow__minimap {
    background-color: ${({ theme }) => theme.colors.cardBg} !important;
    border: 1px solid ${({ theme }) => theme.colors.border} !important;
    border-radius: 8px !important;
  }

  .react-flow__minimap-mask {
    fill: ${({ theme }) => theme.colors.accent}30 !important;
    stroke: ${({ theme }) => theme.colors.accent} !important;
  }

  .react-flow__minimap-node {
    fill: ${({ theme }) => theme.colors.textSecondary} !important;
  }

  /* ReactFlow 背景主题适配 */
  .react-flow__background {
    background-color: ${({ theme }) => theme.colors.primary} !important;
  }

  .react-flow__background svg {
    .react-flow__background-pattern {
      stroke: ${({ theme }) => theme.colors.border} !important;
    }
  }

  /* ReactFlow 画布区域 */
  .react-flow {
    background-color: ${({ theme }) => theme.colors.primary} !important;
  }

  /* ReactFlow 选择框 */
  .react-flow__selection {
    background: ${({ theme }) => theme.colors.accent}20 !important;
    border: 1px solid ${({ theme }) => theme.colors.accent} !important;
  }

  /* ReactFlow 连接线 */
  .react-flow__edge-path {
    stroke: ${({ theme }) => theme.colors.textSecondary} !important;
  }

  .react-flow__edge.selected .react-flow__edge-path {
    stroke: ${({ theme }) => theme.colors.accent} !important;
  }

  /* ReactFlow 节点边框选中状态 */
  .react-flow__node.selected,
  .react-flow__node:focus,
  .react-flow__node:focus-visible {
    outline: none !important;
    outline-offset: 0 !important;
  }

  /* 禁用节点的默认聚焦样式 */
  .react-flow__node {
    outline: none !important;
  }

  /* 禁用节点双击时的选中边框 */
  .react-flow__node.selectable:focus,
  .react-flow__node.selectable:focus-visible {
    outline: none !important;
  }

  /* 自定义缩放显示样式主题适配 */
  .zoom-display {
    background-color: ${({ theme }) => theme.colors.cardBg} !important;
    color: ${({ theme }) => theme.colors.textSecondary} !important;
    border: 1px solid ${({ theme }) => theme.colors.border} !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    
    &:hover {
      background-color: ${({ theme }) => theme.colors.buttonHover} !important;
      color: ${({ theme }) => theme.colors.textPrimary} !important;
    }
  }
  
  /* 禁用原有的媒体查询主题，使用我们的主题系统 */
  @media (prefers-color-scheme: dark) {
    :root {
      --background: ${({ theme }) => theme.colors.primary};
      --foreground: ${({ theme }) => theme.colors.textPrimary};
    }
  }

  /* 响应式设计优化 */
  @media (max-width: 768px) {
    /* 移动设备上的液态玻璃效果优化 */
    .liquid-glass {
      backdrop-filter: blur(4px) saturate(130%);
    }
  }
`; 