import { ILLMConnect, ILLMOverview, ConnectTestResult } from '@repo/common';
import { 
    createApiKeyField, 
    createBaseUrlField, 
    testLLMConnection
} from '../utils/llm-fields';

const ConnectConfig: ILLMOverview = {
    id: 'alibaba',
    name: '阿里云百炼',
    type: 'llm' as const,
    provider: 'alibaba',
    icon: 'alibaba.svg',
    tags: ["domestic"],
    description: '阿里巴巴百炼AI模型连接',
    version: '1.0.0',
    api: { url: 'https://dashscope.aliyuncs.com/compatible-mode/v1/', suffix: '/chat/completions' },
    driver: 'openai',
    about: {
        apiHost: 'https://dashscope.aliyuncs.com/compatible-mode/v1/',
        docUrl: 'https://help.aliyun.com/zh/model-studio/getting-started/',
        modelUrl: 'https://bailian.console.aliyun.com/?tab=model#/model-market',
        getKeyUrl: 'https://dashscope.console.aliyun.com/'
    }
};

export const AlibabaConnect: ILLMConnect = {
    overview: ConnectConfig,
    detail: {
        supportedModels: [
            { id: 'qwen-vl-plus', name: 'qwen-vl-plus', group: 'qwen-vl'},
            { id: 'qwen-coder-plus', name: 'qwen-coder-plus',  group: 'qwen-coder',  },
            { id: 'qwen-turbo', name: 'qwen-turbo',  group: 'qwen-turbo',  },
            { id: 'qwen-plus', name: 'qwen-plus',  group: 'qwen-plus',  },
            { id: 'qwen-max', name: 'qwen-max',  group: 'qwen-max',  }
        ],
        fields: [
            createApiKeyField('sk-...'),
            createBaseUrlField(ConnectConfig.api.url),
        ],
        validateConnection: true,
        connectionTimeout: 10
    },

    async test(config: Record<string, any>, message?: string): Promise<ConnectTestResult> {
        return testLLMConnection(
            ConnectConfig.id,
            config,
            ConnectConfig.api.url+ConnectConfig.api.suffix||'',
            message
        );
    }
}; 