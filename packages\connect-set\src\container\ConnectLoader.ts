import 'reflect-metadata';
import { Container } from 'inversify';
import glob from 'fast-glob';
import path from 'path';
import { createRequire } from 'module';
import { IConnect, AbstractConnectLoader, ConnectRegistry, Symbols } from '@repo/common';

// 创建一个指向项目根目录的 require 函数
const projectRequire = createRequire(process.cwd() + '/index.js');

// 扩展 NodeJS 的 Module 类型
declare module 'module' {
  interface Module {
    _nodeModulePaths: (from: string) => string[];
    _resolveFilename: (request: string, parent: any, isMain: boolean, options: any) => string;
  }
}

/**
 * 具体的文件系统连接加载器实现
 * 专门用于从 connect-set 包中加载连接
 */
export class FileSystemConnectLoader extends AbstractConnectLoader {
  private container: Container;
  private connectSetRoot: string;

  constructor() {
    // 创建依赖注入容器和 ConnectRegistry
    const container = new Container();
    container.bind<ConnectRegistry>(Symbols.ConnectRegistry).to(ConnectRegistry).inSingletonScope();
    const connectRegistry = container.get<ConnectRegistry>(Symbols.ConnectRegistry);
    
    super(connectRegistry);
    this.container = container;
    
    // 设置 connect-set 包的根目录
    this.connectSetRoot = this.resolveConnectSetRoot();
  }

  private resolveConnectSetRoot(): string {
    try {
      console.log('Starting to resolve connect-set package path...');
      console.log('Current working directory:', process.cwd());

      // 使用 __dirname 来定位当前文件位置
      const currentDir = __dirname;
      console.log('Current directory:', currentDir);

      // 尝试多个可能的位置来找到connect-set包
      const possibleRoots = [
        path.resolve(__dirname, '../../..'),     // 从 dist/container 向上3层到connect-set根目录
        path.resolve(__dirname, '../../../..'),  // 从 dist/container 向上4层
        path.resolve(process.cwd(), 'packages/connect-set'),
        path.resolve(process.cwd(), '../connect-set'),
        path.resolve(process.cwd(), '../../packages/connect-set'),
      ];

      for (const root of possibleRoots) {
        console.log('Trying path:', root);
        const testPkgPath = path.join(root, 'package.json');
        if (require('fs').existsSync(testPkgPath)) {
          // 验证这确实是connect-set包
          try {
            const pkg = JSON.parse(require('fs').readFileSync(testPkgPath, 'utf8'));
            console.log('Found package:', pkg.name, 'at', root);
            if (pkg.name === '@repo/connect-set') {
              console.log('✅ Found connect-set package at:', root);
              return root;
            }
          } catch (e) {
            console.log('Failed to read package.json at:', testPkgPath);
          }
        }
      }
      
      throw new Error('Could not find @repo/connect-set package.json in any of the expected locations');
    } catch (e) {
      console.error('Failed to resolve package path:', e);
      // 作为最后的备选方案，假设我们在项目根目录
      const fallback = path.resolve(process.cwd(), 'packages/connect-set');
      console.log('Falling back to:', fallback);
      return fallback;
    }
  }

  private setupModuleResolution(): void {
    try {
      const Module = eval('require')('module'); // 使用 eval 来避免 Next.js 打包时的模块替换
      const originalResolve = Module._resolveFilename;

      // 重写模块解析逻辑
      Module._resolveFilename = (request: string, parent: any, isMain: boolean, options: any): string => {
        if (request === '@repo/common') {
          // 尝试从多个可能的位置解析 @repo/common
          const possiblePaths = [
            path.resolve(this.connectSetRoot, '../../node_modules/@repo/common/dist/index.js'),
            path.resolve(this.connectSetRoot, '../../packages/common/dist/index.js'),
            path.resolve(process.cwd(), 'node_modules/@repo/common/dist/index.js'),
            path.resolve(process.cwd(), 'packages/common/dist/index.js')
          ];

          for (const possiblePath of possiblePaths) {
            try {
              return projectRequire.resolve(possiblePath);
            } catch (e) {
              // 继续尝试下一个路径
            }
          }
        }

        // 对于其他模块，使用原始解析逻辑
        return originalResolve.call(this, request, parent, isMain, options);
      };
    } catch (error) {
      console.warn('Failed to setup module resolution, falling back to default behavior:', error);
      // 如果模块解析设置失败，就跳过这一步，使用默认的模块解析
    }
  }

  async loadConnects(): Promise<void> {
    try {
      this.setupModuleResolution();
      
      // 首先尝试从源码目录加载 TypeScript 文件
      const srcConnectsDir = path.join(this.connectSetRoot, 'src', 'connects');
      console.log('Looking for source connects in:', srcConnectsDir);
      
      // 检查源码目录
      const fs = require('fs');
      if (fs.existsSync(srcConnectsDir)) {
        console.log('Source connects directory exists');
        
        // 寻找 TypeScript 连接文件
        const tsConnectFiles = await glob(['**/*.connect.ts'], {
          cwd: srcConnectsDir,
          absolute: true,
          ignore: ['**/node_modules/**'],
        });
        
        console.log('Found TypeScript connect files:', tsConnectFiles);
        
        if (tsConnectFiles.length > 0) {
          // 如果找到 TypeScript 文件，使用 ts-node 加载它们
          await this.loadTsConnectFiles(tsConnectFiles);
          return;
        }
      }
      
      // 如果没有找到源码文件，回退到编译后的文件
      const connectsDir = path.join(this.connectSetRoot, 'dist', 'connects');
      console.log('Fallback: Looking for compiled connects in:', connectsDir);
      
      if (!fs.existsSync(connectsDir)) {
        console.error(`Connects directory does not exist: ${connectsDir}`);
        // 创建目录
        fs.mkdirSync(connectsDir, { recursive: true });
        console.log('Created connects directory');
      }
      
      // 检查编译后目录
      if (fs.existsSync(connectsDir)) {
        console.log('Compiled connects directory contents:', fs.readdirSync(connectsDir));
      }
      
      const connectFiles = await glob(['**/*.connect.js'], {
        cwd: connectsDir,
        absolute: true,
        ignore: ['**/node_modules/**'],
      });
      
      console.log('Found compiled connect files:', connectFiles);
      
      // 遍历并加载每个连接文件
      for (const filePath of connectFiles) {
        await this.loadConnectFile(filePath);
      }
    } catch (error) {
      // 记录整体初始化失败
      if (error instanceof Error) {
        console.error('连接加载器初始化失败:', error.message);
      }
      throw error; // 重新抛出错误，因为这是致命错误
    }
  }

  private async loadTsConnectFiles(tsFiles: string[]): Promise<void> {
    console.log('Loading TypeScript connect files...');
    
    for (const filePath of tsFiles) {
      try {
        // 使用 require 直接加载 TypeScript 文件
        // 在运行时，这些文件应该已经被 ts-node 或类似工具编译
        delete require.cache[filePath]; // 清除缓存
        const connectModule = require(filePath);
        
        console.log('Loaded module exports:', Object.keys(connectModule));
        
        // 查找导出的连接类
        let foundConnects = 0;
        
        // 检查 default 导出
        if (connectModule.default && this.isValidConnectClass(connectModule.default)) {
          await this.registerConnectClass(connectModule.default, filePath);
          foundConnects++;
        }
        
        // 检查命名导出
        for (const [exportName, exportValue] of Object.entries(connectModule)) {
          if (exportName !== 'default' && this.isValidConnectClass(exportValue as any)) {
            await this.registerConnectClass(exportValue as new () => IConnect, filePath);
            foundConnects++;
          }
        }
        
        console.log(`Found ${foundConnects} connects in ${filePath}`);
        
      } catch (error) {
        console.error(`Failed to load TypeScript connect file ${filePath}:`, error);
      }
    }
  }

  private async loadConnectFile(filePath: string): Promise<void> {
    try {
      // 使用 require 动态加载连接模块
      const connectModule = require(filePath);

      // 过滤出符合 IConnect 接口的导出类
      const exportedClasses = Object.values(connectModule).filter(
        (exp): exp is new () => IConnect =>
          typeof exp === 'function' &&
          exp.prototype &&
          this.isValidConnectClass(exp as new () => any)
      );

      // 将找到的连接类注册到容器中
      for (const ConnectClass of exportedClasses) {
        await this.registerConnectClass(ConnectClass, filePath);
      }
    } catch (error) {
      // 记录单个连接加载失败，但继续处理其他连接
      if (error instanceof Error) {
        console.error(`连接加载失败 (${filePath}):`, error.message);
      }
    }
  }

  private isValidConnectClass(ConnectClass: new () => any): boolean {
    try {
      const instance = new ConnectClass();
      return 'detail' in instance && 'connect' in instance;
    } catch {
      return false;
    }
  }

  private async registerConnectClass(ConnectClass: new () => IConnect, filePath: string): Promise<void> {
    try {
      const instance = new ConnectClass();
      if (!instance.detail) {
        console.warn(`Skipping connect class ${ConnectClass.name}: missing required 'detail' property`);
        return;
      }
      
      this.container.bind<IConnect>(Symbols.ConnectType).to(ConnectClass);
      // 使用 connectRegistry 直接注册，而不是调用 this.registerConnect
      this.connectRegistry.registerConnect(instance);
      console.log(`✅ Successfully registered connect: ${instance.connect.id}`);
    } catch (error) {
      console.error(`❌ Failed to register connect class ${ConnectClass.name || 'unknown'} from ${filePath}:`, error);
    }
  }
}

// 为了向后兼容，保留原来的 ConnectLoader 类名
export class ConnectLoader extends FileSystemConnectLoader {
  constructor() {
    super();
  }
} 