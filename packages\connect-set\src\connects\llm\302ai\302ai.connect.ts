import { ILLMConnect, ConnectTestResult, ILLMOverview } from '@repo/common';
import { LLMTester } from '../utils/llm-tester';
import { 
    createApi<PERSON>eyField, 
    createBaseUrlField, 
    testLLMConnection
} from '../utils/llm-fields';

const ConnectConfig: ILLMOverview = {
    id: '302ai',
    name: '302AI',
    type: 'llm',
    provider: 'custom',
    icon: '302ai.svg',
    description: '302AI聚合平台连接',
    version: '1.0.0',
    api: { url: 'https://api.302.ai', suffix: '/v1/chat/completions' },
    driver: 'openai',
    about: {
        apiHost: 'https://api.302.ai',
        docUrl: 'https://302ai.apifox.cn/api-*********',
        modelUrl: 'https://302.ai/pricing/',
        getKeyUrl: 'https://302.ai/account/settings/api'
    }    
}   
/**
 * 302AI LLM 连接定义
 */
export const AI302Connect: ILLMConnect = {
    overview: ConnectConfig,
    detail: {
        supportedModels: [
            {id: 'deepseek-chat', name: 'deepseek-chat',group: 'DeepSeek'},
            {id: 'deepseek-reasoner', name: 'deepseek-reasoner',group: 'DeepSeek'},
            {id: 'chatgpt-4o-latest', name: 'chatgpt-4o-latest',group: 'OpenAI'},
            {id: 'gpt-4.1', name: 'gpt-4.1',group: 'OpenAI'},
            {id: 'o3', name: 'o3',group: 'OpenAI'},
            {id: 'o4-mini', name: 'o4-mini',group: 'OpenAI'},
            {id: 'qwen3-235b-a22b', name: 'qwen3-235b-a22b',group: 'Qwen'},
            {id: 'gemini-2.5-flash-preview-05-20', name: 'gemini-2.5-flash-preview-05-20',group: 'Gemini'},
            {id: 'gemini-2.5-pro-preview-06-05', name: 'gemini-2.5-pro-preview-06-05',group: 'Gemini'},
            {id: 'claude-sonnet-4-********', name: 'claude-sonnet-4-********',group: 'Anthropic'},
            {id: 'claude-opus-4-********', name: 'claude-opus-4-********',group: 'Anthropic'},
            {id: 'jina-clip-v2', name: 'jina-clip-v2',group: 'Jina AI'},
            {id: 'jina-reranker-m0', name: 'jina-reranker-m0',group: 'Jina AI'}
        ],
        fields: [
            createApiKeyField(),
            createBaseUrlField(ConnectConfig.api.url)
        ],
        validateConnection: true,
        connectionTimeout: 10
    },

    async test(config: Record<string, any>, message?: string): Promise<ConnectTestResult> {
        try {
            // 验证必填字段
            if (!config.apiKey) {
                return {
                    success: false,
                    message: '缺少必填字段: apiKey'
                };
            }

            if (!config.model) {
                return {
                    success: false,
                    message: '缺少必填字段: model'
                };
            }

            // 使用通用测试器
            return await LLMTester.testConnection('302ai', {
                apiKey: config.apiKey,
                baseUrl: config.baseUrl || ConnectConfig.api.url+ConnectConfig.api.suffix||'',
                model: config.model,
                timeout: (config.timeout || 30) * 1000
            }, message);

        } catch (error) {
            return {
                success: false,
                message: `连接失败: ${error instanceof Error ? error.message : String(error)}`
            };
        }
    }
}; 