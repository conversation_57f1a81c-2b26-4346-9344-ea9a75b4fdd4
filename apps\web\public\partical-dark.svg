<svg xmlns="http://www.w3.org/2000/svg" width="400" height="400" viewBox="0 0 400 400">
  <defs>
    <radialGradient id="glow-gradient-dark" cx="50%" cy="50%" r="60%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#00FFFF" stop-opacity="0.8"/>
      <stop offset="40%" stop-color="#8000FF" stop-opacity="0.6"/>
      <stop offset="70%" stop-color="#4D00FF" stop-opacity="0.4"/>
      <stop offset="100%" stop-color="#0000FF" stop-opacity="0"/>
    </radialGradient>
    <filter id="blur-dark" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="20"/>
    </filter>
  </defs>
  <rect width="100%" height="100%" fill="#121212"/>
  <circle cx="200" cy="200" r="150" fill="url(#glow-gradient-dark)" filter="url(#blur-dark)" opacity="0.9"/>
</svg>