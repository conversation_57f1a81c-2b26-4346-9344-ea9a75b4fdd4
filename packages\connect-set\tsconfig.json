{"extends": "../typescript-config/base.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "moduleResolution": "NodeNext", "target": "ES6", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@repo/*": ["../packages/*"]}}, "include": ["src/**/*"], "exclude": ["dist", "node_modules"]}