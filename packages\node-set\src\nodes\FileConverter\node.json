{"detail": {"displayName": "File Converter", "name": "fileConverter", "icon": "file:fileConverter.svg", "group": ["transform"], "version": 1, "description": "Convert data to various file formats", "defaults": {"name": "File Converter"}, "inputs": ["main"], "outputs": ["main"], "fields": [{"displayName": "Operation", "name": "operation", "type": "string", "controlType": "selectwithdesc", "options": [{"name": "Convert to JSON", "value": "to<PERSON><PERSON>", "description": "Convert input data to JSON file"}, {"name": "Convert to Text", "value": "toText", "description": "Convert input data to text file"}, {"name": "Convert to CSV", "value": "to<PERSON>v", "description": "Convert input data to CSV file"}, {"name": "Base64 to File", "value": "base64ToFile", "description": "Convert base64 string to file"}], "default": "to<PERSON><PERSON>"}, {"displayName": "Source Property", "name": "sourceProperty", "type": "string", "controlType": "input", "default": "data", "required": true, "description": "The property name containing the data to convert", "displayOptions": {"show": {"operation": ["to<PERSON><PERSON>", "toText", "to<PERSON>v"]}}}, {"displayName": "Base64 Property", "name": "base64Property", "type": "string", "controlType": "input", "default": "data", "required": true, "description": "The property name containing the base64 string", "displayOptions": {"show": {"operation": ["base64ToFile"]}}}, {"displayName": "Output File Name", "name": "fileName", "type": "string", "controlType": "input", "default": "output", "description": "Name of the output file (without extension)"}, {"displayName": "Output Binary Property", "name": "binaryPropertyName", "type": "string", "controlType": "input", "default": "data", "description": "Name of the binary property to store the file"}, {"displayName": "Encoding", "name": "encoding", "type": "string", "controlType": "select", "options": [{"name": "UTF-8", "value": "utf8"}, {"name": "ASCII", "value": "ascii"}, {"name": "Base64", "value": "base64"}, {"name": "Hex", "value": "hex"}], "default": "utf8", "description": "Text encoding for the output file", "displayOptions": {"show": {"operation": ["to<PERSON><PERSON>", "toText", "to<PERSON>v"]}}}]}}