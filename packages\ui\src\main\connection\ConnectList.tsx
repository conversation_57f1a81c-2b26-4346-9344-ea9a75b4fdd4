import React, { useState, useMemo } from 'react';
import styled from 'styled-components';

import {
  GlassListCards,
  ListCardButtons,
  ListCardIcons
} from '../home/<USER>';
import { ToolbarControls } from '../chat/ToolbarControls';
import { useLiquidConfirm } from '../../components/basic/LiquidConfirm';
import { useToast, ToastManager } from '../../components/basic/LiquidToast';

// Models 标签容器
const ModelsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 6px;
`;

// 单个 Model 标签
const ModelTag = styled.span`
  background: rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 10px;
  color: rgba(147, 197, 253, 0.9);
  font-weight: 500;
  backdrop-filter: blur(4px);
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

// Models 标题
const ModelsLabel = styled.span`
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  margin-right: 6px;
  font-weight: 500;
`;

interface ConnectConfig {
  id: string;
  name: string;
  ctype: string;
  mtype: string; // 添加 mtype 字段，这是数据库中实际的类型字段
  configinfo: string;
  createdtime: Date;
  updatedtime: Date;
  creator: string | null;
}

interface ConnectListProps {
  connects: ConnectConfig[];
  activeTab: string;
  categories: Array<{
    id: string;
    name: string;
    description: string;
    type: string;
  }>;
  onConnectClick?: (connectId: string) => void;
  onDeleteConnect?: (connectId: string) => void;
  onEditConnect?: (connect: ConnectConfig) => void;
  onDebugConnect?: (connect: ConnectConfig) => void;
}

export const ConnectList: React.FC<ConnectListProps> = ({
  connects,
  activeTab,
  categories,
  onConnectClick,
  onDeleteConnect,
  onEditConnect,
  onDebugConnect,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('last-updated');

  // 根据activeTab过滤连接
  const filteredConnects = useMemo(() => {
    let filtered = connects;

    // 如果不是"全部"标签，则按类型过滤
    if (activeTab !== 'all') {
      filtered = connects.filter(connect => connect.mtype === activeTab);
    }

    // 根据搜索词过滤
    if (searchTerm) {
      filtered = filtered.filter(connect =>
        connect.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        connect.mtype.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // 排序
    if (sortBy === 'last-updated') {
      filtered = [...filtered].sort((a, b) =>
        new Date(b.updatedtime).getTime() - new Date(a.updatedtime).getTime()
      );
    } else if (sortBy === 'name') {
      filtered = [...filtered].sort((a, b) => a.name.localeCompare(b.name));
    }

    return filtered;
  }, [connects, activeTab, searchTerm, sortBy]);

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 工具栏区域 */}
      <ToolbarControls
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        searchPlaceholder="搜索连接配置..."
        sortBy={sortBy}
        onSortChange={() => setSortBy(sortBy === 'last-updated' ? 'name' : 'last-updated')}
      />

      {/* 连接配置卡片区域 */}
      <div style={{
        flex: 1,
        padding: '0 30px',
        overflowY: 'auto',
        marginBottom: '20px'
      }}>
        {filteredConnects.length === 0 ? (
          <div style={{
            textAlign: 'center',
            padding: '60px 20px',
            color: '#666'
          }}>
            {activeTab === 'all' ? '暂无连接配置' : `暂无 ${categories.find(c => c.type === activeTab)?.name || activeTab} 类型的连接配置`}
          </div>
        ) : (
          filteredConnects.map((connect) => (
            <ConnectCard
              key={connect.id}
              connect={connect}
              categories={categories}
              onConnectClick={onConnectClick}
              onDeleteConnect={onDeleteConnect}
              onEditConnect={onEditConnect}
              onDebugConnect={onDebugConnect}
            />
          ))
        )}
      </div>
    </div>
  );
};

// 连接配置卡片组件
interface ConnectCardProps {
  connect: ConnectConfig;
  categories: Array<{
    id: string;
    name: string;
    description: string;
    type: string;
  }>;
  onConnectClick?: (connectId: string) => void;
  onDeleteConnect?: (connectId: string) => void;
  onEditConnect?: (connect: ConnectConfig) => void;
  onDebugConnect?: (connect: ConnectConfig) => void;
}

const ConnectCard: React.FC<ConnectCardProps> = ({
  connect,
  categories,
  onConnectClick,
  onDeleteConnect,
  onEditConnect,
  onDebugConnect,
}) => {
  const { showConfirm, ConfirmComponent } = useLiquidConfirm();
  const { showSuccess, showError, toasts, removeToast } = useToast();
  const categoryName = categories.find(c => c.type === connect.mtype)?.name || connect.mtype;

  // 获取图标路径
  const getIconPath = (ctype: string) => {
    return `/connect-icons/${ctype}.svg`;
  };

  // 解析配置信息中的models（仅对LLM类型）
  // const getModels = (): string[] => {
  //   console.log(`🔍 开始解析 "${connect.name}" 的models:`, {
  //     mtype: connect.mtype,
  //     configinfo: connect.configinfo
  //   });

  //   if (connect.mtype !== 'llm') {
  //     console.log('❌ 不是LLM类型，跳过');
  //     return [];
  //   }
    
  //   try {
  //     // 检查configinfo是否为有效字符串
  //     if (!connect.configinfo || typeof connect.configinfo !== 'string') {
  //       console.warn('❌ 配置信息为空或非字符串类型:', connect.configinfo);
  //       return [];
  //     }

  //     // 检查是否为"undefined"字符串或其他无效值
  //     const trimmedConfig = connect.configinfo.trim();
  //     console.log('📝 Trimmed config:', trimmedConfig);
      
  //     if (trimmedConfig === 'undefined' || trimmedConfig === 'null' || trimmedConfig === '' || trimmedConfig === '{}') {
  //       console.warn('❌ 配置信息为无效值:', trimmedConfig);
  //       return [];
  //     }

  //     // 修改加密数据检测逻辑，只检查是否以{开头
  //     if (!trimmedConfig.startsWith('{')) {
  //       console.warn('❌ 配置信息不像JSON格式（不以{开头）:', trimmedConfig.substring(0, 50));
  //       return [];
  //     }

  //     console.log('📜 开始解析JSON...');
  //     const config = JSON.parse(trimmedConfig);
  //     console.log('✅ JSON解析成功:', config);
      
  //     // 检查是否有models字段
  //     if (config.models) {
  //       console.log('📋 发现models字段:', config.models, '类型:', Array.isArray(config.models));
        
  //       if (Array.isArray(config.models)) {
  //         // 过滤掉空字符串和非字符串类型的模型名
  //         const validModels = config.models
  //           .filter((model: any) => {
  //             const isValid = typeof model === 'string' && model.trim() !== '';
  //             console.log(`🔍 验证模型 "${model}":`, isValid);
  //             return isValid;
  //           })
  //           .slice(0, 5); // 最多显示5个模型
          
  //         console.log('✅ 有效模型列表:', validModels);
  //         return validModels;
  //       } else {
  //         console.warn('❌ models字段不是数组:', typeof config.models);
  //       }
  //     } else {
  //       console.warn('❌ 没有找到models字段，配置键:', Object.keys(config));
  //     }
      
  //   } catch (error) {
  //     console.error('❌ 解析配置信息失败:', {
  //       connectName: connect.name,
  //       configinfo: connect.configinfo,
  //       error: error
  //     });
  //   }
    
  //   console.log('🚫 返回空数组');
  //   return [];
  // };

  // const models = getModels();
  
  // 开发阶段调试信息
  // if (connect.mtype === 'llm') {
  //   console.log(`🔍 LLM连接 "${connect.name}" 配置调试:`, {
  //     configinfo: connect.configinfo,
  //     configinfoType: typeof connect.configinfo,
  //     configinfoLength: connect.configinfo?.length,
  //     configinfoPreview: connect.configinfo?.substring(0, 200) + '...',
  //     modelsFound: models.length,
  //     models: models,
  //     parsedConfigKeys: (() => {
  //       try {
  //         if (connect.configinfo && typeof connect.configinfo === 'string') {
  //           const parsed = JSON.parse(connect.configinfo.trim());
  //           return Object.keys(parsed);
  //         }
  //       } catch (e) {
  //         return ['解析失败'];
  //       }
  //       return ['无效数据'];
  //     })()
  //   });
  // }

  return (
    <>
      {ConfirmComponent}
      <ToastManager toasts={toasts} onRemove={removeToast} />
      <GlassListCards>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <div style={{ display: 'flex', alignItems: 'center', flex: 1, gap: '16px' }}>
          {/* 连接图标 */}
          <ListCardIcons>
            <img
              src={getIconPath(connect.ctype)}
              alt={categoryName}
              style={{ width: '38px', height: '38px' }}
              onError={(e) => {
                // 如果图标加载失败，显示默认图标
                const img = e.target as HTMLImageElement;
                const container = img.parentElement;
                if (container) {
                  container.innerHTML = '<span style="fontSize: 24px; color: rgba(255, 255, 255, 0.7)">🔗</span>';
                }
              }}
            />
          </ListCardIcons>
          {/* 连接信息 */}
          <div style={{ flex: 1 }}>
            <h4 style={{
              margin: '0 0 5px 0',
              fontSize: '15px',
              fontWeight: '600',
              color: '#fff'
            }}>
              {connect.name}
            </h4>
            <div style={{
              fontSize: '13px',
              color: 'rgba(255, 255, 255, 0.7)',
              marginBottom: '8px'
            }}>
              类型：{categoryName}
            </div>
            <div style={{
              fontSize: '12px',
              color: 'rgba(255, 255, 255, 0.5)'
            }}>
              创建时间：{new Date(connect.createdtime).toLocaleDateString()}
              {connect.creator && ` | 创建者：${connect.creator}`}
            </div>
            
            {/* 如果是LLM类型，显示models信息或调试信息 
            {connect.mtype === 'llm' && (
              <div style={{ marginTop: '8px' }}>
                {models.length > 0 ? (
                  <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
                    <ModelsLabel>模型:</ModelsLabel>
                    <ModelsContainer>
                      {models.map((model, index) => (
                        <ModelTag key={index} title={model}>
                          {model}
                        </ModelTag>
                      ))}
                    </ModelsContainer>
                  </div>
                ) : (
                  <div style={{ 
                    fontSize: '11px', 
                    color: 'rgba(255, 165, 0, 0.8)',
                    fontStyle: 'italic'
                  }}>
                    模型: 暂无配置 (调试: 检查configinfo字段)
                  </div>
                )}
              </div>
            )}*/}
          </div>
        </div>

        <div style={{ display: 'flex', gap: '8px' }}>
          <ListCardButtons onClick={(e) => {
            e.stopPropagation();
            onDebugConnect?.(connect);
          }}>
            ❅ 调试
          </ListCardButtons>
          {/* <button
            onClick={(e) => {
              e.stopPropagation();
              onDebugConnect?.(connect);
            }}
            style={{
              background: 'rgba(255, 255, 255, 0.08)',
              border: '1px solid rgba(255, 255, 255, 0.15)',
              borderRadius: '6px',
              padding: '2px 14px',
              color: 'rgba(255, 255, 255, 0.9)',
              fontSize: '12px',
              fontWeight: '500',
              cursor: 'pointer',
              backdropFilter: 'blur(8px)',
              transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.12)';
              e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.25)';
              e.currentTarget.style.transform = 'translateY(-1px)';
              e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
              e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.15)';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
            }}
          >
            <RiLoader2Line /> 调试
          </button> */}
          <ListCardButtons onClick={(e) => {
            e.stopPropagation();
            onEditConnect?.(connect);
          }}>
            ✏️ 编辑
          </ListCardButtons>
          {/* <button
            onClick={(e) => {
              e.stopPropagation();
              onEditConnect?.(connect);
            }}
            style={{
              background: 'rgba(255, 255, 255, 0.08)',
              border: '1px solid rgba(255, 255, 255, 0.15)',
              borderRadius: '4px',
              padding: '2px 14px',
              color: 'rgba(255, 255, 255, 0.9)',
              fontSize: '12px',
              cursor: 'pointer',
              backdropFilter: 'blur(8px)',
              transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.12)';
              e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.25)';
              e.currentTarget.style.transform = 'translateY(-1px)';
              e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
              e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.15)';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
            }}
          >
            ✏️ 编辑
          </button> */}
          <ListCardButtons onClick={async (e) => {
            e.stopPropagation();
            const confirmed = await showConfirm({
              title: '删除连接',
              message: `确定要删除连接 "${connect.name}" 吗？\n\n此操作不可撤销。`,
              confirmText: '删除',
              cancelText: '取消',
              triggerElement: e.currentTarget as HTMLElement,
              positioning: 'below-trigger'
            });
            if (confirmed) {
              try {
                const response = await fetch(`/api/connect-configs/${connect.id}`, {
                  method: 'DELETE',
                });
                
                const result = await response.json();
                
                if (result.success) {
                  showSuccess('删除成功', result.message || '连接配置删除成功');
                  onDeleteConnect?.(connect.id);
                } else {
                  if (result.error === 'REFERENCED_BY_AGENTS') {
                    showError('删除失败', result.message);
                  } else {
                    showError('删除失败', result.error || '删除连接配置失败');
                  }
                }
              } catch (error) {
                console.error('删除连接配置时发生错误:', error);
                showError('删除失败', '网络错误，请稍后重试');
              }
            }
          }}>
            🗑️ 删除
          </ListCardButtons>
          {/* <button
            onClick={(e) => {
              e.stopPropagation();
              if (confirm(`确定要删除连接 "${connect.name}" 吗？`)) {
                onDeleteConnect?.(connect.id);
              }
            }}
            style={{
              background: 'rgba(255, 255, 255, 0.06)',
              border: '1px solid rgba(255, 255, 255, 0.12)',
              borderRadius: '8px',
              padding: '8px 14px',
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: '12px',
              fontWeight: '500',
              cursor: 'pointer',
              backdropFilter: 'blur(8px)',
              transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'rgba(239, 68, 68, 0.15)';
              e.currentTarget.style.borderColor = 'rgba(239, 68, 68, 0.3)';
              e.currentTarget.style.color = '#ff6b6b';
              e.currentTarget.style.transform = 'translateY(-1px)';
              e.currentTarget.style.boxShadow = '0 4px 12px rgba(239, 68, 68, 0.2)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.06)';
              e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.12)';
              e.currentTarget.style.color = 'rgba(255, 255, 255, 0.7)';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
            }}
          >
            🗑️ 删除
          </button> */}
        </div>
      </div>
    </GlassListCards>
    </>
  );
};