// MySQL 数据库连接
import MySQLConnect from './mysql/mysql.connect';

// PostgreSQL 数据库连接
import PostgreSQLConnect from './postgresql/postgresql.connect';

// SQL Server 数据库连接
import SQLServerConnect from './sqlserver/sqlserver.connect';

// Oracle 数据库连接
import OracleConnect from './oracle/oracle.connect';

// DB2 数据库连接
import DB2Connect from './db2/db2.connect';

// 人大金仓 数据库连接
import KingbaseConnect from './kingbase/kingbase.connect';

// 达梦数据库连接
import DamengConnect from './dameng/dameng.connect';

// 导出所有数据库连接
export {
    MySQLConnect,
    PostgreSQLConnect,
    SQLServerConnect,
    OracleConnect,
    DB2Connect,
    KingbaseConnect,
    DamengConnect
};

// 默认导出所有数据库连接数组
export default [
    MySQLConnect,
    PostgreSQLConnect,
    SQLServerConnect,
    OracleConnect,
    DB2Connect,
    KingbaseConnect,
    DamengConnect
]; 