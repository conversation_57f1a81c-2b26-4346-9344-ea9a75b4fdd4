// 统一的控件样式常量
export const CONTROL_STYLES = {
  // 颜色主题
  colors: {
    primary: '#33C2EE',
    primaryHover: '#2AA8CC', 
    border: '#ddd',
    borderHover: '#bbb',
    borderFocus: '#33C2EE',
    background: 'white',
    text: '#333',
    textSecondary: '#666',
    textDisabled: '#999',
    placeholder: '#999',
    disabled: '#f0f0f0',
    error: '#ff4d4f',
    success: '#52c41a',
    warning: '#faad14',
  },

  // 字体大小
  fontSize: {
    small: '12px',
    default: '14px', 
    large: '16px',
  },

  // 边框圆角
  borderRadius: {
    small: '2px',
    default: '4px',
    large: '6px',
  },

  // 间距
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '12px',
    lg: '16px',
    xl: '20px',
  },

  // 阴影效果
  shadows: {
    focus: '0 0 0 2px rgba(51, 194, 238, 0.1)',
    dropdown: '0 4px 12px rgba(0, 0, 0, 0.1)',
  },

  // 过渡动画
  transitions: {
    fast: '0.1s ease',
    normal: '0.2s ease',
    slow: '0.3s ease',
  },
} as const;

// 通用输入框样式
export const inputBaseStyles = `
  width: 100%;
  padding: ${CONTROL_STYLES.spacing.sm} ${CONTROL_STYLES.spacing.md};
  border: 1px solid ${CONTROL_STYLES.colors.border};
  border-radius: ${CONTROL_STYLES.borderRadius.default};
  font-size: ${CONTROL_STYLES.fontSize.default};
  color: ${CONTROL_STYLES.colors.text};
  background: ${CONTROL_STYLES.colors.background};
  transition: ${CONTROL_STYLES.transitions.normal};
  outline: none;

  &::placeholder {
    color: ${CONTROL_STYLES.colors.placeholder};
    font-size: ${CONTROL_STYLES.fontSize.default};
  }

  &:hover {
    border-color: ${CONTROL_STYLES.colors.borderHover};
  }

  &:focus {
    border-color: ${CONTROL_STYLES.colors.borderFocus};
    box-shadow: ${CONTROL_STYLES.shadows.focus};
  }

  &:disabled {
    background: ${CONTROL_STYLES.colors.disabled};
    color: ${CONTROL_STYLES.colors.textDisabled};
    cursor: not-allowed;
    
    &::placeholder {
      color: ${CONTROL_STYLES.colors.textDisabled};
    }
  }
`;

// 通用标签样式
export const labelBaseStyles = `
  display: block;
  margin-bottom: ${CONTROL_STYLES.spacing.sm};
  font-size: ${CONTROL_STYLES.fontSize.default};
  color: ${CONTROL_STYLES.colors.text};
  font-weight: 500;
`;

// 通用描述文本样式
export const descriptionBaseStyles = `
  margin-top: ${CONTROL_STYLES.spacing.xs};
  font-size: ${CONTROL_STYLES.fontSize.small};
  color: ${CONTROL_STYLES.colors.textSecondary};
  line-height: 1.4;
`; 