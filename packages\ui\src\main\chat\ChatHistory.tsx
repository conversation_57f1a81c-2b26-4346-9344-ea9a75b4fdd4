import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { HiChatBubbleLeftRight } from 'react-icons/hi2';
import { BiTime } from 'react-icons/bi';
import { FaListUl } from "react-icons/fa6";

const ChatHistoryContainer = styled.div`
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  display: flex;
  flex-direction: column;
  padding: 0;
  box-sizing: border-box;
`;

const HistoryHeader = styled.div`
  padding: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  font-size: 14px;
  svg {
    margin-bottom: -2px;
  }
`;

const HistoryList = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 0px;
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 2px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(76, 76, 77, 0.4);
    border-radius: 2px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.6);
  }
`;

const HistoryItem = styled.div<{ $isActive?: boolean }>`
  padding: 12px;
  //margin-bottom: 8px;
  background: ${props => props.$isActive
    ? 'rgba(76, 175, 80, 0.2)'
    : 'rgba(255, 255, 255, 0.05)'};
  border-bottom: 1px solid ${props => props.$isActive
    ? 'rgba(76, 175, 80, 0.4)'
    : 'rgba(255, 255, 255, 0.1)'};*/
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  
  &:hover {
    background: ${props => props.$isActive
    ? 'rgba(76, 175, 80, 0.3)'
    : 'rgba(255, 255, 255, 0.1)'};
    border-color: ${props => props.$isActive
    ? 'rgba(76, 175, 80, 0.6)'
    : 'rgba(255, 255, 255, 0.2)'};
  }
`;

const ItemHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
`;

const ItemTitle = styled.div`
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  flex: 1;
  margin-right: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  
  .title-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    max-width: 250px;
  }
`;

const ItemTime = styled.div`
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
`;

const ItemPreview = styled.div`
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  line-height: 1.4;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: rgba(255, 255, 255, 0.6);
`;

const EmptyContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 240px;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  padding: 20px;
  p {
      font-size:13px;
  }
`;

interface Thread {
  threadId: string;
  agentId: string;
  content: string;
  createdAt: Date;
}

interface ChatHistoryProps {
  agentId?: string;
  activeThreadId?: string;
  onThreadSelect?: (threadId: string) => void;
}

export const ChatHistory: React.FC<ChatHistoryProps> = ({
  agentId,
  activeThreadId,
  onThreadSelect
}) => {
  const [threads, setThreads] = useState<Thread[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取历史会话列表
  const fetchThreads = useCallback(async () => {
    if (!agentId) return;

    setLoading(true);
    setError(null);

    try {
      // 直接调用API获取threads
      const response = await fetch(`/api/agentic/threads?agentId=${agentId}&limit=50`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch threads: ${response.status} ${response.statusText}`);
      }
      
      const threadsData = await response.json();
      setThreads(threadsData);
    } catch (err) {
      console.error('Error fetching threads:', err);
      setError(err instanceof Error ? err.message : 'Failed to load history');
    } finally {
      setLoading(false);
    }
  }, [agentId]);

  // 由于新的数据结构没有thread id，暂时移除删除功能

  // 格式化时间
  const formatTime = (timeStr: string) => {
    const date = new Date(timeStr);
    const now = new Date();
    const diff = now.getTime() - date.getTime();

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else if (diff < 604800000) { // 1周内
      return `${Math.floor(diff / 86400000)}天前`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // 生成会话标题
  const getThreadTitle = (thread: Thread) => {
    return thread.content || '空消息';
  };

  // 点击会话项
  const handleThreadClick = (thread: Thread) => {
    // 使用threadId来标识会话
    onThreadSelect?.(thread.threadId);
  };

  // 当agentId变化时重新获取数据
  useEffect(() => {
    fetchThreads();
  }, [fetchThreads]);

  if (!agentId) {
    return (
      <ChatHistoryContainer>
        <EmptyContainer>
          <HiChatBubbleLeftRight size={48} />
          <p>请选择一个智能体</p>
        </EmptyContainer>
      </ChatHistoryContainer>
    );
  }

  return (
    <ChatHistoryContainer>
      <HistoryHeader>
        历史会话 
        <FaListUl size={13} color={'#bfbfbf'} />
      </HistoryHeader>

      <HistoryList>
        {loading && (
          <LoadingContainer>
            <p>加载中...</p>
          </LoadingContainer>
        )}

        {error && (
          <EmptyContainer>
            <p>加载失败: {error}</p>
            <button onClick={fetchThreads}>重试</button>
          </EmptyContainer>
        )}

        {!loading && !error && threads.length === 0 && (
          <EmptyContainer>
            <HiChatBubbleLeftRight size={36} />
            <p>暂无历史会话</p>
            <p>开始新的对话吧！</p>
          </EmptyContainer>
        )}

        {!loading && !error && threads.map((thread) => (
          <HistoryItem
            key={thread.threadId}
            $isActive={activeThreadId === thread.threadId}
            onClick={() => handleThreadClick(thread)}
          >
            <ItemHeader>
              <ItemTitle>
                <HiChatBubbleLeftRight size={16} />
                <span className="title-text" title={getThreadTitle(thread)}>
                  {getThreadTitle(thread)}
                </span>
              </ItemTitle>
            </ItemHeader>

            <ItemTime>
              <BiTime size={12} />
              {formatTime(thread.createdAt.toString())}
            </ItemTime>
          </HistoryItem>
        ))}
      </HistoryList>
    </ChatHistoryContainer>
  );
};