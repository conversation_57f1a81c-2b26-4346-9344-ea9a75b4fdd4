import React, { useState } from 'react';
import styled from 'styled-components';
import { avatarOptions, getAvatarIcon } from '../../utils/avatarUtils';
import {
  ModalBackdrop,
  PremiumModalContainer,
  PremiumTitleDesc,
  ModalHeader,
  ModalContent,
  CloseButton,
  FormField,
  FormLabel,
  FormInput,
  FormButton,
  FormButtonGroup,
  PremiumFormButton,
  LiquidToast,
  AvatarPanel
} from '../basic';
import {
  PremiumEmptyModelMessage
} from '../shared/ModelSectionComponents';
import { ModelSelectorModal } from '../forms/connect/ModelSelectorModal';
import { AddModelModal } from '../forms/connect/AddModelModal';
import { LLMConnectSelectorModal } from './LLMConnectSelectorModal';
import { FaTools } from "react-icons/fa";
import type { ModelInfo } from '@repo/common';

// 左右两列布局容器
const TwoColumnLayout = styled.div`
  display: flex;
  gap: 0px;
  height: 100%;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
`;

const LeftColumn = styled.div`
  flex: 0 0 50%;
  width: 50%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
`;

const RightColumn = styled.div`
  flex: 0 0 50%;
  width: 50%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
`;

// 头像选择区域
const AvatarSection = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 12px 20px;
  position: relative;
  display: flex;
  align-items: center;
  gap: 20px;
`;

const SelectedAvatar = styled.div`
  width: 70px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
  }
`;

// AvatarPanel 模态窗口
const AvatarPanelModal = styled(ModalBackdrop)`
  z-index: 10000;
`;

export const FormInputNoborder = styled.input`
  width: 100%;
  height: 32px;
  padding: 0 12px;
  border: none;
  border-radius: 0;
  font-size: 16px;
  background: transparent; /* 完全透明背景 */
  color: ${({ theme }) => theme.mode === 'dark' ? '#e2e8f0' : '#374151'};

  &::placeholder {
    color: ${({ theme }) => theme.mode === 'dark' ? 'rgba(255, 255, 255, 0.5)' : '#9ca3af'};
  }
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
    //box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
     border-bottom: 1px solid ${({ theme }) => theme.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : '#e0e0e0'}; 
  }
`;

const AvatarPanelContainer = styled.div`
  max-width: 800px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
`;

const AvatarPanelCloseButton = styled(CloseButton)`
  color: white;
  font-size: 24px;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

const AvatarPanelActions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 0 0 0;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: 20px;
`;

// 表单区域样式
const FormSection = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 8px 10px;
  display: flex;
  flex-direction: column;
  gap: 3px;
`;

const PremiumFormLabel = styled(FormLabel)`
  color: white;
  font-weight: 600;
  padding: 0px 6px;
`;

// 模型选择区域
const ModelSelectionArea = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 8px 18px;
`;

const ModelTitle = styled.h4`
  color: white;
  font-size: 14px;
  font-weight: 400;
  margin: 0 0 8px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const ModelCardsContainer = styled.div`
  position: relative;
  display: flex;
  gap: 12px;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 4px;
  /* 根据模态框宽度(90vw)计算：右列占一半(45vw) - ModelSelectionArea的左右padding(36px) - TwoColumnLayout的gap一半(12px) */
  width: calc(45vw - 48px);
  
  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    height: 16px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
`;

// GPU优化的模型卡片 - 适配蓝色背景
const ModelCard = styled.div<{ $selected: boolean }>`
  position: relative;
  flex: 0 0 160px;
  width: 160px;
  height: 80px;
  padding: 8px;
  background: ${props => props.$selected
    ? 'rgba(255, 255, 255, 0.4)'
    : 'rgba(255, 255, 255, 0.08)'
  };
  border: 1px solid ${props => props.$selected
    ? 'rgba(255, 255, 255, 0.6)'
    : 'rgba(255, 255, 255, 0.15)'
  };
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.2s ease, background-color 0.2s ease;
  box-shadow: ${props => props.$selected
    ? '0 8px 32px rgba(255, 255, 255, 0.1), 0 0 20px rgba(255, 255, 255, 0.05) inset'
    : 'none'
  };
  
  &:hover {
    background: ${props => props.$selected
    ? 'rgba(255, 255, 255, 0.5)'
    : 'rgba(255, 255, 255, 0.2)'
  };
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }
  
  ${props => props.$selected && `
    &::before {
      content: '✔';
      position: absolute;
      top: 8px;
      right: 8px;
      background: rgba(34, 197, 94, 0.9);
      color: white;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 11px;
      font-weight: bold;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      z-index: 1;
    }
  `}
`;

const ModelContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-align: center;
`;

const ModelIcon = styled.div`
  width: 36px;
  height: 36px;
  display: flex;
  border-radius: 50%;
  padding:4px;
  background: #ffffff60;
  border: 1px solid rgba(255, 255, 255, 0.2);
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin: 0 auto;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
`;

const ModelInfo = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
  width: 100%;
  text-align: center;
  margin-top: 8px;
`;

const ModelName = styled.div`
  color: white;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 2px;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
`;

const ModelProvider = styled.div`
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  line-height: 1.2;
`;

const ModelType = styled.div`
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  line-height: 1.2;
`;



// MCP选择区域
const McpSelectionArea = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 8px 20px;
`;

const McpTitle = styled.h4`
  color: white;
  font-size: 14px;
  font-weight: 400;
  margin: 0;
`;

const McpTitleRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const ToolModeSelect = styled.select`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  padding: 2px 12px;
  border-radius: 4px;
  font-size: 12px;
  min-width: 80px;
  &:focus{
    outline: none;
  }
  option {
    background: #0f172a;
    color: white;
    padding: 4px;
  }
  
  option:hover {
    background: #1e293b;
  }
  
  option:checked {
    background: #3b82f6;
  }
`;

const McpCardsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  //max-height: 320px;
  overflow-y: auto;
  padding: 4px;
  align-content: start; /* 让内容从顶部开始排列，避免垂直居中造成的间距 */
  
  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
`;

// GPU优化的MCP卡片
const McpCard = styled.div<{ $selected: boolean }>`
  background: ${props => props.$selected
    ? (props.theme.mode === 'dark'
      ? 'rgba(34, 197, 94, 0.2)'
      : 'rgba(34, 197, 94, 0.1)')
    : (props.theme.mode === 'dark'
      ? 'rgba(255, 255, 255, 0.08)'
      : 'rgba(0, 0, 0, 0.03)')
  };
  border: 2px solid ${props => props.$selected
    ? '#22c55e'
    : (props.theme.mode === 'dark'
      ? 'rgba(255, 255, 255, 0.2)'
      : 'rgba(0, 0, 0, 0.1)')
  };
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: left;
  justify-content: center;
  padding: 8px;
  cursor: pointer;
  transition: border-color 0.2s ease, background-color 0.2s ease;
  position: relative;
  height: 55px;
  
  &:hover {
    background: ${props => props.$selected
    ? (props.theme.mode === 'dark'
      ? 'rgba(34, 197, 94, 0.3)'
      : 'rgba(34, 197, 94, 0.15)')
    : (props.theme.mode === 'dark'
      ? 'rgba(255, 255, 255, 0.12)'
      : 'rgba(0, 0, 0, 0.05)')
  };
    border-color: ${props => props.$selected
    ? '#16a34a'
    : (props.theme.mode === 'dark'
      ? 'rgba(255, 255, 255, 0.4)'
      : 'rgba(0, 0, 0, 0.2)')
  };
  }
  
  ${props => props.$selected && `
    &::before {
      content: '✔';
      position: absolute;
      top: 8px;
      right: 8px;
      background: #22c55e;
      color: white;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 11px;
      font-weight: bold;
    }
  `}
`;

const McpIcon = styled.div`
  width: 12px;
  height: 12px;
  //border-radius: 8px;
  display: flex;
`;

const McpName = styled.h5`
  color: white;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  svg{
    margin:0 6px -2px 0;
    width: 12px;
    height: 12px;
    flex-shrink: 0;
  }
`;

const McpType = styled.p`
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  margin: 2px 0 0 18px;
  line-height: 1.2;
`;

// 自定义选择框样式
const CustomSelect = styled.select`
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  width: 100%;
  
  option {
    background: #0f172a !important;
    color: white !important;
    padding: 8px !important;
  }
  
  option:hover {
    background: #1e293b !important;
  }
  
  option:checked {
    background: #3b82f6 !important;
  }
`;

interface AgentConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave?: (data: any) => void;
  onSaveSuccess?: () => Promise<void>; // 添加保存成功回调
  editMode?: boolean;
  editData?: any;
  onFetchLLMConnects?: () => Promise<any[]>;
  onFetchMcpConfigs?: () => Promise<any[]>;
  // LLM连接创建相关
  onFetchConnects?: () => Promise<any[]>; // 获取连接定义列表
  onFetchConnectDetails?: (connectId: string) => Promise<any>; // 获取连接详情
  onSaveConnect?: (data: any) => Promise<boolean>; // 保存连接，返回boolean表示成功状态
  onTestConnect?: (config: Record<string, any>, message?: string) => Promise<any>; // 测试连接
}

export const AgentConfigModal: React.FC<AgentConfigModalProps> = ({
  isOpen,
  onClose,
  onSave,
  onSaveSuccess,
  editMode = false,
  editData,
  onFetchLLMConnects,
  onFetchMcpConfigs,
  onFetchConnects,
  onFetchConnectDetails,
  onSaveConnect,
  onTestConnect
}) => {
  const [activeTab, setActiveTab] = useState<'quick' | 'json'>('quick');
  const [agentName, setAgentName] = useState('');
  const [agentDescription, setAgentDescription] = useState('');
  const [agentPrompt, setAgentPrompt] = useState('');
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [selectedMcps, setSelectedMcps] = useState<string[]>([]);
  const [toolMode, setToolMode] = useState<string>('function'); // 工具模式状态

  // 头像相关状态
  const [selectedAvatar, setSelectedAvatar] = useState<string>('user'); // 头像key
  const [selectedAvatarColor, setSelectedAvatarColor] = useState<string>('#3B82F6'); // 头像颜色
  const [isAvatarPanelOpen, setIsAvatarPanelOpen] = useState(false);

  // 模型选择相关状态
  const [selectedModels, setSelectedModels] = useState<string[]>([]);
  const [showAddModelModal, setShowAddModelModal] = useState<boolean>(false);
  const [showModelSelector, setShowModelSelector] = useState<boolean>(false);
  const [customModels, setCustomModels] = useState<ModelInfo[]>([]);
  const [showLLMConnectModal, setShowLLMConnectModal] = useState<boolean>(false);
  const [modelSelectionCleared, setModelSelectionCleared] = useState<boolean>(false);

  // 存储从数据库获取的数据
  const [llmConnects, setLlmConnects] = useState<any[]>([]);
  const [mcpConfigs, setMcpConfigs] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' | 'warning' } | null>(null);
  const [systemModels, setSystemModels] = useState<any[]>([]);


  // 获取 LLM 连接配置
  const fetchLLMConnects = async () => {
    if (!onFetchLLMConnects) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const connects = await onFetchLLMConnects();
      setLlmConnects(connects || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取连接配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取 MCP 配置
  const fetchMcpConfigs = async () => {
    if (!onFetchMcpConfigs) {
      return;
    }

    try {
      const configs = await onFetchMcpConfigs();
      setMcpConfigs(configs || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取MCP配置失败');
    }
  };

  // 当模态框打开时获取数据
  React.useEffect(() => {
    if (isOpen) {
      fetchLLMConnects();
      fetchMcpConfigs();
    }
  }, [isOpen]);

  // 监听Escape键关闭头像选择器
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isAvatarPanelOpen) {
        setIsAvatarPanelOpen(false);
      }
    };

    if (isAvatarPanelOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isAvatarPanelOpen]);

  // 编辑模式下初始化数据
  React.useEffect(() => {
    if (editMode && editData) {
      setAgentName(editData.name || '');
      setAgentDescription(editData.description || '');
      setAgentPrompt(editData.prompt || '');
      setModelSelectionCleared(false); // 重置模型清空状态

      // 使用connectid来选中对应的连接
      const connectIdToSelect = editData.connectid || editData.modelId || '';

      // 验证连接是否存在于连接列表中
      if (connectIdToSelect && llmConnects.length > 0) {
        const foundConnect = llmConnects.find(connect => connect.id === connectIdToSelect);
        if (foundConnect) {
          setSelectedModel(connectIdToSelect);
        } else {
          setToast({ message: '智能体关联的连接已不存在，请重新选择连接', type: 'error' });
          setSelectedModel('');
        }
      } else {
        setSelectedModel(connectIdToSelect);
      }

      // 初始化选中的模型（如果有modelId）
      if (editData.modelId) {
        setSelectedModels([editData.modelId]);
      }

      setSelectedMcps(editData.mcpIds || []);

      // 初始化工具模式
      setToolMode(editData.toolmode || 'function');

      // 初始化头像，支持字符串、JSON字符串或对象格式
      if (editData.avatar) {
        if (typeof editData.avatar === 'string') {
          try {
            // 尝试解析为JSON（新格式）
            const avatarObj = JSON.parse(editData.avatar);
            if (avatarObj.name) {
              setSelectedAvatar(avatarObj.name);
              setSelectedAvatarColor(avatarObj.color || '#3B82F6');
            } else {
              // 如果解析出的对象没有name属性，则当作旧格式处理
              setSelectedAvatar(editData.avatar);
              setSelectedAvatarColor('#3B82F6');
            }
          } catch {
            // JSON解析失败，当作旧格式：纯字符串头像名称
            setSelectedAvatar(editData.avatar);
            setSelectedAvatarColor('#3B82F6'); // 使用默认颜色
          }
        } else if (typeof editData.avatar === 'object' && editData.avatar.name) {
          // 直接传递的对象格式（理论上不会出现，但保留兼容性）
          setSelectedAvatar(editData.avatar.name);
          setSelectedAvatarColor(editData.avatar.color || '#3B82F6');
        }
      }
    }
  }, [editMode, editData, llmConnects]); // 添加llmConnects依赖，确保连接列表加载后再选中

  // 组件关闭时重置状态
  React.useEffect(() => {
    if (!isOpen) {
      setAgentName('');
      setAgentDescription('');
      setAgentPrompt('');
      setSelectedModel('');
      setSelectedMcps([]);
      setToolMode('function'); // 重置为默认工具模式
      setSelectedModels([]);
      setSelectedAvatar('user'); // 重置为默认头像
      setSelectedAvatarColor('#FFFFFF'); // 重置为默认颜色
      setIsAvatarPanelOpen(false);
      setToast(null);
      setSystemModels([]);
      setModelSelectionCleared(false);
    }
  }, [isOpen]);



  // 初始化默认模型列表（当没有选择连接时显示）
  const defaultSystemModels = React.useMemo(() => {
    // 这里暂时使用llmConnects作为可选模型，你可以根据实际需求调整
    return llmConnects.map((connect) => ({
      id: connect.id,
      name: connect.name,
      group: connect.ctype || 'LLM',
      description: `${connect.ctype || 'LLM'} 类型模型`,
      tags: [(connect.ctype || 'llm').toLowerCase(), '推理']
    }));
  }, [llmConnects]);

  // 获取连接列表，在编辑模式下优先显示当前智能体使用的连接
  const getOrderedConnects = React.useMemo(() => {
    if (!llmConnects.length) {
      return llmConnects;
    }

    // 编辑模式下，优先显示已保存的连接
    if (editMode && editData && (editData.connectId || editData.connectid)) {
      const savedConnectId = editData.connectId || editData.connectid;
      const savedConnect = llmConnects.find(connect => connect.id === savedConnectId);
      const otherConnects = llmConnects.filter(connect => connect.id !== savedConnectId);

      if (savedConnect) {
        return [savedConnect, ...otherConnects];
      }
    }

    // 如果当前有选中的连接，也排在前面
    // if (selectedModel) {
    //   const selectedConnect = llmConnects.find(connect => connect.id === selectedModel);
    //   const otherConnects = llmConnects.filter(connect => connect.id !== selectedModel);

    //   if (selectedConnect) {
    //     return [selectedConnect, ...otherConnects];
    //   }
    // }

    // 默认返回原始顺序
    return llmConnects;
  }, [llmConnects, selectedModel, editMode, editData]);

  // 获取选中的模型信息，包含自定义模型
  const selectedModelInfos = React.useMemo(() => {
    return selectedModels.map(modelId => {
      // 先在systemModels（从supportedModels转换的数据）中查找
      const supportedModel = systemModels.find(m => m.id === modelId) ||
        defaultSystemModels.find(m => m.id === modelId);
      if (supportedModel) {
        return {
          id: supportedModel.id,
          name: supportedModel.name,
          group: supportedModel.group || 'LLM'
        } as ModelInfo;
      }

      // 再在自定义模型中查找
      const customModel = customModels.find(m => m.id === modelId);
      if (customModel) {
        return customModel;
      }

      // 编辑模式下，如果在上述列表中找不到，且用户没有手动清空选择，尝试从 editData 中获取模型信息
      if (editMode && editData && editData.modelId === modelId && !modelSelectionCleared) {
        return {
          id: editData.modelId,
          name: editData.modelName || modelId,
          group: 'LLM'
        } as ModelInfo;
      }

      // 如果都找不到，返回一个基础的模型信息
      return {
        id: modelId,
        name: modelId,
        group: 'LLM'
      } as ModelInfo;
    }).filter(Boolean) as ModelInfo[];
  }, [selectedModels, systemModels, defaultSystemModels, customModels, editMode, editData, modelSelectionCleared]);



  // 模型选择处理函数
  const handleSelectModel = async () => {
    // 检查是否选择了连接
    if (!selectedModel) {
      setToast({ message: '请先选择一个连接', type: 'error' });
      return;
    }

    // 检查是否提供了获取连接详情的回调函数
    if (!onFetchConnectDetails) {
      // 使用默认模型列表
      setShowModelSelector(true);
      return;
    }

    // 获取选中连接的详细信息
    try {
      const selectedConnect = llmConnects.find(connect => connect.id === selectedModel);
      if (!selectedConnect) {
        setToast({ message: '未找到选中的连接信息', type: 'error' });
        return;
      }

      // 使用连接的ctype（连接类型）去获取连接定义详情，而不是使用连接配置的ID
      const connectDetails = await onFetchConnectDetails(selectedConnect.ctype);

      if (connectDetails && connectDetails.detail && connectDetails.detail.supportedModels) {
        // 将supportedModels转换为ModelSelectorModal需要的格式
        const modelsForSelector = connectDetails.detail.supportedModels.map((model: any) => ({
          id: model.id,
          name: model.name,
          group: model.group || selectedConnect.ctype,
          description: `${model.group || selectedConnect.ctype} 模型`,
          tags: [selectedConnect.ctype.toLowerCase(), '推理']
        }));

        // 更新systemModels并打开模型选择器
        setSystemModels(modelsForSelector);
        setShowModelSelector(true);
      } else {
        // 如果没有supportedModels，使用默认模型列表
        setShowModelSelector(true);
      }
    } catch (error) {
      setToast({ message: '获取模型列表失败，请重试', type: 'error' });
    }
  };

  const handleAddModelClick = () => {
    // 检查是否选择了连接 - 与"选择模型"保持一致的约束
    if (!selectedModel) {
      setToast({ message: '请先选择一个连接', type: 'error' });
      return;
    }

    setShowAddModelModal(true);
  };

  // 处理模型选择确认
  const handleModelSelect = (modelIds: string[]) => {
    // 与现有选中的模型合并，去重
    setSelectedModels(prev => {
      const merged = [...new Set([...prev, ...modelIds])];
      return merged;
    });
    setModelSelectionCleared(false); // 重置清空状态，因为用户重新选择了模型
    setShowModelSelector(false);
  };

  // 处理添加模型
  const handleAddModel = (model: { id: string; name: string }) => {
    // 创建新的模型信息
    const newModel: ModelInfo = {
      id: model.id,
      name: model.name,
      group: 'Custom' // 自定义模型默认分组
    };

    // 添加到自定义模型列表
    setCustomModels(prev => [...prev, newModel]);

    // 自动选择新添加的模型
    setSelectedModels(prev => [...prev, model.id]);
    setModelSelectionCleared(false); // 重置清空状态，因为用户添加了模型
  };

  // 移除单个模型
  const handleRemoveModel = (modelId: string) => {
    setSelectedModels(prev => prev.filter(id => id !== modelId));
  };

  // 连接创建成功后的处理
  const handleConnectCreated = () => {
    setShowLLMConnectModal(false);
    // 重新获取LLM连接列表
    fetchLLMConnects();
    setToast({ message: 'LLM连接创建成功！', type: 'success' });
  };

  const handleSave = async () => {
    // 验证必填字段
    if (agentName.trim() === '') {
      setToast({ message: '请输入智能体名称', type: 'error' });
      return;
    }

    if (agentDescription.trim() === '') {
      setToast({ message: '请输入智能体描述', type: 'error' });
      return;
    }

    if (!selectedModel) {
      setToast({ message: '请选择一个连接', type: 'error' });
      return;
    }

    // 检查模型选择：编辑模式下可以使用已保存的模型，创建模式下必须选择新模型
    if (selectedModels.length === 0 && (!editMode || !editData?.modelId)) {
      setToast({ message: '请选择至少一个模型', type: 'error' });
      return;
    }

    try {
      // 获取模型信息：优先使用新选择的模型，否则使用已保存的模型
      let primaryModelId: string;
      let primaryModelName: string;

      if (selectedModels.length > 0) {
        // 用户重新选择了模型
        primaryModelId = selectedModels[0] || '';
        const primaryModelInfo = selectedModelInfos.find(model => model.id === primaryModelId);
        primaryModelName = primaryModelInfo?.name || '未知模型';
      } else if (editMode && editData?.modelId) {
        // 编辑模式下使用已保存的模型
        primaryModelId = editData.modelId;
        primaryModelName = editData.modelName || '未知模型';
      } else {
        setToast({ message: '请选择一个模型', type: 'error' });
        return;
      }

      // 构建保存数据
      const agentData = {
        ...(editMode && editData?.id && { id: editData.id }), // 编辑模式下包含ID
        name: agentName,
        description: agentDescription,
        prompt: agentPrompt,
        avatar: JSON.stringify({
          name: selectedAvatar,
          color: selectedAvatarColor
        }), // 保存头像信息为JSON字符串
        modelId: primaryModelId, // 选择的具体模型ID
        modelName: primaryModelName, // 模型显示名称
        connectId: selectedModel as string, // 连接配置ID
        mcpIds: selectedMcps,
        toolmode: toolMode, // 工具模式
        createUser: 'system' // 这里可以从用户上下文获取
      };

      // 根据编辑模式选择不同的API端点和方法
      const apiUrl = editMode && editData?.id
        ? `/api/agents/${editData.id}`
        : '/api/agents';
      const method = editMode && editData?.id ? 'PUT' : 'POST';

      // 调用API保存
      const response = await fetch(apiUrl, {
        method: method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(agentData),
      });

      const result = await response.json();

      if (result.success) {
        setToast({
          message: `智能体配置${editMode ? '更新' : '保存'}成功！`,
          type: 'success'
        });

        // 调用成功回调，刷新列表
        if (onSaveSuccess) {
          await onSaveSuccess();
        }

        // 延迟关闭模态框，让用户看到成功消息
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        setToast({
          message: result.error || `${editMode ? '更新' : '保存'}失败，请重试`,
          type: 'error'
        });
      }
    } catch (error) {
      setToast({
        message: `${editMode ? '更新' : '保存'}失败，请检查网络连接`,
        type: 'error'
      });
    }
  };

  if (!isOpen) return null;

  return (
    <ModalBackdrop onClick={(e) => e.target === e.currentTarget && onClose()}>
      <PremiumModalContainer style={{ height: '90vh' }}>
        <ModalHeader>
          <PremiumTitleDesc>
            <h4>智能体配置</h4>
            <p>创建和配置您的专属AI智能体，打造个性化的智能助手</p>
          </PremiumTitleDesc>
          <CloseButton onClick={onClose} style={{ color: 'white' }}>×</CloseButton>
        </ModalHeader>

        <ModalContent style={{ height: 'calc(90vh - 80px)', overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
          <TwoColumnLayout style={{ padding: '0 24px', flex: '1 1 auto', height: 'auto' }}>
            {/* 左列：头像和基本信息 */}
            <LeftColumn style={{ height: '100%', overflowY: 'auto', paddingRight: '12px', display: 'flex', flexDirection: 'column' }}>
              {/* 头像选择区域 */}
              <AvatarSection>
                <SelectedAvatar
                  onClick={() => setIsAvatarPanelOpen(true)}
                >
                  {React.cloneElement(getAvatarIcon(selectedAvatar), {
                    style: { color: selectedAvatarColor, fontSize: '32px' }
                  })}
                </SelectedAvatar>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '2px', width: '100%' }}>
                  <FormInputNoborder
                    type="text"
                    value={agentName}
                    onChange={(e) => setAgentName(e.target.value)}
                    placeholder="请输入智能体名称"
                  />
                  <FormInputNoborder
                    value={agentDescription}
                    onChange={(e) => setAgentDescription(e.target.value)}
                    placeholder="请输入智能体的详细描述和功能说明"
                    type="text"
                    style={{
                      fontSize: '12px'
                    }}
                  />
                </div>
              </AvatarSection>

              {/* 基本信息区域 */}
              <FormSection style={{ flex: '1 1 auto'}}>
                  <PremiumFormLabel>智能体提示语</PremiumFormLabel>
                  <FormInput
                    value={agentPrompt}
                    onChange={(e) => setAgentPrompt(e.target.value)}
                    placeholder="请输入智能体的系统提示语，用于指导AI的行为和回复风格"
                    as="textarea"
                    rows={10}
                    style={{
                      background: 'transparent',
                      border: '1px solid #ffffff30',
                      color: 'white',
                      resize: 'vertical',
                      minHeight: '200px',
                      flex: '1 1 auto'
                    }}
                  />
 
              </FormSection>
            </LeftColumn>

            {/* 右列：模型和MCP选择 */}
            <RightColumn style={{ height: '100%', paddingLeft: '12px', display: 'flex', flexDirection: 'column' }}>
              {/* 模型选择区域 */}
              <ModelSelectionArea>
                <ModelTitle>连接凭证
                  <div>
                    <span
                      onClick={() => setShowLLMConnectModal(true)}
                      style={{
                        color: '#60a5fa',
                        cursor: 'pointer',
                        fontWeight: '400',
                        textDecoration: 'underline'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.color = '#3b82f6';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.color = '#60a5fa';
                      }}
                    >
                      前往
                    </span>
                    <span style={{ marginLeft: '4px', fontWeight: '300' }}>创建大模型连接</span>
                  </div>
                </ModelTitle>
                {loading ? (
                  <div style={{ color: 'white', textAlign: 'center', padding: '20px' }}>
                    🔄 正在加载模型...
                  </div>
                ) : error ? (
                  <div style={{ color: '#ff6b6b', textAlign: 'center', padding: '20px' }}>
                    ⚠️ {error}
                  </div>
                ) : (
                  <>
                    <FormField>
                      <ModelCardsContainer>
                        {llmConnects.length === 0 ? (
                          <div style={{
                            color: 'rgba(255, 255, 255, 0.7)',
                            textAlign: 'center',
                            padding: '20px',
                            fontSize: '14px',
                            gridColumn: '1 / -1'
                          }}>
                            暂无可用的LLM模型配置
                          </div>
                        ) : (
                          <>
                            {getOrderedConnects.map((connect) => (
                              <ModelCard
                                key={connect.id}
                                $selected={selectedModel === connect.id}
                                onClick={() => {
                                  // 使用当前点击的连接ID进行比较，避免状态异步更新问题
                                  const newConnectId = connect.id;
                                  const currentSelectedModel = selectedModel;

                                  // 只有当选择了不同的连接时，才清空已选择的模型
                                  if (currentSelectedModel !== newConnectId) {
                                    setSelectedModels([]);
                                    setSystemModels([]); // 同时清空系统模型缓存
                                    setModelSelectionCleared(true); // 标记模型选择已被清空
                                  }
                                  setSelectedModel(newConnectId);
                                }}
                                title={connect.name}
                              >
                                <ModelIcon>
                                  <img src={`/connect-icons/${connect.ctype}.svg`} alt={connect.ctype} />
                                </ModelIcon>
                                <ModelInfo>
                                  <ModelName title={connect.name}>{connect.name}</ModelName>
                                </ModelInfo>
                              </ModelCard>
                            ))}
                          </>
                        )}
                      </ModelCardsContainer>
                    </FormField>

                    {/* 模型操作区域 */}
                    <div style={{ marginTop: '16px' }}>
                      {/* 统一使用PremiumEmptyModelMessage显示模型状态 */}
                      <PremiumEmptyModelMessage style={{
                        marginTop: '0',
                        textAlign: 'center',
                        fontSize: '14px',
                        maxHeight: '304px',
                        minHeight: 'auto',
                      }}>
                        {(() => {
                          const hasSelectedModels = selectedModels.length > 0;
                          const hasModelInfos = selectedModelInfos.length > 0;
                          // const isEditModeWithSavedModel = editMode && editData?.modelId && editData?.modelName;

                          return hasSelectedModels && hasModelInfos;
                        })() ? (
                          <div style={{
                            marginTop: '-10px',
                            height: '31px'
                          }}>
                            <div style={{ paddingRight: '30px' }}>
                              {selectedModelInfos.map((model, index) => (
                                <div key={model.id} style={{ marginBottom: index < selectedModelInfos.length - 1 ? '10px' : '0' }}>
                                  <div style={{
                                    fontSize: '16px',
                                    fontWeight: '600',
                                    color: 'rgba(255, 255, 255, 0.95)',
                                    marginBottom: '0px'
                                  }}>
                                    {model.name}
                                  </div>
                                  <div style={{
                                    fontSize: '13px',
                                    color: 'rgba(255, 255, 255, 0.7)'
                                  }}>
                                    {model.id}
                                  </div>
                                </div>
                              ))}
                            </div>
                            <button
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                setSelectedModels([]);
                                setModelSelectionCleared(true);
                              }}
                              style={{
                                position: 'absolute',
                                top: '4px',
                                right: '4px',
                                background: 'rgba(255, 255, 255, 0.1)',
                                border: '0px',
                                color: 'rgba(255, 255, 255, 0.7)',
                                cursor: 'pointer',
                                fontSize: '14px',
                                padding: '4px 8px',
                                borderRadius: '4px',
                                transition: 'all 0.2s',
                                zIndex: 1000,
                                minWidth: '24px',
                                minHeight: '24px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.color = '#ffffff';
                                e.currentTarget.style.background = 'rgba(252, 252, 252, 0.2)';
                                e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.4)';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.color = 'rgba(255, 255, 255, 0.7)';
                                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                                e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                              }}
                            >
                              ×
                            </button>
                          </div>
                        ) : editMode && editData?.modelId && editData?.modelName && !modelSelectionCleared ? (
                          // 编辑模式下显示已保存的模型信息
                          <div style={{
                            position: 'relative',
                            padding: '8px 12px',
                            width: '100%',
                            color: 'rgba(255, 255, 255, 0.9)',
                            fontSize: '18px',
                            background: 'rgba(34, 197, 94, 0.1)', // 绿色背景表示已保存
                            border: '1px solid rgba(34, 197, 94, 0.3)',
                            borderRadius: '6px',
                          }}>
                            <div style={{ paddingRight: '30px' }}>
                              <div style={{
                                fontSize: '16px',
                                fontWeight: '600',
                                color: 'rgba(255, 255, 255, 0.95)',
                                marginBottom: '2px'
                              }}>
                                {editData.modelName}
                              </div>
                              <div style={{
                                fontSize: '13px',
                                color: 'rgba(255, 255, 255, 0.7)'
                              }}>
                                {editData.modelId}
                              </div>
                              <div style={{
                                fontSize: '11px',
                                color: 'rgba(34, 197, 94, 0.8)',
                                marginTop: '4px'
                              }}>
                                ✓ 已保存的模型
                              </div>
                            </div>
                            <button
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                // 清空已选模型，允许重新选择
                                setSelectedModels([]);
                                setModelSelectionCleared(true);
                              }}
                              style={{
                                position: 'absolute',
                                top: '4px',
                                right: '4px',
                                background: 'rgba(255, 255, 255, 0.1)',
                                border: '1px solid rgba(255, 255, 255, 0.2)',
                                color: 'rgba(255, 255, 255, 0.7)',
                                cursor: 'pointer',
                                fontSize: '14px',
                                padding: '4px 8px',
                                borderRadius: '4px',
                                transition: 'all 0.2s',
                                zIndex: 1000,
                                minWidth: '24px',
                                minHeight: '24px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.color = '#dc2626';
                                e.currentTarget.style.background = 'rgba(220, 38, 38, 0.2)';
                                e.currentTarget.style.borderColor = 'rgba(220, 38, 38, 0.4)';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.color = 'rgba(255, 255, 255, 0.7)';
                                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                                e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                              }}
                              title="重新选择模型"
                            >
                              ×
                            </button>
                          </div>
                        ) : (
                          <div style={{ fontSize: '13px', color: 'rgba(255, 255, 255, 0.7)' }}>
                            请选择上方大模型凭证，再
                            <span
                              onClick={handleSelectModel}
                              style={{
                                color: '#60a5fa',
                                cursor: 'pointer',
                                textDecoration: 'underline',
                                marginRight: '8px'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.color = '#3b82f6';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.color = '#60a5fa';
                              }}
                            >
                              选择模型
                            </span>
                            或
                            <span
                              onClick={handleAddModelClick}
                              style={{
                                color: '#60a5fa',
                                cursor: 'pointer',
                                textDecoration: 'underline',
                                marginLeft: '8px'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.color = '#3b82f6';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.color = '#60a5fa';
                              }}
                            >
                              添加模型
                            </span>
                            来增加模型
                          </div>
                        )}
                      </PremiumEmptyModelMessage>
                    </div>
                  </>
                )}
              </ModelSelectionArea>

              {/* MCP选择区域 */}
              <McpSelectionArea style={{ flex: '1 1 auto', display: 'flex', flexDirection: 'column', minHeight: '0' }}>
                <McpTitleRow>
                  <McpTitle>MCP选择 (可多选)</McpTitle>
                  <ToolModeSelect id='mode-select'
                    value={toolMode}
                    onChange={(e) => setToolMode(e.target.value)}
                  >
                    <option value="function">函数</option>
                    <option value="prompt">提示词</option>
                  </ToolModeSelect>
                </McpTitleRow>
                <McpCardsContainer style={{ flex: '1 1 auto', overflowY: 'auto', minHeight: '0' }}>
                  {mcpConfigs.length === 0 ? (
                    <div style={{
                      color: 'rgba(255, 255, 255, 0.7)',
                      textAlign: 'center',
                      padding: '16px',
                      fontSize: '12px',
                      gridColumn: '1 / -1'
                    }}>
                      暂无可用的MCP配置
                    </div>
                  ) : (
                    mcpConfigs.map((mcp) => (
                      <McpCard
                        key={mcp.id}
                        $selected={selectedMcps.includes(mcp.id)}
                        onClick={() => {
                          setSelectedMcps(prev =>
                            prev.includes(mcp.id)
                              ? prev.filter(id => id !== mcp.id)
                              : [...prev, mcp.id]
                          );
                        }}
                      >
                        <McpName title={mcp.name}>
                          <FaTools />
                          {mcp.name}
                        </McpName>
                        <McpType>{mcp.type}</McpType>
                      </McpCard>
                    ))
                  )}
                </McpCardsContainer>
              </McpSelectionArea>

            </RightColumn>
          </TwoColumnLayout>

          {/* 操作按钮区域 */}
          <FormButtonGroup style={{ flexShrink: 0, padding: '20px 24px 0 24px', justifyContent: 'space-between', display: 'flex', gap: '0px' }}>
            <PremiumFormButton
              $variant="primary"
              onClick={handleSave}
              style={{
                background: '#3b82f6',
                border: 'none',
                fontWeight: '600',
                flex: '1',
                marginRight: '12px'
              }}
            >
              保存配置
            </PremiumFormButton>
            <PremiumFormButton
              $variant="secondary"
              onClick={onClose}
              style={{
                flex: '1',
                marginLeft: '12px'
              }}
            >
              取消
            </PremiumFormButton>
          </FormButtonGroup>
        </ModalContent>
      </PremiumModalContainer>
      {toast && (
        <LiquidToast
          title={toast.type === 'success' ? '成功' : '错误'}
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}

      {/* 模型选择弹窗 */}
      <ModelSelectorModal
        isOpen={showModelSelector}
        onClose={() => setShowModelSelector(false)}
        models={systemModels.length > 0 ? systemModels : defaultSystemModels}
        selectedModels={selectedModels}
        onSelectModels={handleModelSelect}
        title="AI智能体模型选择"
        modelUrl=""
        apiKey=""
      />

      {/* 添加模型弹窗 */}
      <AddModelModal
        isOpen={showAddModelModal}
        onClose={() => setShowAddModelModal(false)}
        onAddModel={handleAddModel}
      />

      {/* LLM连接选择弹窗 */}
      <LLMConnectSelectorModal
        isOpen={showLLMConnectModal}
        onClose={() => setShowLLMConnectModal(false)}
        onConnectCreated={handleConnectCreated}
        onFetchLLMConnects={onFetchConnects}
        onFetchConnectDetails={onFetchConnectDetails}
        onSaveConnect={onSaveConnect}
        onTestConnect={onTestConnect}
      />

      {/* 头像选择面板 */}
      {isAvatarPanelOpen && (
        <AvatarPanelModal onClick={(e) => e.target === e.currentTarget && setIsAvatarPanelOpen(false)}>
          <AvatarPanelContainer>
            <AvatarPanel
              selectedAvatar={selectedAvatar}
              selectedColor={selectedAvatarColor}
              onAvatarSelect={(avatarKey) => {
                setSelectedAvatar(avatarKey);
              }}
              onColorSelect={(color) => {
                setSelectedAvatarColor(color);
              }}
            />

            {/* <AvatarPanelActions>
              <FormButton
                $variant="primary"
                onClick={() => setIsAvatarPanelOpen(false)}
                style={{
                  background: '#3b82f6',
                  border: 'none',
                  fontWeight: '600'
                }}
              >
                确认选择
              </FormButton>
              <FormButton
                $variant="secondary"
                onClick={() => setIsAvatarPanelOpen(false)}
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  color: 'white'
                }}
              >
                取消
              </FormButton>
            </AvatarPanelActions> */}
          </AvatarPanelContainer>
        </AvatarPanelModal>
      )}
    </ModalBackdrop>
  );
};