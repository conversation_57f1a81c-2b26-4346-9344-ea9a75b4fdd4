{"name": "@repo/node-set", "version": "1.0.0", "description": "", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./dist/*": {"types": "./dist/*.d.ts", "default": "./dist/*.js"}}, "scripts": {"lint": "eslint . --max-warnings 0", "check-types": "tsc --noEmit", "clean": "powershell -Command \"Remove-Item -Path '.turbo', 'node_modules' -Recurse -Force -ErrorAction SilentlyContinue\"", "build": "tsup && pnpm run copy-files", "copy-files": "copyfiles -u 1 \"src/nodes/**/*.{json,svg}\" dist"}, "dependencies": {"@repo/common": "workspace:*", "@astronautlabs/jsonpath": "^1.1.2", "@google/genai": "^1.0.0", "@sinclair/typebox": "^0.34.33", "@types/nodemailer": "^6.4.17", "nodemailer": "^6.9.0", "ollama": "^0.5.15", "fast-glob": "^3.3.2", "inversify": "^6.2.2", "reflect-metadata": "^0.2.1", "zod": "^3.24.1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"copyfiles": "^2.4.1", "tsup": "^8.0.2"}}