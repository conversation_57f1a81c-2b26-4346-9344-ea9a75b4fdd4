import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@repo/db";

interface RouteParams {
    params: Promise<{
        id: string;
    }>;
}

export async function GET(req: NextRequest, { params }: RouteParams) {

    const { id } = await params;
    const result = await prisma.agentThread.findUnique({
        where: { id: id }
    });

    return NextResponse.json(result);
}

//删除话题
export async function DELETE(req: NextRequest, { params }: RouteParams) {
    const { id } = await params;
    try {
        await prisma.$transaction(async (tx) => {

            // Delete messages first (though CASCADE should handle this)
            await tx.agentThread.delete({
                where: {id: id},
            });

            // Delete the threads
            await tx.agentMessage.deleteMany({
                where: { threadId: id },
            });
        });

        return NextResponse.json(true);
    } catch (error) {
        console.error("❌ Failed to delete thread:", error);
        throw error;
    }
}