<svg xmlns="http://www.w3.org/2000/svg" width="400" height="400" viewBox="0 0 400 400">
  <defs>
    <radialGradient id="glow-gradient-light" cx="50%" cy="50%" r="60%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#B3F0FF" stop-opacity="0.7"/>
      <stop offset="40%" stop-color="#D9B3FF" stop-opacity="0.5"/>
      <stop offset="70%" stop-color="#B3CCFF" stop-opacity="0.3"/>
      <stop offset="100%" stop-color="#B3E6FF" stop-opacity="0"/>
    </radialGradient>
    <filter id="blur-light" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="20"/>
    </filter>
  </defs>
  <rect width="100%" height="100%" fill="#F5F7FA"/>
  <circle cx="200" cy="200" r="150" fill="url(#glow-gradient-light)" filter="url(#blur-light)" opacity="0.85"/>
</svg>