"use client";

import React, { useState } from 'react';
import styled from 'styled-components';
import { ConnectSettings } from './ConnectSettings';
import { ILLMConnect } from '@repo/common';

// 连接详情内容容器 - 适合在模态框内部使用
const DetailsContent = styled.div`
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
`;

const HeaderActions = styled.div`
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  gap: 8px;
  z-index: 10;
`;

const BackButton = styled.button`
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  width: 40px;
  height: 36px;
  font-size: 16px;
  cursor: pointer;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
  }
`;

const CloseButton = styled.button`
  background: ${({ theme }) => theme.mode === 'dark'
    ? 'rgba(100, 116, 139, 0.15)'
    : 'rgba(100, 116, 139, 0.1)'
  };
  border: 1px solid ${({ theme }) => theme.mode === 'dark'
    ? 'rgba(100, 116, 139, 0.3)'
    : 'rgba(100, 116, 139, 0.2)'
  };
  border-radius: 6px;
  width: 36px;
  height: 36px;
  font-size: 18px;
  cursor: pointer;
  color: ${({ theme }) => theme.mode === 'dark' ? '#94a3b8' : '#64748b'};
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${({ theme }) => theme.mode === 'dark'
      ? 'rgba(100, 116, 139, 0.25)'
      : 'rgba(100, 116, 139, 0.15)'
    };
    color: ${({ theme }) => theme.mode === 'dark' ? '#cbd5e1' : '#475569'};
  }
`;

interface ConnectDetailsViewProps {
  connect: ILLMConnect;
  savedValues?: Record<string, any>;
  onClose: () => void; // 回退功能
  onSave: (connectData: any) => void;
  onTest?: (config: Record<string, any>, message?: string) => Promise<any>;
  onStreamTest?: (config: Record<string, any>, message: string, onChunk: (chunk: string) => void) => Promise<any>;
  editMode?: boolean;
  editData?: {
    id: string;
    connectId: string;
    name: string;
    config: Record<string, any>;
  };
  showBackButton?: boolean; // 是否显示回退按钮
}

export const ConnectDetailsView: React.FC<ConnectDetailsViewProps> = ({
  connect,
  savedValues = {},
  onClose,
  onSave,
  onTest,
  onStreamTest,
  editMode = false,
  editData,
  showBackButton = false
}) => {
  const [showDialogue, setShowDialogue] = useState(false);

  const handleSave = async (connectData: any) => {
    // 添加连接类型信息到保存数据中
    const saveData = {
      ...connectData,
      mtype: connect.overview.type // 如果 connectType 不存在，使用 connect.type 作为后备
    };
    await onSave(saveData);
    onClose();
  };

  return (
    <DetailsContent>
      {showBackButton && (
        <HeaderActions>
          <BackButton onClick={onClose} title="返回连接列表">
            ←
          </BackButton>
        </HeaderActions>
      )}
      <ConnectSettings
        connect={connect}
        savedValues={editMode && editData ? editData.config : savedValues}
        onClose={onClose}
        onSave={handleSave}
        onTest={onTest}
        onStreamTest={onStreamTest}
        onDialogueToggle={setShowDialogue}
        editMode={editMode}
        editData={editData}
      />
    </DetailsContent>
  );
}; 