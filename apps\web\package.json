{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build && pnpm run copy-node-assets && pnpm run copy-connect-assets", "copy-node-assets": "copyfiles -u 6 ../../packages/node-set/dist/nodes/**/*.{json,svg} public/nodes", "copy-connect-assets": "copyfiles -f ../../packages/connect-set/dist/connects/**/**/*.svg public/connect-icons", "start": "next start --port 3000", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit", "clean": "powershell -Command \"Remove-Item -Path '.next', '.turbo', 'node_modules' -Recurse -Force -ErrorAction SilentlyContinue\"", "db:push": "pnpm --filter @repo/db db:push", "db:studio": "pnpm --filter @repo/db db:studio"}, "dependencies": {"@prisma/client": "^5.11.0", "@repo/common": "workspace:*", "@repo/db": "workspace:*", "@repo/node-set": "workspace:*", "@repo/connect-set": "workspace:*", "@repo/ui": "workspace:*", "@repo/engine": "workspace:*", "@types/bcryptjs": "^2.4.6", "@astronautlabs/jsonpath": "^1.1.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "express": "^4.18.3", "next": "^15.3.0", "next-auth": "^4.24.7", "react": "^18.2.0", "react-dom": "^18.2.0", "styled-components": "^6.1.8"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^22.15.3", "@types/react": "^18.2.66", "@types/styled-components": "^5.1.34", "copyfiles": "^2.4.1", "eslint": "^9.25.0", "typescript": "5.8.2"}}