// 客户端安全版本 - 不包含任何 Node.js 依赖

// 仅导出连接器定义和类型
export * from './connects';
export * from './categories';
export * from './utils';

// 导入所有连接器定义（Next.js环境兼容的手动导入方式）
// 数据库连接器
import { MySQLConnect } from './connects/database/mysql/mysql.connect';
import { PostgreSQLConnect } from './connects/database/postgresql/postgresql.connect';
import { DamengConnect } from './connects/database/dameng/dameng.connect';
import { DB2Connect } from './connects/database/db2/db2.connect';
import { KingbaseConnect } from './connects/database/kingbase/kingbase.connect';
import { OracleConnect } from './connects/database/oracle/oracle.connect';
import { SQLServerConnect } from './connects/database/sqlserver/sqlserver.connect';

// HTTP连接器
import { RestApiConnect } from './connects/http/rest-api/rest-api.connect';

// LLM连接器
import { OpenAIConnect } from './connects/llm/openai/openai.connect';
import { SiliconFlowConnect } from './connects/llm/siliconFlow/siliconflow.connect';
import { AnthropicConnect } from './connects/llm/anthropic/anthropic.connect';
import { GoogleConnect } from './connects/llm/google/google.connect';
import { BaiduConnect } from './connects/llm/baidu/baidu.connect';
import { TencentConnect } from './connects/llm/tencent/tencent.connect';
import { XfyunConnect } from './connects/llm/xfyun/xfyun.connect';
import { MinimaxConnect } from './connects/llm/minimax/minimax.connect';
import { MistralConnect } from './connects/llm/mistral/mistral.connect';
import { XAIConnect } from './connects/llm/xai/xai.connect';
import { AlibabaConnect } from './connects/llm/alibaba/alibaba.connect';
import { GroqConnect } from './connects/llm/groq/groq.connect';
import { DeepSeekConnect } from './connects/llm/deepseek/deepseek.connect';
import { MoonshotConnect } from './connects/llm/moonshot/moonshot.connect';
import { ZhipuConnect } from './connects/llm/zhipu/zhipu.connect';
import { LingyiwanwuConnect } from './connects/llm/lingyiwanwu/lingyiwanwu.connect';
import { BaichuanConnect } from './connects/llm/baichuan/baichuan.connect';
import { Qihoo360Connect } from './connects/llm/qihoo360/qihoo360.connect';
import { StepfunConnect } from './connects/llm/stepfun/stepfun.connect';
import { TogetherConnect } from './connects/llm/together/together.connect';
import { OllamaConnect } from './connects/llm/ollama/ollama.connect';
import { OpenRouterConnect } from './connects/llm/openrouter/openrouter.connect';

// Next.js环境兼容的初始化函数
export async function initializeConnects() {
  const isDev = process.env.NODE_ENV === 'development';
  
  if (isDev) console.log('🔄 Creating ConnectRegistry instance...');
  
  // 动态导入 ConnectRegistry 以避免循环依赖
  const { ConnectRegistry } = await import('@repo/common');
  const registry = new ConnectRegistry();
  
  if (isDev) console.log('📦 Registering connects...');
  
  // 手动注册所有连接（确保在Next.js环境中正常工作）
  try {
    // 注册数据库连接
    const databaseConnects = [
      MySQLConnect,
      PostgreSQLConnect,
      DamengConnect,
      DB2Connect,
      KingbaseConnect,
      OracleConnect,
      SQLServerConnect
    ];
    
    for (const connect of databaseConnects) {
      registry.registerConnect(connect);
      if (isDev) console.log(`✅ Registered ${connect.overview.name} connect`);
    }
    
    // 注册HTTP连接
    const httpConnects = [
      RestApiConnect
    ];
    
    for (const connect of httpConnects) {
      registry.registerConnect(connect);
      if (isDev) console.log(`✅ Registered ${connect.overview.name} connect`);
    }
    
    // 注册LLM连接
    const llmConnects = [
      OpenAIConnect,
      SiliconFlowConnect,
      AnthropicConnect,
      GoogleConnect,
      BaiduConnect,
      TencentConnect,
      XfyunConnect,
      MinimaxConnect,
      MistralConnect,
      XAIConnect,
      AlibabaConnect,
      GroqConnect,
      DeepSeekConnect,
      MoonshotConnect,
      ZhipuConnect,
      LingyiwanwuConnect,
      BaichuanConnect,
      Qihoo360Connect,
      StepfunConnect,
      TogetherConnect,
      OllamaConnect,
      OpenRouterConnect
    ];
    
    for (const connect of llmConnects) {
      registry.registerConnect(connect);
      if (isDev) console.log(`✅ Registered ${connect.overview.name} connect`);
    }
    
    console.log('🎉 Connect registry initialized with', registry.getAllConnects().length, 'connects');
    if (isDev) {
      console.log('📊 Final statistics:', registry.getStatistics?.());
    }
    
    return registry;
  } catch (error) {
    console.error('❌ Failed to register connects:', error);
    throw error;
  }
}

// 有条件地重新导出 common 模块（仅类型，不传播依赖）
export type {
  IConnect,
  IConnectRegistry,
  ConnectTestResult,
  ConnectType,
  ILLMConnect
} from '@repo/common';