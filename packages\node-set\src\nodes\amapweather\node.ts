import { injectable } from 'inversify';
import { INode, INodeBasic, INodeDetail,NodeLink, CategoryType, IExecuteOptions, IconName } from '@repo/common';
import config from "./node.json"
import amapWeather from "./AmapWeather";

@injectable()
export class Amap<PERSON>eather implements INode {
    node: INodeBasic = {
        kind: config.kind,
        name: config.name,
        categories: config.categories as CategoryType[],
        version: config.version,
        position: config.position as [number, number],
        description: config.description,
        icon: config.icon as IconName,
        link: {
			inputs: [NodeLink.Data],
			outputs: [NodeLink.Data]
		}
    };
    detail: INodeDetail = {
        // displayName: config.displayName,
        // name: config.name,
        // group: config.group as NodeGroupType[],
        // subtitle: config.subtitle,
        // description: config.description,
        // inputs: [NodeConnectionTypes.Main],
        // outputs: [NodeConnectionTypes.Main],
        fields: [
            {
                displayName: 'API Key',
                name: 'api<PERSON><PERSON>',
                type: 'string',
                default: '',
                required: true,
                placeholder: '',
                description:
                    'Amap Weather API using a simple API key.',
                controlType: 'input'
            },
            {
                displayName: 'City Code',
                name: 'cityCode',
                type: 'string',
                default: "",
                required: true,
                placeholder: '',
                description:
                    'City code for query',
                controlType: 'input'
            },
        ],
    };

    async execute(opts: IExecuteOptions): Promise<string> {
        const inputs = opts?.inputs;
        return await amapWeather({
            apiKey: inputs?.apiKey,
            cityCode: inputs?.cityCode,
            extensions: inputs?.extensions,
        })
    }
}
