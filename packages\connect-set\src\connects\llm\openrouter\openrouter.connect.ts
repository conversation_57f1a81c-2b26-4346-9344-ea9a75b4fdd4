import { ILLMConnect, ConnectTestResult, ILLMOverview } from '@repo/common';
import { LLMTester } from '../utils/llm-tester';
import { 
    createApi<PERSON>eyField, 
    createBaseUrlField, 
    testLLMConnection
} from '../utils/llm-fields';

const ConnectConfig: ILLMOverview = {
    id: 'openrouter',
    name: 'OpenRouter',
    type: 'llm',
    provider: 'custom',
    icon: 'OpenRouter.svg',
    description: 'OpenRouter AI模型路由服务连接',
    version: '1.0.0',
    api: { url: 'https://openrouter.ai/api/v1', suffix: '/chat/completions' },
    driver: 'openai',
    about: {
        apiHost: 'https://openrouter.ai/api/v1/',
        docUrl: 'https://openrouter.ai/docs/quick-start',
        modelUrl: 'https://openrouter.ai/models',
        getKeyUrl: 'https://openrouter.ai/keys'
    }
};

/**
 * OpenRouter LLM 连接定义
 */
export const OpenRouterConnect: ILLMConnect = {
    overview: ConnectConfig,
    detail: {
        supportedModels: [
            {id: 'openai/gpt-4o', name: 'gpt-4o',group: 'OpenAI'},
            {id: 'anthropic/claude-3.5-sonnet', name: 'claude-3.5-sonnet',group: 'Anthropic'},
            {id: 'meta-llama/llama-3.1-70b-instruct', name: 'llama-3.1-70b-instruct',group: 'Meta'},
            {id: 'deepseek/deepseek-r1-distill-llama-70b', name: 'deepseek-r1-distill-llama-70b',group: 'DeepSeek'},
            {id: 'deepseek/deepseek-r1-distill-qwen-32b', name: 'deepseek-r1-distill-qwen-32b',group: 'DeepSeek'},
            {id: 'deepseek/deepseek-r1-distill-qwen-32b-instruct', name: 'deepseek-r1-distill-qwen-32b-instruct',group: 'DeepSeek'},
            {id: 'deepseek/deepseek-r1-distill-qwen-32b-instruct-32k', name: 'deepseek-r1-distill-qwen-32b-instruct-32k',group: 'DeepSeek'},
            {id: 'deepseek/deepseek-r1-distill-qwen-32b-instruct-32k-32k', name: 'deepseek-r1-distill-qwen-32b-instruct-32k-32k',group: 'DeepSeek'},
            {id: 'deepseek/deepseek-r1-distill-qwen-32b-instruct-32k-32k-32k', name: 'deepseek-r1-distill-qwen-32b-instruct-32k-32k-32k',group: 'DeepSeek'},
            {id: 'deepseek/deepseek-r1-distill-qwen-32b-instruct-32k-32k-32k-32k', name: 'deepseek-r1-distill-qwen-32b-instruct-32k-32k-32k-32k',group: 'DeepSeek'},
        ],
        fields: [
            createApiKeyField(),
            createBaseUrlField(ConnectConfig.api.url),
        ],
        validateConnection: true,
        connectionTimeout: 10
    },

    async test(config: Record<string, any>, message?: string): Promise<ConnectTestResult> {
        try {
            // 验证必填字段
            if (!config.apiKey) {
                return {
                    success: false,
                    message: '缺少必填字段: apiKey'
                };
            }

            if (!config.model) {
                return {
                    success: false,
                    message: '缺少必填字段: model'
                };
            }

            // 使用通用测试器
            return await LLMTester.testConnection('openrouter', {
                apiKey: config.apiKey,
                baseUrl: config.baseUrl || 'https://openrouter.ai/api/v1',
                model: config.model,
                timeout: (config.timeout || 30) * 1000
            }, message);

        } catch (error) {
            return {
                success: false,
                message: `连接失败: ${error instanceof Error ? error.message : String(error)}`
            };
        }
    }
}; 