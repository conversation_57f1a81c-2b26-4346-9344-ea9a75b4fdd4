{"name": "@repo/common", "version": "1.0.0", "description": "", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"lint": "eslint . --max-warnings 0", "check-types": "tsc --noEmit", "clean": "powershell -Command \"Remove-Item -Path '.turbo', 'node_modules', 'dist' -Recurse -Force -ErrorAction SilentlyContinue\"", "build": "tsup"}, "dependencies": {"inversify": "^6.2.2", "fast-glob": "^3.3.2", "reflect-metadata": "^0.2.1"}, "devDependencies": {"tsup": "^8.0.2"}, "keywords": [], "author": "", "license": "ISC"}