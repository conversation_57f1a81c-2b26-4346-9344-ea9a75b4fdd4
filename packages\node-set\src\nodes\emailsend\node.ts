import { injectable } from 'inversify';
import { INode, INodeBasic, INodeDetail, NodeLink,CategoryType, IExecuteOptions, IconName } from '@repo/common';
import { appendAttributionOption } from "../../utils/descriptions";
import config from "./node.json"
import emailSend from "./EmailSend"

@injectable()
export class EmailSend implements INode {
    node: INodeBasic = {
        kind: config.kind,
        name: config.name,
        categories: config.categories as CategoryType[],
        version: config.version,
        position: config.position as [number, number],
        description: config.description,
        icon: config.icon as IconName,  
        link: {
            inputs: [NodeLink.Data],
            outputs: [NodeLink.Data]
        }
    };
    detail: INodeDetail = {
        // displayName: config.displayName,
        // name: config.name,
        // group: config.group as NodeGroupType[],
        // subtitle: config.subtitle,
        // description: config.description,
        // inputs: [NodeConnectionTypes.Main],
        // outputs: [NodeConnectionTypes.Main],
        fields: [
            {
                displayName: 'SMTP Host',
                name: 'smtpHost',
                type: 'string',
                default: '',
                required: true,
                placeholder: 'mail.example.com',
                description:
                    'SMTP host of the sender.',
                controlType: 'input'
            },
            {
                displayName: 'SMTP Port',
                name: 'smtpPort',
                type: 'number',
                default: 465,
                required: true,
                placeholder: '465',
                description:
                    'SMTP port of the sender.',
                controlType: 'input'
            },
            {
                displayName: 'Secure',
                name: 'secure',
                type: 'boolean',
                default: true,
                required: true,
                placeholder: '',
                description:
                    'Whether to use identity authentication.',
                controlType: 'input'
            },
            {
                displayName: 'SMTP Username',
                name: 'smtpUsername',
                type: 'string',
                default: "",
                required: true,
                placeholder: 'admin',
                description:
                    'SMTP username of the sender.',
                controlType: 'input'
            },
            {
                displayName: 'SMTP Password',
                name: 'smtpPassword',
                type: 'string',
                default: "",
                required: true,
                placeholder: '',
                description:
                    'SMTP password of the sender.',
                controlType: 'input'
            },
            {
                displayName: 'From Email',
                name: 'fromEmail',
                type: 'string',
                default: '',
                required: true,
                placeholder: '<EMAIL>',
                description:
                    'Email address of the sender. You can also specify a name: Nathan Doe &lt;<EMAIL>&gt;.',
                controlType: 'input'
            },
            {
                displayName: 'To Email',
                name: 'toEmail',
                type: 'string',
                default: '',
                required: true,
                placeholder: '<EMAIL>',
                description:
                    'Email address of the recipient. You can also specify a name: Nathan Doe &lt;<EMAIL>&gt;.',
                controlType: 'input'
            },
            {
                displayName: 'Subject',
                name: 'subject',
                type: 'string',
                default: '',
                placeholder: 'My subject line',
                description: 'Subject line of the email',
                controlType: 'input'
            },
            {
                displayName: 'Email Format',
                name: 'emailFormat',
                type: 'options',
                options: [
                    {
                        name: 'Text',
                        value: 'text',
                    },
                    {
                        name: 'HTML',
                        value: 'html',
                    },
                    {
                        name: 'Both',
                        value: 'both',
                    },
                ],
                default: 'text',
                controlType: 'input'
            },
            {
                displayName: 'Text',
                name: 'bodyText',
                type: 'string',
                typeOptions: {
                    rows: 5,
                },
                default: '',
                description: 'Plain text message of email',
                displayOptions: {
                    show: {
                        emailFormat: ['text', 'both'],
                    },
                },
                controlType: 'input'
            },
            {
                displayName: 'HTML',
                name: 'bodyHtml',
                type: 'string',
                typeOptions: {
                    rows: 5,
                },
                default: '',
                description: 'HTML text message of email',
                displayOptions: {
                    show: {
                        emailFormat: ['html', 'both'],
                    },
                },
                controlType: 'input'
            },
            {
                displayName: 'Options',
                name: 'options',
                type: 'collection',
                placeholder: 'Add option',
                default: {},
                controlType: 'input',
                options: [
                    {
                        ...appendAttributionOption,
                        description:
                            'Whether to include the phrase "This email was sent automatically with n8n" to the end of the email',
                    },
                    {
                        displayName: 'Attachments',
                        name: 'attachments',
                        type: 'string',
                        default: '',
                        description:
                            'Name of the binary properties that contain data to add to email as attachment. Multiple ones can be comma-separated. Reference embedded images or other content within the body of an email message, e.g. &lt;img src="cid:image_1"&gt;',
                        controlType: 'file',
                    },
                    {
                        displayName: 'CC Email',
                        name: 'ccEmail',
                        type: 'string',
                        default: '',
                        placeholder: '<EMAIL>',
                        description: 'Email address of CC recipient',
                        controlType: 'input',
                    },
                    {
                        displayName: 'BCC Email',
                        name: 'bccEmail',
                        type: 'string',
                        default: '',
                        placeholder: '<EMAIL>',
                        description: 'Email address of BCC recipient',
                        controlType: 'input',
                    },
                    {
                        displayName: 'Ignore SSL Issues (Insecure)',
                        name: 'allowUnauthorizedCerts',
                        type: 'boolean',
                        default: false,
                        description: 'Whether to connect even if SSL certificate validation is not possible',
                        controlType: 'input',
                    },
                    {
                        displayName: 'Reply To',
                        name: 'replyTo',
                        type: 'string',
                        default: '',
                        placeholder: '<EMAIL>',
                        description: 'The email address to send the reply to',
                        controlType: 'input',
                    },
                ],
            },
        ],
    };

    async execute(opts: IExecuteOptions): Promise<any> {
        const inputs = opts?.inputs
        return await emailSend({
            smtpHost: inputs?.smtpHost,
            smtpPort: inputs?.smtpPort,
            secure: inputs?.secure,
            smtpUsername: inputs?.smtpUsername,
            smtpPassword: inputs?.smtpPassword,
            rejectUnauthorized: inputs?.rejectUnauthorized,
            fromEmail: inputs?.fromEmail,
            toEmail: inputs?.toEmail,
            subject: inputs?.subject,
            emailFormat: inputs?.emailFormat,
            bodyText: inputs?.bodyText,
            bodyHtml: inputs?.bodyHtml,
        });
    }
}
