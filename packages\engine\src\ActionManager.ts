import { Engine, EngineAction } from "./workflow";
import { FileNodeLoader, IEnumeratorData, INode } from '@repo/common';

class ActionManager {
    readonly #actions: EngineAction[];
    #isLoaded : boolean = false;
    #version : number = 0;

    get actions(): EngineAction[] {
        return [...this.#actions];
    }

    get version(): number {
        return this.#version;
    }

    constructor() {
        this.#actions = [];
    }

    add(action: EngineAction) {
        this.#actions.push(action);
        this.#version = Date.now();
        return this;
    }

    async initialize() {
        if(this.#isLoaded) {
            return;
        }

        const loader = new FileNodeLoader();
        await loader.loadNodes();
        const registry = loader.getNodeRegistry();
        const nodes = registry.getAllNodes();
        nodes.forEach((node: INode) => {
            this.add({
                kind: node.node.kind,
                name: node.node.name,
                description: node.node.description,
                handler: async ({event: _event, step, workflow, workflowAction, state}) => {

                    const subflow = workflow.subflows ? workflow.subflows[workflowAction.id] : null;
                    if(subflow) {
                        const result : Record<string, any> = {};
                        const engine = new Engine({ actions: actionManager.actions });

                        if(node.node.executeMode === 'each') {

                            let enumerator = await step.run(workflowAction.id, async () => {
                                return node.first?.({
                                    id: workflowAction.id,
                                    name: workflowAction.name,
                                    kind: workflowAction.kind,
                                    description: workflowAction.description,
                                    inputs: workflowAction.inputs,
                                    state: state
                                });
                            }) as IEnumeratorData;

                            state.set(workflowAction.id, { ...enumerator })

                            await engine.run({event: _event, step, workflow: subflow, state});

                            while(!enumerator.eof) {
                                const index = enumerator.current??0;

                                enumerator = await step.run(workflowAction.id, async () => {
                                    return node.next?.({
                                        id: workflowAction.id,
                                        name: workflowAction.name,
                                        kind: workflowAction.kind,
                                        description: workflowAction.description,
                                        inputs: workflowAction.inputs,
                                        state: state,
                                        index: index + 1
                                    }) as unknown as IEnumeratorData;
                                });

                                if(!enumerator?.data) {
                                    break;
                                }
                                state.set(workflowAction.id, { ...enumerator });
                                await engine.run({event: _event, step, workflow: subflow, state});
                            }
                        }

                        return result;
                    } else {
                        const data = await step.run(workflowAction.id, async () => {
                            return node.execute?.({
                                id: workflowAction.id,
                                name: workflowAction.name,
                                kind: workflowAction.kind,
                                description: workflowAction.description,
                                inputs: workflowAction.inputs,
                                state: state
                            });
                        });

                        return { data: data };
                    }
                }
            });

            console.log(`action loaded: ${node.node.name}`)
        })

        this.#isLoaded = true;
    }
}

export const actionManager = new ActionManager();