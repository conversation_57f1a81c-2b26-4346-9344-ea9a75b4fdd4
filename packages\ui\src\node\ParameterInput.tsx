"use client";

import React from 'react';
import { UnifiedParameterInput, type UnifiedParameterField } from '../components/forms/unified/UnifiedParameterInput';

interface ParameterInputProps {
  parameter: {
    name: string;
    displayName: string;
    type: string;
    controlType?: string;
    default?: any;
    options?: { name: string; value: any; description?: string }[];
    description?: string;
    placeholder?: string;
    displayOptions?: {
      show?: {
        [key: string]: string[];
      };
    };
   };
  value: any;
  onChange: (name: string, value: any) => void;
  formValues?: Record<string, any>;
  onExpandModeChange?: (expanded: boolean) => void;
}

export const ParameterInput: React.FC<ParameterInputProps> = ({
  parameter,
  value,
  onChange,
  formValues,
  onExpandModeChange
}) => {
  // 将原始 parameter 转换为统一的 field 格式
  const unifiedField: UnifiedParameterField = {
    name: parameter.name,
    displayName: parameter.displayName,
    type: parameter.type,
    controlType: parameter.controlType,
    default: parameter.default,
    options: parameter.options,
    description: parameter.description,
    displayOptions: parameter.displayOptions,
    // Node 变体的默认值
    hint: undefined,
    placeholder: parameter.placeholder, // 传递placeholder属性
    required: false,
    isSecure: false,
    typeOptions: undefined
  };

  return (
    <UnifiedParameterInput
      variant="node"
      field={unifiedField}
      value={value}
      onChange={onChange}
      formValues={formValues}
      onExpandModeChange={onExpandModeChange}
    />
  );
};