{"name": "@repo/ui", "version": "0.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./main": "./src/main/index.tsx", "./main/home": "./src/main/home/<USER>", "./main/agent": "./src/main/agent/index.tsx", "./main/connection": "./src/main/connection/index.tsx", "./controls": "./src/controls/index.tsx", "./main/flow": "./src/main/flow/index.tsx", "./connect": "./src/connect/index.ts", "./components": "./src/components/index.ts", "./themes": "./src/themes/index.ts"}, "scripts": {"lint": "eslint . --max-warnings 0", "generate:component": "turbo gen react-component", "check-types": "tsc --noEmit", "clean": "powershell -Command \"Remove-Item -Path '.turbo', 'node_modules' -Recurse -Force -ErrorAction SilentlyContinue\"", "build": "tsc --declaration --emitDeclarationOnly --outDir dist"}, "devDependencies": {"@repo/engine": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@swc/core": "^1.12.0", "@turbo/gen": "^2.5.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/styled-components": "^5.1.34", "eslint": "^9.25.0", "typescript": "5.8.2"}, "dependencies": {"@codemirror/lang-json": "^6.0.1", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.3", "@codemirror/view": "^6.38.0", "@uiw/react-codemirror": "^4.23.12", "react": "^18.2.0", "react-dom": "^18.2.0", "react-json-view": "^1.21.3", "react-markdown": "^10.1.0", "reactflow": "^11.11.4", "styled-components": "^6.1.8"}}