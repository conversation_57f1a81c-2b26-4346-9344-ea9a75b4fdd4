#!/usr/bin/env node

/**
 * 硅基流动API诊断工具
 * 检测网络连接、DNS解析、API响应时间等
 */

const https = require('https');
const http = require('http');
const dns = require('dns');
const { URL } = require('url');

const SILICONFLOW_ENDPOINTS = [
    'https://api.siliconflow.cn/v1',
    'https://api.siliconflow.cn',
    'https://siliconflow.cn'
];

const FAST_MODELS = [
    'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
    'deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B',
    'Qwen/Qwen2.5-7B-Instruct',
    'internlm/internlm2_5-7b-chat'
];

const SLOW_MODELS = [
    'deepseek-ai/DeepSeek-V3',
    'deepseek-ai/DeepSeek-R1',
    'Qwen/Qwen2.5-72B-Instruct'
];

console.log('🔍 硅基流动API诊断工具');
console.log('=' .repeat(50));

async function dnsLookup(hostname) {
    return new Promise((resolve) => {
        const startTime = Date.now();
        dns.lookup(hostname, (err, address, family) => {
            const latency = Date.now() - startTime;
            if (err) {
                resolve({ success: false, error: err.message, latency });
            } else {
                resolve({ success: true, address, family, latency });
            }
        });
    });
}

async function httpPing(url) {
    return new Promise((resolve) => {
        const startTime = Date.now();
        const urlObj = new URL(url);
        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
            path: urlObj.pathname,
            method: 'HEAD',
            timeout: 10000
        };

        const req = (urlObj.protocol === 'https:' ? https : http).request(options, (res) => {
            const latency = Date.now() - startTime;
            resolve({
                success: true,
                status: res.statusCode,
                latency,
                headers: res.headers
            });
        });

        req.on('error', (err) => {
            const latency = Date.now() - startTime;
            resolve({
                success: false,
                error: err.message,
                latency
            });
        });

        req.on('timeout', () => {
            req.destroy();
            const latency = Date.now() - startTime;
            resolve({
                success: false,
                error: 'Request timeout',
                latency
            });
        });

        req.end();
    });
}

async function testApiEndpoint(endpoint) {
    console.log(`\n🌐 测试端点: ${endpoint}`);
    
    // DNS解析测试
    const hostname = new URL(endpoint).hostname;
    console.log(`   📡 DNS解析测试: ${hostname}`);
    const dnsResult = await dnsLookup(hostname);
    
    if (dnsResult.success) {
        console.log(`   ✅ DNS解析成功: ${dnsResult.address} (${dnsResult.latency}ms)`);
    } else {
        console.log(`   ❌ DNS解析失败: ${dnsResult.error} (${dnsResult.latency}ms)`);
        return { dns: dnsResult, http: null };
    }
    
    // HTTP连接测试
    console.log(`   🔗 HTTP连接测试...`);
    const httpResult = await httpPing(endpoint);
    
    if (httpResult.success) {
        const status = httpResult.status >= 200 && httpResult.status < 400 ? '✅' : '⚠️';
        console.log(`   ${status} HTTP连接成功: ${httpResult.status} (${httpResult.latency}ms)`);
        
        // 检查服务器信息
        if (httpResult.headers.server) {
            console.log(`   📋 服务器: ${httpResult.headers.server}`);
        }
        if (httpResult.headers['x-ratelimit-remaining']) {
            console.log(`   ⏱️ 剩余请求: ${httpResult.headers['x-ratelimit-remaining']}`);
        }
    } else {
        console.log(`   ❌ HTTP连接失败: ${httpResult.error} (${httpResult.latency}ms)`);
    }
    
    return { dns: dnsResult, http: httpResult };
}

async function analyzePerformance() {
    console.log('\n📊 性能分析');
    console.log('-'.repeat(30));
    
    const results = [];
    for (const endpoint of SILICONFLOW_ENDPOINTS) {
        const result = await testApiEndpoint(endpoint);
        results.push({ endpoint, ...result });
    }
    
    // 找出最快的端点
    const validResults = results.filter(r => r.dns.success && r.http.success);
    if (validResults.length > 0) {
        const fastest = validResults.reduce((min, current) => 
            (current.dns.latency + current.http.latency) < (min.dns.latency + min.http.latency) ? current : min
        );
        
        console.log(`\n🏆 推荐端点: ${fastest.endpoint}`);
        console.log(`   总延迟: ${fastest.dns.latency + fastest.http.latency}ms`);
        console.log(`   DNS: ${fastest.dns.latency}ms, HTTP: ${fastest.http.latency}ms`);
    } else {
        console.log('\n❌ 所有端点都无法访问！');
    }
    
    return results;
}

function provideOptimizationSuggestions() {
    console.log('\n💡 优化建议');
    console.log('-'.repeat(30));
    
    console.log('\n🚀 推荐使用更快的模型:');
    FAST_MODELS.forEach(model => {
        console.log(`   ✅ ${model}`);
    });
    
    console.log('\n⚠️ 避免使用较慢的大模型:');
    SLOW_MODELS.forEach(model => {
        console.log(`   ❌ ${model}`);
    });
    
    console.log('\n⚙️ 配置优化:');
    console.log('   • max_tokens: 建议设置为500-1000，避免超过2000');
    console.log('   • timeout: 建议设置为8-15秒');
    console.log('   • 启用流式输出提升用户体验');
    console.log('   • 避免高峰时段（北京时间9-11点，19-21点）');
    
    console.log('\n🔧 故障排除:');
    console.log('   • 检查API密钥是否有效');
    console.log('   • 确认账户余额充足');
    console.log('   • 尝试更换网络环境');
    console.log('   • 考虑使用CDN或代理');
}

async function main() {
    try {
        await analyzePerformance();
        provideOptimizationSuggestions();
        
        console.log('\n' + '='.repeat(50));
        console.log('🏁 诊断完成');
    } catch (error) {
        console.error('❌ 诊断过程中发生错误:', error.message);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    dnsLookup,
    httpPing,
    testApiEndpoint,
    analyzePerformance
}; 