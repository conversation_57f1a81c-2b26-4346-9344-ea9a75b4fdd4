"use client";

import React, { useState, useMemo, useEffect } from 'react';
import styled from 'styled-components';
import { ModelInfo } from '@repo/common';

// 弹窗背景
const ModalBackdrop = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

// 弹窗容器
const ModalContainer = styled.div`
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  height: 80%;
  max-height: 600px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
`;

// 弹窗头部
const ModalHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
`;

const ModalTitle = styled.h2`
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  line-height: 1;
  
  &:hover {
    color: #374151;
  }
`;

const AddButton = styled.button`
  background: #10b981;
  border: none;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  color: white;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12px;
  
  &:hover {
    background: #059669;
  }
`;

// 搜索区域
const SearchSection = styled.div`
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
`;

const SearchContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 12px 16px 12px 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  
  &:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  
  &::placeholder {
    color: #9ca3af;
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  left: 12px;
  color: #9ca3af;
  font-size: 16px;
`;

// 分类标签
const CategoryTabs = styled.div`
  display: flex;
  gap: 8px;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  overflow-x: auto;
`;

const CategoryTab = styled.button<{ $active?: boolean }>`
  padding: 6px 16px;
  border: 1px solid ${props => props.$active ? '#10b981' : '#d1d5db'};
  background: ${props => props.$active ? '#10b981' : 'white'};
  color: ${props => props.$active ? 'white' : '#374151'};
  border-radius: 20px;
  font-size: 14px;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    border-color: #10b981;
    background: ${props => props.$active ? '#059669' : '#f0fdf4'};
  }
`;

// 内容区域
const ModalContent = styled.div`
  flex: 1;
  padding: 24px;
  overflow-y: auto;
`;

const ModelGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
`;

const ModelCard = styled.div<{ $selected?: boolean }>`
  padding: 16px;
  border: 2px solid ${props => props.$selected ? '#10b981' : '#e5e7eb'};
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: ${props => props.$selected ? '#f0fdf4' : 'white'};
  
  &:hover {
    border-color: #10b981;
    background: #f8fafc;
  }
`;

const ModelName = styled.div`
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
`;

const ModelId = styled.div`
  font-size: 12px;
  color: #6b7280;
  font-family: monospace;
  margin-bottom: 8px;
`;

const ModelDescription = styled.div`
  font-size: 14px;
  color: #4b5563;
  line-height: 1.4;
`;

const ModelTags = styled.div`
  display: flex;
  gap: 4px;
  margin-top: 8px;
  flex-wrap: wrap;
`;

const ModelTag = styled.span`
  background: #e5e7eb;
  color: #374151;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
`;

// 底部按钮区域
const ModalFooter = styled.div`
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
`;

const Button = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  padding: 8px 16px;
  border: 1px solid ${props => props.$variant === 'primary' ? '#10b981' : '#d1d5db'};
  background: ${props => props.$variant === 'primary' ? '#10b981' : 'white'};
  color: ${props => props.$variant === 'primary' ? 'white' : '#374151'};
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: ${props => props.$variant === 'primary' ? '#059669' : '#f9fafb'};
  }
`;

// 空状态
const EmptyState = styled.div`
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
`;

interface ModelSelectorModalProps {
  isOpen: boolean;
  onClose: () => void;
  models: ModelInfo[];
  selectedModels?: string[]; // 改为数组支持多选
  onSelectModels: (modelIds: string[]) => void; // 改为返回数组
  title?: string;
  modelUrl?: string;
  apiKey?: string;
  // 保持向后兼容
  selectedModel?: string;
  onSelectModel?: (modelId: string) => void;
}

export const ModelSelectorModal: React.FC<ModelSelectorModalProps> = ({
  isOpen,
  onClose,
  models,
  selectedModels = [],
  onSelectModels,
  title = "硅基流动模型",
  modelUrl,
  apiKey,
  // 向后兼容的props
  selectedModel,
  onSelectModel
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategory, setActiveCategory] = useState('全部');
  const [tempSelectedModel, setTempSelectedModel] = useState<string>(
  selectedModels.length > 0 
    ? selectedModels[0] ?? ''  // 确保第一个元素不是 undefined
    : selectedModel ?? ''      // 确保 selectedModel 不是 undefined
);

  // 当 selectedModels 变化时，同步更新临时选中状态
  useEffect(() => {
    console.log('[ModelSelectorModal] selectedModels changed:', selectedModels);
    const newTempSelected = selectedModels.length > 0 ? selectedModels[0] : (selectedModel || '');
    console.log('[ModelSelectorModal] Setting tempSelectedModel to:', newTempSelected);
    // 确保 newTempSelected 不为 undefined
    setTempSelectedModel(newTempSelected || '');
  }, [selectedModels, selectedModel]);
  const [apiModels, setApiModels] = useState<ModelInfo[]>([]);
  const [isLoadingModels, setIsLoadingModels] = useState(false);
  const [useApiModels, setUseApiModels] = useState(false);

  // 分类列表
  const categories = ['全部', '推理', '视觉', '联网', '免费', '嵌入', '重构', '工具'];

  // 获取模型数据的函数
  const fetchModels = async () => {
    if (!modelUrl) {
      console.log('[ModelSelectorModal] No modelUrl provided, using static models');
      console.log('[ModelSelectorModal] Static models count:', models.length);
      setUseApiModels(false);
      return;
    }
    
    console.log('[ModelSelectorModal] Starting to fetch models from API:', modelUrl);
    console.log('[ModelSelectorModal] Current static models count:', models.length);
    setIsLoadingModels(true);
    
    try {
      let apiUrl = modelUrl;
      
      // 如果是硅基流动的网页链接，转换为API地址
      if (modelUrl.includes('cloud.siliconflow.cn/models')) {
        apiUrl = 'https://api.siliconflow.cn/v1/models';
        console.log('[ModelSelectorModal] Detected SiliconFlow, using API endpoint:', apiUrl);
      }
      
      console.log('[ModelSelectorModal] Sending fetch request to:', apiUrl);
      const headers: Record<string, string> = {};
      
      // 只在有 API Key 时添加认证头
      if (apiKey) {
        headers['Authorization'] = `Bearer ${apiKey}`;
        console.log('[ModelSelectorModal] Adding Authorization header with API key');
      } else {
        console.log('[ModelSelectorModal] No API key provided, making unauthenticated request');
      }
      
      const response = await fetch(apiUrl, { headers });
      
      console.log('[ModelSelectorModal] API response status:', response.status, response.statusText);
      
      if (response.ok) {
        const data = await response.json();
        console.log('[ModelSelectorModal] Raw API response data:', data);
        
        // 这里需要根据不同提供商的 API 响应格式来解析数据
        const parsedModels = parseModelResponse(data, modelUrl);
        console.log('[ModelSelectorModal] Parsed models count:', parsedModels.length);
        console.log('[ModelSelectorModal] Parsed models sample:', parsedModels.slice(0, 3));
        
        setApiModels(parsedModels);
        setUseApiModels(true);
        console.log('[ModelSelectorModal] ✅ Successfully switched to API models');
      } else {
        console.warn('[ModelSelectorModal] ❌ Failed to fetch models from API, using fallback models. Status:', response.status);
        setUseApiModels(false);
      }
    } catch (error) {
      console.error('[ModelSelectorModal] ❌ Error fetching models:', error);
      console.log('[ModelSelectorModal] Falling back to static models');
      setUseApiModels(false);
    } finally {
      setIsLoadingModels(false);
      console.log('[ModelSelectorModal] Finished loading models, isLoadingModels set to false');
    }
  };

  // 解析不同提供商的模型数据
  const parseModelResponse = (data: any, originalUrl: string): ModelInfo[] => {
    console.log('[ModelSelectorModal] Parsing model response, data type:', typeof data);
    console.log('[ModelSelectorModal] Original URL:', originalUrl);
    
    // 硅基流动 API 格式：{ "object": "list", "data": [...] }
    if (data.object === "list" && Array.isArray(data.data)) {
      console.log('[ModelSelectorModal] Detected SiliconFlow API format, models count:', data.data.length);
      return data.data
        .filter((model: any) => model.id && model.id.includes('/')) // 过滤有效的模型ID
        .map((model: any) => ({
          id: model.id,
          name: model.id.split('/').pop() || model.id, // 取最后一部分作为显示名称
          group: getModelGroup(model.id),
          description: `${getModelGroup(model.id)} 模型 - ${model.id}`,
          tags: getModelTags(model.id)
        }));
    }
    
    // OpenAI 格式：{ "data": [...] }
    if (data.data && Array.isArray(data.data)) {
      console.log('[ModelSelectorModal] Detected OpenAI format, models count:', data.data.length);
      return data.data.map((model: any) => ({
        id: model.id,
        name: model.id,
        group: getModelGroup(model.id),
        description: model.description || `${getModelGroup(model.id)} 模型`,
        tags: getModelTags(model.id)
      }));
    }
    
    // 直接的数组格式
    if (Array.isArray(data)) {
      console.log('[ModelSelectorModal] Detected array format, models count:', data.length);
      return data.map((model: any) => {
        const modelId = typeof model === 'string' ? model : model.id;
        return {
          id: modelId,
          name: modelId.split('/').pop() || modelId,
          group: getModelGroup(modelId),
          description: `${getModelGroup(modelId)} 模型`,
          tags: getModelTags(modelId)
        };
      });
    }
    
    // 如果是其他格式，可以在这里添加解析逻辑
    console.log('[ModelSelectorModal] Unknown format, returning empty array');
    return [];
  };

  // 根据模型ID推断分组
  const getModelGroup = (modelId: string): string => {
    const id = modelId.toLowerCase();
    
    // 硅基流动常见模型分组
    if (id.includes('qwen')) return 'Qwen';
    if (id.includes('deepseek')) return 'DeepSeek';
    if (id.includes('glm') || id.includes('chatglm')) return 'GLM';
    if (id.includes('internlm')) return 'InternLM';
    if (id.includes('yi-')) return 'Yi';
    if (id.includes('baichuan')) return 'Baichuan';
    if (id.includes('llama')) return 'Llama';
    if (id.includes('mistral')) return 'Mistral';
    if (id.includes('gemma')) return 'Gemma';
    if (id.includes('phi')) return 'Phi';
    
    // 图像生成模型
    if (id.includes('stable-diffusion') || id.includes('sd')) return 'Stable Diffusion';
    if (id.includes('flux')) return 'FLUX';
    if (id.includes('kolors')) return 'Kolors';
    
    // 语音模型
    if (id.includes('cosyvoice')) return 'CosyVoice';
    if (id.includes('sensevoice')) return 'SenseVoice';
    if (id.includes('fish-speech')) return 'Fish Speech';
    
    // 视频模型
    if (id.includes('hunyuan') && id.includes('video')) return 'HunyuanVideo';
    if (id.includes('ltx-video')) return 'LTX-Video';
    
    // 嵌入和重排序模型
    if (id.includes('bge') || id.includes('embedding')) return 'BGE';
    if (id.includes('bce')) return 'BCE';
    
    // 通用 OpenAI 模型
    if (id.includes('gpt')) return 'OpenAI';
    if (id.includes('claude')) return 'Anthropic';
    if (id.includes('gemini')) return 'Gemini';
    
    return 'Other';
  };

  // 根据模型ID推断标签
  const getModelTags = (modelId: string): string[] => {
    const id = modelId.toLowerCase();
    const tags: string[] = [];
    
    // 基础功能标签
    if (id.includes('chat') || id.includes('instruct')) {
      tags.push('推理');
    }
    
    if (id.includes('vision') || id.includes('vl') || id.includes('视觉')) {
      tags.push('视觉');
    }
    
    if (id.includes('coder') || id.includes('code')) {
      tags.push('代码');
    }
    
    if (id.includes('math')) {
      tags.push('数学');
    }
    
    if (id.includes('embedding')) {
      tags.push('嵌入');
    }
    
    if (id.includes('rerank')) {
      tags.push('重排');
    }
    
    // 模态标签
    if (id.includes('tts') || id.includes('speech') || id.includes('voice') || id.includes('audio')) {
      tags.push('语音');
    }
    
    if (id.includes('diffusion') || id.includes('flux') || id.includes('kolors') || id.includes('image')) {
      tags.push('图像');
    }
    
    if (id.includes('video')) {
      tags.push('视频');
    }
    
    // 特殊标签
    if (id.includes('free') || id.includes('免费') || 
        id.includes('qwen2.5-7b') || id.includes('glm-4-9b')) {
      tags.push('免费');
    }
    
    if (id.includes('pro') || id.includes('turbo') || id.includes('plus')) {
      tags.push('高级');
    }
    
    // 如果没有任何标签，添加默认标签
    if (tags.length === 0) {
      tags.push('推理');
    }
    
    return tags;
  };

  // 组件打开时尝试获取模型
  useEffect(() => {
    if (isOpen && modelUrl) {
      console.log('[ModelSelectorModal] Modal opened with modelUrl, triggering fetchModels');
      fetchModels();
    } else if (isOpen) {
      console.log('[ModelSelectorModal] Modal opened without modelUrl, using static models');
    }
  }, [isOpen, modelUrl]);

  // 当前使用的模型列表
  const currentModels = useApiModels ? apiModels : models;
  
  // 添加数据源切换日志
  useEffect(() => {
    console.log(`[ModelSelectorModal] 🔄 Data source changed: ${useApiModels ? 'API' : 'Static'}`);
    console.log(`[ModelSelectorModal] Current models count: ${currentModels.length}`);
  }, [useApiModels, currentModels.length]);

  // 过滤模型
  const filteredModels = useMemo(() => {
    // 首先去重处理，以model.id为唯一标识
    const uniqueModels = currentModels.reduce((acc, current) => {
      const existing = acc.find(item => item.id === current.id);
      if (!existing) {
        acc.push(current);
      } else {
        // 如果已存在，保留更完整的信息（有description的优先）
        if (current.description && !existing.description) {
          const index = acc.findIndex(item => item.id === current.id);
          acc[index] = current;
        }
      }
      return acc;
    }, [] as ModelInfo[]);
    
    let filtered = uniqueModels;

    // 按搜索词过滤
    if (searchTerm) {
      filtered = filtered.filter(model => 
        model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        model.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        model.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // 按分类过滤（这里可以根据实际的标签系统来实现）
    if (activeCategory !== '全部') {
      filtered = filtered.filter(model => 
        model.tags?.includes(activeCategory.toLowerCase()) ||
        model.group?.toLowerCase().includes(activeCategory.toLowerCase())
      );
    }

    return filtered;
  }, [currentModels, searchTerm, activeCategory]);

  const handleConfirm = () => {
    if (tempSelectedModel) {
      if (onSelectModels) {
        onSelectModels([tempSelectedModel]);
      } else if (onSelectModel) {
        // 向后兼容：如果使用旧接口
        onSelectModel(tempSelectedModel);
      }
    }
    onClose();
  };

  const handleAddModel = () => {
    // 这里可以添加新增模型的逻辑
    console.log('添加新模型功能');
  };

  if (!isOpen) return null;

  return (
    <ModalBackdrop onClick={onClose}>
      <ModalContainer onClick={(e) => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>{title}</ModalTitle>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <AddButton onClick={handleAddModel}>+</AddButton>
            <CloseButton onClick={onClose}>×</CloseButton>
          </div>
        </ModalHeader>

        <SearchSection>
          <SearchContainer>
            <SearchIcon>🔍</SearchIcon>
            <SearchInput
              placeholder="搜索模型 ID 或名称"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </SearchContainer>
        </SearchSection>

        <CategoryTabs>
          {categories.map((category) => (
            <CategoryTab
              key={category}
              $active={activeCategory === category}
              onClick={() => setActiveCategory(category)}
            >
              {category}
            </CategoryTab>
          ))}
        </CategoryTabs>

        <ModalContent>
          {filteredModels.length === 0 ? (
            <EmptyState>
              {searchTerm || activeCategory !== '全部' ? '未找到匹配的模型' : '暂无可用模型'}
            </EmptyState>
          ) : (
            <ModelGrid>
              {filteredModels.map((model) => (
                <ModelCard
                  key={model.id}
                  $selected={tempSelectedModel === model.id}
                  onClick={() => {
                    if (tempSelectedModel === model.id) {
                      // 取消选择
                      setTempSelectedModel('');
                    } else {
                      // 选择新模型（单选）
                      setTempSelectedModel(model.id);
                    }
                  }}
                >
                  <ModelName>{model.name}</ModelName>
                  <ModelId>{model.id}</ModelId>
                  {model.description && (
                    <ModelDescription>{model.description}</ModelDescription>
                  )}
                  {model.tags && model.tags.length > 0 && (
                    <ModelTags>
                      {model.tags.map((tag, index) => (
                        <ModelTag key={index}>{tag}</ModelTag>
                      ))}
                    </ModelTags>
                  )}
                </ModelCard>
              ))}
            </ModelGrid>
          )}
        </ModalContent>

        <ModalFooter>
          <Button onClick={onClose}>取消</Button>
          <Button 
            $variant="primary" 
            onClick={handleConfirm}
            disabled={!tempSelectedModel}
          >
            确认选择{tempSelectedModel ? ' (1)' : ''}
          </Button>
        </ModalFooter>
      </ModalContainer>
    </ModalBackdrop>
  );
};