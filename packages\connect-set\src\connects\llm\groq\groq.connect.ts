import { ILLMConnect, ILLMOverview, ConnectTestResult } from '@repo/common';
import { 
    createApiKeyField, 
    createBaseUrlField, 
    testLLMConnection
} from '../utils/llm-fields';

const ConnectConfig: ILLMOverview = {
    id: 'groq',
    name: 'Groq',
    type: 'llm' as const,
    provider: 'groq',
    icon: 'groq.svg',
    tags: ["international"],
    description: 'Groq AI模型连接',
    version: '1.0.0',
    api: { url: 'https://api.groq.com/openai', suffix: '/v1/chat/completions' },
    driver: 'openai',
    about: {
        apiHost: 'https://api.groq.com/openai',
        docUrl: 'https://console.groq.com/docs/quickstart',
        modelUrl: 'https://console.groq.com/docs/models',
        getKeyUrl: 'https://console.groq.com/keys'
    }
};

export const GroqConnect: ILLMConnect = {
    overview: ConnectConfig,
    detail: {
        supportedModels: [
            {id: 'llama-3.1-405b-reasoning', name: 'llama-3.1-405b-reasoning',group: 'LLama'},
            {id: 'llama-3.1-70b-versatile', name: 'llama-3.1-70b-versatile',group: 'LLama'},
            {id: 'llama-3.1-8b-instant', name: 'llama-3.1-8b-instant',group: 'LLama'},
            {id: 'mixtral-8x7b-32768', name: 'mixtral-8x7b-32768',group: 'Mixtral'},
            {id: 'gemma-7b-it', name: 'gemma-7b-it',group: 'Gemma'},
            {id: 'gemma2-9b-it', name: 'gemma2-9b-it',group: 'Gemma'}
        ],
        fields: [
            createApiKeyField('gsk_...'),
            createBaseUrlField(ConnectConfig.api.url),
        ],
        validateConnection: true,
        connectionTimeout: 10
    },

    async test(config: Record<string, any>, message?: string): Promise<ConnectTestResult> {
        return testLLMConnection(
            ConnectConfig.id,
            config,
            ConnectConfig.api.url+ConnectConfig.api.suffix||'',
            message
        );
    }
}; 