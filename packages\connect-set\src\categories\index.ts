/**
 * 连接分类定义
 */

import { IConnectCategory } from '@repo/common';

/**
 * LLM连接分类
 */
export const CATEGORIES: IConnectCategory[] = [
    {
        id: 'll',
        name: '通用大模型',
        description: 'deepseek、<PERSON><PERSON>、<PERSON>tGP<PERSON>、<PERSON>、Gemini等对话式AI模型或嵌入模型',
        type: 'llm'
    },
    {
        id: 'relational-db',
        name: '关系数据库',
        description: 'MySQL、PostgreSQL、Oracle、SQL Server等关系型数据库',
        type: 'rd-db'
    },
    {
        id: 'nosql-db',
        name: 'NoSQL数据库',
        description: 'MongoDB、Redis、ClickHouse、Elasticsearch等非关系型数据库',
        type: 'nosql-db'
    },
    {
        id: 'nosql-db',
        name: 'NoSQL数据库',
        description: 'MongoDB、Redis、ClickHouse、Elasticsearch等非关系型数据库',
        type: 'nosql-db'
    },
    {
        id: 'rest-api',
        name: 'REST API',
        description: 'RESTful Web服务接口',
        type: 'http'
    },
    {
        id: 'graphql-api',
        name: 'GraphQL API',
        description: 'GraphQL查询接口',
        type: 'http'
    },
    {
        id: 'webhook',
        name: 'Webhook',
        description: 'Webhook回调接口',
        type: 'http'
    }
];

export const ALL_CATEGORIES: IConnectCategory[] = [
    ...CATEGORIES
]; 