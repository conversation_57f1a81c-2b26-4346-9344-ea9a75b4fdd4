import { NextResponse } from "next/server";
import { initializeNodes, getAllCategories } from "@repo/node-set";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const kind = searchParams.get('kind');

    const nodeRegistry = await initializeNodes();
    if (kind) {
      // If kind is provided, return the specific node
      const node = nodeRegistry.getNodeByKind(kind);
      if (node) {
        return NextResponse.json({ node });
      } else {
        return NextResponse.json(
          { message: `Node with kind ${kind} not found` },
          { status: 404 }
        );
      }
    } else {
      // If no kind is provided, return all categorized nodes
      const categories = getAllCategories();
      const nodesByCategory = nodeRegistry.getNodesByCategoryNotWithDetail();
      console.log(nodesByCategory);

      // Validate that we have nodes loaded
      if (Object.keys(nodesByCategory).length === 0) {
        console.warn('No nodes were loaded. Check node registration for errors.');
      }

      // Build complete category and node association data
      const result = categories.map(category => ({
        ...category,
        nodes: nodesByCategory[category.id] || []
      }));

      // Add cache control headers
      const response = NextResponse.json({ dataSource: result });
      // response.headers.set('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
      return response;
    }
  } catch (error) {
    console.error("Failed to get node information:", error);
    return NextResponse.json(
      {
        message: "Failed to get node information",
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
