import { McpServerConfig } from "./McpInterfaces";
import {
    Agent,
    AgentResult,
    StateData,
    Message,
    TextMessage,
    ToolMessage,
    ToolCallMessage,
    type MaybePromise
} from "@/agentic";

export enum ModelSeries {
    OpenAI = "openai",
    Anthropic = "anthropic",
    Gemini = "gemini",
    Grok = "grok",
    DeepSeek = "deepseek",
    SiliconFlow = "siliconflow",
}

export enum ToolMode {
    Function = "function",
    Prompt = "prompt",
}

export interface ChatModel {
    series?: ModelSeries | null;
    model?: string | null;
    apiKey?: string | null;
    baseUrl?: string | null;
    toolMode?: ToolMode | null;
    stream?: boolean | false;
}

export interface AiAgent {
    id: string;
    name: string;
    description?: string | null | undefined;
    systemMessage?: string | null | undefined;
    chatModel?: ChatModel | null | undefined;
    mcpServers?: McpServerConfig[] | null | undefined;
    abilities?: AgentAbilities | null | undefined;
}

export interface AgentAbilities {
    useInternet?: boolean | false;          // 是否联网
    useWorkflow?: boolean | false;          // 是否可用工作流
    maxTokens?: number | 512;               // 最大token
    enableThinking?: boolean | true;        // 深度思考
    thinkingBudget?: number | 4096;         // 思想链输出的最大标记数
    minP?: number | 0.05;                   // 根据 Token 概率进行调整的动态筛选阈值
    topP?: number | 0.7;                    // 根据累积概率动态调整每个预测标记的选择数
    topK?: number | 50;                     //
    temperature?: number | 0.7;             // 确定响应中的随机程度
    frequencyPenalty?: number | 1;          // 要返回的代数
    maxIter?: number | 5;                   // 最大迭代次数
}

export interface MCPToolInputSchema {
    type: string
    title: string
    description?: string
    required?: string[]
    properties: Record<string, object>
}

export interface MCPTool {
    id: string
    serverId: string
    serverName: string
    name: string
    description?: string
    inputSchema: MCPToolInputSchema
}

export interface AgentRunOptions {
    input: string;
    threadId: string;
    userId: string;
    extSystemMessage?: string;
    state?: Record<string, any>;
    waitOutput: boolean | false
    stream: boolean | false;
}

export interface StreamMessage {
    type: StreamType,
    result?: AgentResult | null,
    message?: Message | TextMessage | ToolCallMessage | null,
    threadId: string,
    isCompleted: boolean | false,
}

export enum StreamType {
    chunk = "chunk",
    result = "result"
}

declare module "./agentic/types" {
    interface TextMessage {
        reasoning_content?: string;
        streamEnd?: boolean | false;
    }

    interface ToolCallMessage {
        content?: string;
        reasoning_content?: string;
    }

    interface ToolResultMessage {
        reasoning_content?: string;
    }
}

declare module "./agentic/agent" {

    namespace Agent {
        interface Lifecycle<T extends StateData> {
            onStream?: (
                args: Agent.LifecycleArgs.Stream<T>
            ) => MaybePromise<void>
        }

        namespace LifecycleArgs {
            interface Stream<T extends StateData> {
                message: TextMessage;
            }
        }
    }
}