// tsup.WorkflowTrigger.json
import { defineConfig } from "tsup";
var tsup_config_default = defineConfig({
  entry: ["src/index.ts", "src/nodes/**/node.ts"],
  outDir: "dist",
  format: ["cjs", "esm"],
  dts: true,
  splitting: false,
  sourcemap: true,
  clean: true,
  treeshake: true,
  outExtension({ format }) {
    return {
      js: `.${format === "cjs" ? "js" : "mjs"}`
    };
  }
});
export {
  tsup_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidHN1cC5jb25maWcudHMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9faW5qZWN0ZWRfZmlsZW5hbWVfXyA9IFwiRDpcXFxcU291cmNlQ29kZVxcXFxjb2ZseVxcXFxwYWNrYWdlc1xcXFxub2RlLXNldFxcXFx0c3VwLmNvbmZpZy50c1wiO2NvbnN0IF9faW5qZWN0ZWRfZGlybmFtZV9fID0gXCJEOlxcXFxTb3VyY2VDb2RlXFxcXGNvZmx5XFxcXHBhY2thZ2VzXFxcXG5vZGUtc2V0XCI7Y29uc3QgX19pbmplY3RlZF9pbXBvcnRfbWV0YV91cmxfXyA9IFwiZmlsZTovLy9EOi9Tb3VyY2VDb2RlL2NvZmx5L3BhY2thZ2VzL25vZGUtc2V0L3RzdXAuY29uZmlnLnRzXCI7aW1wb3J0IHsgZGVmaW5lQ29uZmlnIH0gZnJvbSAndHN1cCc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBkZWZpbmVDb25maWcoe1xyXG4gIGVudHJ5OiBbJ3NyYy9pbmRleC50cycsICdzcmMvbm9kZXMvKiovbm9kZS50cyddLFxyXG4gIG91dERpcjogJ2Rpc3QnLFxyXG4gIGZvcm1hdDogWydjanMnLCAnZXNtJ10sXHJcbiAgZHRzOiB0cnVlLFxyXG4gIHNwbGl0dGluZzogZmFsc2UsXHJcbiAgc291cmNlbWFwOiB0cnVlLFxyXG4gIGNsZWFuOiB0cnVlLFxyXG4gIHRyZWVzaGFrZTogdHJ1ZSxcclxuICBvdXRFeHRlbnNpb24oeyBmb3JtYXQgfSkge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAganM6IGAuJHtmb3JtYXQgPT09ICdjanMnID8gJ2pzJyA6ICdtanMnfWBcclxuICAgIH07XHJcbiAgfVxyXG59KTsiXSwKICAibWFwcGluZ3MiOiAiO0FBQXVRLFNBQVMsb0JBQW9CO0FBRXBTLElBQU8sc0JBQVEsYUFBYTtBQUFBLEVBQzFCLE9BQU8sQ0FBQyxnQkFBZ0Isc0JBQXNCO0FBQUEsRUFDOUMsUUFBUTtBQUFBLEVBQ1IsUUFBUSxDQUFDLE9BQU8sS0FBSztBQUFBLEVBQ3JCLEtBQUs7QUFBQSxFQUNMLFdBQVc7QUFBQSxFQUNYLFdBQVc7QUFBQSxFQUNYLE9BQU87QUFBQSxFQUNQLFdBQVc7QUFBQSxFQUNYLGFBQWEsRUFBRSxPQUFPLEdBQUc7QUFDdkIsV0FBTztBQUFBLE1BQ0wsSUFBSSxJQUFJLFdBQVcsUUFBUSxPQUFPLEtBQUs7QUFBQSxJQUN6QztBQUFBLEVBQ0Y7QUFDRixDQUFDOyIsCiAgIm5hbWVzIjogW10KfQo=
