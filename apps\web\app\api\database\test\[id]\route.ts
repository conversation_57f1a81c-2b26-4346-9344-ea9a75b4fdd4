import { NextRequest, NextResponse } from 'next/server';
import { MySQLConnect, PostgreSQLConnect, SQLServerConnect, OracleConnect, DB2Connect, KingbaseConnect, DamengConnect } from '@repo/connect-set/connects/database';

// 支持的数据库提供商配置
const DATABASE_PROVIDERS = {
    mysql: MySQLConnect,
    postgresql: PostgreSQLConnect,
    sqlserver: SQLServerConnect,
    oracle: OracleConnect,
    db2: DB2Connect,
    kingbase: KingbaseConnect,
    dameng: DamengConnect
};

/**
 * POST /api/database/test/[id]
 * 测试单个数据库连接
 * 路径参数:
 * - id: 提供商名称或连接ID
 * 请求体:
 * - provider?: 提供商名称（如果id不是提供商名称）
 * - config: 连接配置
 */
export async function POST(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const { id } = params;
        const body = await request.json();
        const { provider, config } = body;

        if (!config) {
            return NextResponse.json(
                {
                    success: false,
                    error: '参数错误',
                    message: 'config 是必填字段'
                },
                { status: 400 }
            );
        }

        // 确定提供商名称：优先使用provider参数，否则使用id
        const providerName = provider || id;

        // 验证提供商是否支持
        const dbConnect = DATABASE_PROVIDERS[providerName as keyof typeof DATABASE_PROVIDERS];
        if (!dbConnect) {
            return NextResponse.json(
                {
                    success: false,
                    error: '不支持的提供商',
                    message: `数据库提供商 "${providerName}" 不受支持`,
                    supportedProviders: Object.keys(DATABASE_PROVIDERS)
                },
                { status: 400 }
            );
        }

        // 执行测试
        const result = await dbConnect.test(config);

        return NextResponse.json({
            success: true,
            data: {
                id,
                provider: providerName,
                ...result
            },
            message: result.message || '测试完成'
        });

    } catch (error) {
        console.error(`数据库测试失败 [${params.id}]:`, error);
        return NextResponse.json(
            {
                success: false,
                error: '数据库测试失败',
                message: error instanceof Error ? error.message : '未知错误'
            },
            { status: 500 }
        );
    }
}

/**
 * GET /api/database/test/[id]
 * 获取特定数据库提供商的信息
 * 路径参数:
 * - id: 提供商名称
 */
export async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const { id } = params;

        // 检查提供商是否支持
        const dbConnect = DATABASE_PROVIDERS[id as keyof typeof DATABASE_PROVIDERS];
        
        if (!dbConnect) {
            return NextResponse.json(
                {
                    success: false,
                    error: '提供商不存在',
                    message: `数据库提供商 "${id}" 不受支持`,
                    supportedProviders: Object.keys(DATABASE_PROVIDERS)
                },
                { status: 404 }
            );
        }

        return NextResponse.json({
            success: true,
            data: {
                provider: id,
                name: dbConnect.connect.name,
                description: dbConnect.connect.description,
                defaultPort: dbConnect.detail.defaultPort,
                supportedFeatures: dbConnect.detail.supportedFeatures,
                fields: dbConnect.detail.fields.map(field => ({
                    name: field.name,
                    displayName: field.displayName,
                    type: field.type,
                    required: field.required || false,
                    default: field.default,
                    description: field.description
                })),
                allProviders: Object.keys(DATABASE_PROVIDERS)
            },
            message: `数据库提供商 "${id}" 信息获取成功`
        });

    } catch (error) {
        console.error(`获取数据库提供商信息失败 [${params.id}]:`, error);
        return NextResponse.json(
            {
                success: false,
                error: '获取数据库提供商信息失败',
                message: error instanceof Error ? error.message : '未知错误'
            },
            { status: 500 }
        );
    }
} 