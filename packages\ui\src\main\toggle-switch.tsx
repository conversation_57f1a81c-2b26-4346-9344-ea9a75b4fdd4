"use client";

import React from 'react';
import styled from 'styled-components';

const SwitchContainer = styled.label`
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  margin-right: 8px;
`;

const SwitchInput = styled.input`
  opacity: 0;
  width: 0;
  height: 0;

  &:checked + span {
    background-color: ${({ theme }) => theme.colors.accent};
  }

  &:checked + span:before {
    transform: translateX(20px);
  }
`;

const SwitchSlider = styled.span`
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${({ theme }) => theme.colors.border};
  transition: 0.4s;
  border-radius: 34px;

  &:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: ${({ theme }) => theme.colors.secondary};
    transition: 0.4s;
    border-radius: 50%;
  }
`;

const SwitchLabel = styled.div`
  display: flex;
  align-items: center;
  font-size: 14px;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

interface ToggleSwitchProps {
  isActive: boolean;
  onChange: (isActive: boolean) => void;
  label?: string;
  className?: string;
}

export const ToggleSwitch: React.FC<ToggleSwitchProps> = ({
  isActive,
  onChange,
  label,
  className
}) => {
  return (
    <SwitchLabel className={className}>
      <SwitchContainer>
        <SwitchInput
          type="checkbox"
          checked={isActive}
          onChange={(e) => onChange(e.target.checked)}
        />
        <SwitchSlider />
      </SwitchContainer>
      {label}
    </SwitchLabel>
  );
};
