import { injectable } from 'inversify';
import type { IExecuteOptions, INode, INodeBasic, INodeDetail } from '@repo/common';
import { NodeLink } from '@repo/common';

@injectable()
export class Markdown implements INode {
	node: INodeBasic = {
		kind: 'manual',
		name: '手工触发',
		categories: ['trigger'],
		version: 1,
		position: [0, 0],
		description: "手工触发任务，多用于测试",
		icon: 'manual.svg',
		link: {
			outputs: [NodeLink.Data]
		}
	};
	detail: INodeDetail = {
		fields: [
			// 模式选择器（核心联动字段）
			{
				displayName: '说明',      // 显示名称
				name: 'note',                 // 字段名
				type: 'string',              // 字段类型
				default: 'htmlToMarkdown',    // 默认值
				controlType: 'Note'        // 提示AI这是联动触发器
			}
		],
	};

	async execute(opts: IExecuteOptions): Promise<any> {
		return "this a markdown executing function";
	}
}
