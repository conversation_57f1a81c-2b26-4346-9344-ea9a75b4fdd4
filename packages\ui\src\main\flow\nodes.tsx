"use client";

import styled from 'styled-components';
import { glassBase } from '../home/<USER>';

// 优化的样式组件 - 减少不必要的计算
export const MenuContainer = styled.div<{ $collapsed?: boolean }>`
    width: ${props => props.$collapsed ? '0' : '315px'};
    height: 100%;
    font-family: 'PingFang SC', 'Microsoft YaHei', 'SimSun', sans-serif;
    /* 应用液体磨砂玻璃背景
    background: ${({ theme }) => theme.mode === 'dark'
      ? '016A7D'
      : 'rgba(32, 58, 83, 0.7)'
    };
    
    border-right: 1px solid ${({ theme }) => theme.mode === 'dark'
      ? 'rgba(59, 130, 246, 0.2)'
      : 'rgba(59, 130, 246, 0.15)'
    };
    ${glassBase}
    border-radius: 0;
    transition: width 0.3s ease;
    position: relative;
    user-select: none;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    /* 增强效果 */
    box-shadow: ${({ theme }) => theme.mode === 'dark'
      ? 'inset 0 0 20px rgba(117, 167, 247, 0.15), 0 0 50px rgba(59, 130, 246, 0.1)'
      : 'inset 0 0 20px rgba(59, 130, 246, 0.08), 0 0 50px rgba(59, 130, 246, 0.05)'
    };
`;

export const MenuHeader = styled.div<{ $collapsed?: boolean }>`
  position: relative;
  padding-left: ${props => props.$collapsed ? "0px" : "10px"};
  height: 12px;
  vertical-align: middle;
  display: flex;
  width: 100%;
  padding:10px;
  align-items: center;
  justify-content: center;
 // background: ${props => props.$collapsed ? (props.theme?.colors?.sidebarBg || '#ffffff') : 'none'};
  border: none;
  cursor: pointer;
  user-select: none;
  z-index: 10;
`;

export const CollapseFlag = styled.div`
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: baseline;
  gap: 2px;
  font-size: 12px;
  user-select: none;
  color: ${({ theme }) => theme.mode === 'dark' ? '#95A3C6' : '#64748b'};
  
  &:hover {
    color: ${({ theme }) => theme.mode === 'dark' ? '#fff' : '#3b82f6'};
    text-shadow: 0 0 10px rgba(59, 130, 246, 0.4);
  } 
`;


//目录的总体样式
export const MenuContent = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 0 5px;
  
  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }
    
  background:${({ theme }) => theme.mode === 'dark' ? '#333F50' : '#ffffff'};
  //滚轴样式
  &::-webkit-scrollbar-track {
    background: ${({ theme }) => theme.mode === 'dark' 
      ? 'rgba(15, 23, 42, 0.3)' 
      : 'rgba(248, 250, 252, 0.3)'
    };
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: ${({ theme }) => theme.mode === 'dark' 
      ? 'rgba(59, 130, 246, 0.3)' 
      : 'rgba(59, 130, 246, 0.2)'
    };
    border-radius: 3px;
    
    &:hover {
      background: ${({ theme }) => theme.mode === 'dark' 
        ? 'rgba(59, 130, 246, 0.5)' 
        : 'rgba(59, 130, 246, 0.4)'
      };
    }
  }
`;

// 简化的分类头部 - 避免复杂的 Collapse 组件
export const CategoryHeader = styled.div<{ $expanded?: boolean }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 15px 16px 15px;
   background: ${({ $expanded, theme }) => $expanded
    ? (theme.mode === 'dark'
      ? 'rgba(59, 130, 246, 0.25)'
      : 'rgba(59, 130, 246, 0.15)')
    : (theme.mode === 'dark'
      ? 'rgba(21, 42, 90, 0.3)'
      : 'rgba(248, 250, 252, 0.4)')
  };
  
   color: ${({ theme }) => theme?.colors?.textSecondary || '#666'};
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
  // border-top: 1px solid ${({ theme }) => theme?.colors?.border || '#d9d9d9'};  
  // border-bottom: 1px solid ${({ theme }) => theme?.colors?.border || '#d9d9d9'};  
  &:hover {
    background-color: ${({ theme }) => theme?.colors?.buttonHover || '#f5f5f5'};
  }
`;


export const CategoryContent = styled.div<{ $expanded: boolean }>`
  max-height: ${props => props.$expanded ? '1000px' : '0'};
  overflow: hidden;
  transition: max-height 0.3s ease;
  background-color: ${({ theme }) => theme?.colors?.sidebarBg || '#ffffff'};
  margin-bottom: 0;
`;

export const CategoryHeaderContent = styled.div`
  display: flex;
  align-items: center;
  flex: 1;
`;

export const CategoryIcon = styled.div`
  margin-right: 12px;
  width: 28x;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    color: ${({ theme }) => theme?.colors?.textSecondary || '#666'};
  }
`;

export const CategoryTitle = styled.div`
  font-weight: 800;
  color: ${({ theme }) => theme?.colors?.textPrimary || '#333'};
  font-size: 15px;
`;


export const CategoryText = styled.div`
    flex: 1;
`;

export const CategoryDescription = styled.div`
  color: ${({ theme }) => theme?.colors?.textTertiary || '#999'};
  font-size: 12px;
`;

export const ExpandIcon = styled.div<{ $expanded: boolean }>`
  color: ${({ theme }) => theme?.colors?.textSecondary || '#666'};
  transition: transform 0.2s ease;
  transform: ${props => props.$expanded ? 'rotate(90deg)' : 'rotate(0deg)'};
`;

// 简化的节点项 - 移除 Tooltip 包装
export const NodeItem = styled.div`
  padding: 12px 15px;
  cursor: grab;
  border-radius: 0px;
  background: ${({ theme }) => theme.mode === 'dark'
    ? 'rgba(15, 23, 42, 0.4)'
    : 'rgba(248, 250, 252, 0.6)'
  };
  backdrop-filter: blur(4px);
  border: 1px solid ${({ theme }) => theme.mode === 'dark'
    ? 'rgba(59, 130, 246, 0.15)'
    : 'rgba(59, 130, 246, 0.1)'
  };
  //transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(168, 85, 247, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }

  &:hover {
    background: ${({ theme }) => theme.mode === 'dark'
      ? 'rgba(59, 130, 246, 0.2)'
      : 'rgba(59, 130, 246, 0.1)'
    };
    border-color: rgba(59, 130, 246, 0.4);
    //transform: translateY(-2px);
    box-shadow: ${({ theme }) => theme.mode === 'dark'
      ? '0 4px 12px rgba(59, 130, 246, 0.25)'
      : '0 4px 12px rgba(59, 130, 246, 0.12)'
    };
  }
`;

export const NodeContent = styled.div`
  display: flex;
  align-items: center;
`;

export const NodeIconContainer = styled.div`
  margin-right: 12px;
`;

export const NodeIcon = styled.img`
  width: 20px;
  height: 20px;
`;

export const NodeTextContent = styled.div`
  flex: 1;
`;

export const NodeTitle = styled.div`
  font-weight: 500;
  color: ${({ theme }) => theme?.colors?.textPrimary || '#333'};
  font-size: 14px;
`;

export const NodeDescription = styled.div`
  font-size: 12px;
  color: ${({ theme }) => theme?.colors?.textTertiary || '#999'};
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-top: 2px;
`;

export const EmptyState = styled.div`
  padding: 20px;
  color: ${({ theme }) => theme?.colors?.textTertiary || '#999'};
  text-align: center;
  font-size: 13px;
`;

export const SearchContainer = styled.div`
  padding:6px;
  // padding: 8px 15px;
  //  margin-top: 15px;
  /*  
    border: 1px solid ${({ theme }) => theme.mode === 'dark'
    ? 'rgba(150, 185, 241, 0.2)'
    : 'rgba(59, 130, 246, 0.15)'
  };*/
`;


// Search input for node menu
export const NodeMenuSearch = styled.input`
  width: 100%;
  padding: 6px 12px;
  background: ${({ theme }) => theme.mode === 'dark'
    ? 'rgba(15, 23, 42, 0.4)'
    : 'rgba(248, 250, 252, 0.6)'
  };
  border: 1px solid ${({ theme }) => theme.mode === 'dark'
    ? 'rgba(59, 130, 246, 0.2)'
    : 'rgba(59, 130, 246, 0.15)'
  };
  border-radius: 4px;
  font-size: 13px;
  color: ${({ theme }) => theme.mode === 'dark' ? '#e2e8f0' : '#1e293b'};
  
  &::placeholder {
    color: ${({ theme }) => theme.mode === 'dark' ? '#64748b' : '#94a3b8'};
  }
  
  &:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: ${({ theme }) => theme.mode === 'dark'
      ? '0 0 0 3px rgba(59, 130, 246, 0.2), inset 0 2px 6px rgba(0, 0, 0, 0.15)'
      : '0 0 0 3px rgba(59, 130, 246, 0.1), inset 0 2px 6px rgba(0, 0, 0, 0.05)'
    };
    background: ${({ theme }) => theme.mode === 'dark'
      ? 'rgba(59, 130, 246, 0.1)'
      : 'rgba(59, 130, 246, 0.05)'
    };
  }
  
  &:hover {
    border-color: rgba(59, 130, 246, 0.3);
  }
`;