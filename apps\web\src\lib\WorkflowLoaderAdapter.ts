import { WorkflowLoadAdapter } from "@repo/engine/src/WorkflowInterfaces";
import { getAllWorkflowConfigs, getWorkflowConfigById, WorkflowConfig, WorkflowEngineConfig } from "@repo/db"
import { Workflow } from "@repo/engine/src";

class WorkflowLoaderAdapter implements WorkflowLoadAdapter {

    async list(limit: number | undefined): Promise<Workflow[]> {
        const configs : Workflow[] = [];
        const records = await getAllWorkflowConfigs(limit);
        for(let config of records) {
            configs.push({
                id: config.id,
                name: config.name,
                actions: config.nodesInfo ? JSON.parse(config.nodesInfo) : [],
                edges: config.relation ? JSON.parse(config.relation) : [],
            } as unknown as Workflow);
        }
        return configs;
    }

    async get(id: string): Promise<Workflow | null> {
        const config = await getWorkflowConfigById(id);
        if(!config) {
            return null;
        }

        return {
            id: config.id,
            name: config.name,
            actions: config.nodesInfo ? JSON.parse(config.nodesInfo) : [],
            edges: config.relation ? JSON.parse(config.relation) : [],
        } as unknown as Workflow;
    }
}

export const workflowLoaderAdapter = new WorkflowLoaderAdapter() ;