import { ILLMConnect, ILLMOverview, ConnectTestResult } from '@repo/common';
import { 
    createApiKeyField, 
    createBaseUrlField, 
    testLLMConnection
} from '../utils/llm-fields';

const ConnectConfig: ILLMOverview = {
    id: 'deepseek',
    name: 'DeepSeek',
    type: 'llm' as const,
    provider: 'deepseek',
    icon: 'deepseek.svg',
    tags: ["domestic"],
    description: 'DeepSeek AI模型连接',
    version: '1.0.0',
    api: { url: 'https://api.deepseek.com', suffix: '/v1/chat/completions' },
    about: {
        apiHost: 'https://api.deepseek.com/',
        docUrl: 'https://platform.deepseek.com/api-docs/',
        modelUrl: 'https://platform.deepseek.com/api-docs/',
        getKeyUrl: 'https://platform.deepseek.com/'
    }
};

export const DeepSeekConnect: ILLMConnect = {
    overview: ConnectConfig,
    detail: {
        supportedModels: [
            {id: 'deepseek-chat', name: 'Deep<PERSON>eek Chat',group: 'DeepSeek Chat'},
            {id: 'deepseek-coder', name: 'DeepSeek Coder',group: 'DeepSeek Coder'},
            {id: 'deepseek-reasoner', name: 'DeepSeek Reasoner',group: 'DeepSeek Reasoner'}
        ],
        fields: [
            createApiKeyField('sk-...'),
            createBaseUrlField(ConnectConfig.api.url),
        ],
        validateConnection: true,
        connectionTimeout: 10
    },

    async test(config: Record<string, any>, message?: string): Promise<ConnectTestResult> {
        return testLLMConnection(
            ConnectConfig.id,
            config,
            ConnectConfig.api.url+ConnectConfig.api.suffix||'',
            message
        );
    }
}; 