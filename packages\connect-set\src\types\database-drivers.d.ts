// MySQL2 驱动类型声明
declare module 'mysql2/promise' {
  export interface Connection {
    execute(sql: string, values?: any[]): Promise<[any[], any]>;
    end(): Promise<void>;
  }
  
  export interface ConnectionOptions {
    host?: string;
    port?: number;
    user?: string;
    password?: string;
    database?: string;
    charset?: string;
    ssl?: any;
    connectTimeout?: number;
    acquireTimeout?: number;
    timeout?: number;
    socketPath?: string;
  }
  
  export function createConnection(config: ConnectionOptions): Promise<Connection>;
}

// MySQL 经典驱动类型声明
declare module 'mysql' {
  export interface Connection {
    connect(callback: (err: any) => void): void;
    query(sql: string, callback: (err: any, results: any) => void): void;
    end(): void;
    destroy(): void;
  }
  
  export interface ConnectionOptions {
    host?: string;
    port?: number;
    user?: string;
    password?: string;
    database?: string;
    charset?: string;
    ssl?: any;
    connectTimeout?: number;
    acquireTimeout?: number;
    timeout?: number;
  }
  
  export function createConnection(config: ConnectionOptions): Connection;
}

// KingbaseES 官方驱动类型声明
declare module 'kb' {
  export class Client {
    constructor(config: any);
    connect(callback: (err: any) => void): void;
    query(sql: string, callback: (err: any, result: any) => void): void;
    end(): void;
  }
}

// PostgreSQL 驱动类型声明
declare module 'pg' {
  export class Client {
    constructor(config: any);
    connect(): Promise<void>;
    query(sql: string): Promise<any>;
    end(): void;
  }
}

// Oracle 驱动类型声明
declare module 'oracledb' {
  export function getConnection(config: any): Promise<any>;
  export const BIND_OUT: any;
  export const STRING: any;
  export const NUMBER: any;
}

// SQL Server 驱动类型声明
declare module 'mssql' {
  export class ConnectionPool {
    constructor(config: any);
    connect(): Promise<void>;
    request(): any;
    close(): Promise<void>;
  }
}

// IBM DB2 驱动类型声明
declare module 'ibm_db' {
  export function open(connectionString: string, callback: (err: any, conn: any) => void): void;
  export function openSync(connectionString: string): any;
}

// 其他数据库驱动的基本类型声明（如果需要）
declare module 'mysql2' {
  export interface ConnectionConfig {
    host?: string;
    port?: number;
    user?: string;
    password?: string;
    database?: string;
    ssl?: boolean | object;
    timeout?: number;
    connectTimeout?: number;
    acquireTimeout?: number;
  }

  export interface Connection {
    connect(callback?: (err: Error | null) => void): void;
    query(sql: string, callback?: (err: Error | null, results?: any, fields?: any) => void): void;
    query(sql: string, values: any[], callback?: (err: Error | null, results?: any, fields?: any) => void): void;
    end(callback?: (err: Error | null) => void): void;
  }

  export function createConnection(config: ConnectionConfig): Connection;
}

declare module 'mssql' {
  export interface ConnectionConfig {
    user?: string;
    password?: string;
    server?: string;
    database?: string;
    port?: number;
    options?: {
      encrypt?: boolean;
      trustServerCertificate?: boolean;
    };
    connectionTimeout?: number;
    requestTimeout?: number;
  }

  export class Request {
    query(command: string): Promise<IResult<any>>;
  }

  export interface IResult<T> {
    recordset: T[];
    recordsets: T[][];
    rowsAffected: number[];
  }
}

declare module 'ibm_db' {
  export interface ConnectionConfig {
    DATABASE?: string;
    HOSTNAME?: string;
    PORT?: number;
    PROTOCOL?: string;
    UID?: string;
    PWD?: string;
    CONNECTTIMEOUT?: number;
  }

  export interface Database {
    open(connectionString: string, callback: (err: Error | null, conn?: any) => void): void;
    query(sql: string, callback: (err: Error | null, data?: any[], more?: boolean) => void): void;
    close(callback?: (err: Error | null) => void): void;
  }

  export function open(connectionString: string, callback: (err: Error | null, conn?: Database) => void): void;
} 