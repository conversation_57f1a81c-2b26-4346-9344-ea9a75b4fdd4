"use client";

import React from 'react';
import styled from 'styled-components';
import { NodeSettings } from './NodeSettings';

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
`;

interface NodeDetailsViewProps {
  node: any;
  parameters: any[];
  savedValues?: Record<string, any>;
  onClose: () => void;
  onSave: (nodeData: any) => void;
  onNodeIdChange?: (oldId: string, newId: string) => void;
  nodeWidth?: number;
  onTest?: (nodeValues: Record<string, any>) => void;
  onTestPreviousNode?: (nodeValues: Record<string, any>, targetNodeId: string) => void;
  onSaveMockData?: (mockTestResult: any) => void;
  testOutput?: string;
  lastTestResult?: any;
  testHistory?: any[];
  previousNodeIds?: string[];
  onPreviousNodeChange?: (nodeId: string) => void;
  selectedPreviousNodeId?: string;
  nodesTestResultsMap?: Record<string, any>;
  getLatestNodesTestResultsMap?: () => Record<string, any>;
  nodesDetailsMap?: Record<string, any>;
  showToast?: (type: 'error' | 'warning', title: string, message: string) => void;
}

export const NodeDetailsView: React.FC<NodeDetailsViewProps> = ({
  node,
  parameters,
  savedValues = {},
  onClose,
  onSave,
  onNodeIdChange,
  nodeWidth,
  onTest,
  onTestPreviousNode,
  onSaveMockData,
  testOutput,
  lastTestResult,
  testHistory,
  previousNodeIds,
  onPreviousNodeChange,
  selectedPreviousNodeId,
  nodesTestResultsMap,
  getLatestNodesTestResultsMap,
  nodesDetailsMap,
  showToast,
}) => {
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <ModalOverlay onClick={handleOverlayClick} className="node-details-view">
      <NodeSettings
        node={node}
        parameters={parameters}
        savedValues={savedValues}
        onClose={onClose}
        onSave={onSave}
        onNodeIdChange={onNodeIdChange}
        nodeWidth={nodeWidth}
        onTest={onTest}
        onTestPreviousNode={onTestPreviousNode}
        onSaveMockData={onSaveMockData}
        testOutput={testOutput}
        lastTestResult={lastTestResult}
        previousNodeIds={previousNodeIds}
        onPreviousNodeChange={onPreviousNodeChange}
        selectedPreviousNodeId={selectedPreviousNodeId}
        nodesTestResultsMap={nodesTestResultsMap}
        getLatestNodesTestResultsMap={getLatestNodesTestResultsMap}
        nodesDetailsMap={nodesDetailsMap}
        showToast={showToast}
      />
    </ModalOverlay>
  );
};