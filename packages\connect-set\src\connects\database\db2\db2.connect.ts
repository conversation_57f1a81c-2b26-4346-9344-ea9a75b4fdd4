import { IDatabaseConnect, ConnectTestResult } from '@repo/common';

/**
 * IBM DB2 数据库连接定义
 */
export const DB2Connect: IDatabaseConnect = {
    overview: {
        id: 'db2',
        name: 'IBM DB2',
        type: 'rd-db' as const,
        provider: 'db2' as const,
        icon: 'db2.svg',
        description: 'IBM DB2关系型数据库连接',
        version: '1.0.0',
    },

    detail: {
        defaultPort: 50000,
        supportedFeatures: [
            'transactions' as const,
            'stored_procedures' as const,
            'views' as const,
            'triggers' as const,
            'full_text_search' as const,
            'json_support' as const
        ],
        fields: [
            {
                displayName: '主机地址',
                name: 'host',
                type: 'string' as const,
                default: 'localhost',
                description: 'DB2服务器的主机地址',
                placeholder: 'localhost 或 IP地址',
                required: true,
                controlType: "input"
            },
            {
                displayName: '端口',
                name: 'port',
                type: 'number' as const,
                default: 50000,
                description: 'DB2服务器端口号',
                typeOptions: {
                    minValue: 1,
                    maxValue: 65535
                },
                required: true,
                controlType: "input"
            },
            {
                displayName: '数据库名',
                name: 'database',
                type: 'string' as const,
                default: '',
                description: '要连接的数据库名称',
                placeholder: 'SAMPLE, TESTDB等',
                required: true,
                controlType: "input"
            },
            {
                displayName: '用户名',
                name: 'username',
                type: 'string' as const,
                default: '',
                placeholder: "请输入数据库用户名",
                description: '数据库用户名',
                required: true,
                controlType: "input"
            },
            {
                displayName: '密码',
                name: 'password',
                type: 'string' as const,
                default: '',
                description: '数据库密码',
                placeholder: "请输入数据库密码",
                typeOptions: {
                    password: true
                },
                isSecure: true,
                controlType: "input"
            },
            {
                displayName: 'Schema',
                name: 'schema',
                type: 'string' as const,
                default: '',
                description: '默认Schema名称（可选）',
                placeholder: '通常与用户名相同',
                controlType: "input"
            },
            {
                displayName: '认证类型',
                name: 'authType',
                type: 'options' as const,
                default: 'server',
                description: 'DB2认证类型',
                options: [
                    { name: '服务器认证', value: 'server' },
                    { name: '客户端认证', value: 'client' },
                    { name: 'Kerberos认证', value: 'kerberos' }
                ],
                controlType: "select"
            },
            {
                displayName: '启用SSL',
                name: 'ssl',
                type: 'boolean' as const,
                default: false,
                description: '是否启用SSL连接',
                controlType: "CheckBox"
            },
            {
                displayName: '连接超时(秒)',
                name: 'connectionTimeout',
                type: 'number' as const,
                default: 30,
                description: '连接超时时间，单位：秒',
                typeOptions: {
                    minValue: 1,
                    maxValue: 300
                },
                controlType: "input"
            },
            {
                displayName: '语句超时(秒)',
                name: 'statementTimeout',
                type: 'number' as const,
                default: 30,
                description: 'SQL语句执行超时时间，单位：秒',
                typeOptions: {
                    minValue: 1,
                    maxValue: 3600
                },
                controlType: "input"
            },
            {
                displayName: '连接池大小',
                name: 'poolSize',
                type: 'number' as const,
                default: 10,
                description: '连接池最大连接数',
                typeOptions: {
                    minValue: 1,
                    maxValue: 100
                },
                controlType: "input"
            }
        ],
        validateConnection: true,
        connectionTimeout: 30000
    },

    /**
     * 测试DB2连接
     */
    async test(config: Record<string, any>): Promise<ConnectTestResult> {
        const startTime = Date.now();
        try {
            // 验证必填字段
            const requiredFields = ['host', 'port', 'database', 'username'];
            for (const field of requiredFields) {
                if (!config[field]) {
                    return {
                        success: false,
                        message: `缺少必填字段: ${field}`
                    };
                }
            }

            // 模拟连接测试（实际应该使用 ibm_db 或其他DB2客户端）
            await new Promise(resolve => setTimeout(resolve, 250));

            const latency = Date.now() - startTime;

            return {
                success: true,
                message: 'DB2连接测试成功',
                latency,
                details: {
                    host: config.host,
                    port: config.port,
                    database: config.database,
                    schema: config.schema || config.username.toUpperCase(),
                    authType: config.authType || 'server',
                    ssl: config.ssl || false,
                    poolSize: config.poolSize || 10,
                    connectionTimeout: config.connectionTimeout || 30,
                    statementTimeout: config.statementTimeout || 30
                }
            };

        } catch (error) {
            return {
                success: false,
                message: `DB2连接失败: ${error instanceof Error ? error.message : String(error)}`,
                latency: Date.now() - startTime
            };
        }
    }
};

export default DB2Connect; 