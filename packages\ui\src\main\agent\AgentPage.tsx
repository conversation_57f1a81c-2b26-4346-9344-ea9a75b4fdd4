import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';

// 导入UI组件样式
import {
  GlassContainer,
  GlassMain,
  GlassHeader,
  GlassDescription,
  GlassTabNav,
  GlassTab,
  GlassDescInfo
} from '../home/<USER>';
import {
  HeaderContainer,
  TitleContainer,
  ButtonGroup,
  WelcomeContainer,
  WelcomeContent,
  IconContainer,
  WelcomeTitle,
  PlaceholderContainer,
  EmptyContainer
}
  from '../shared/styles/welcome';

import { MdGroupAdd } from "react-icons/md";
import { RiHealthBookLine } from "react-icons/ri";
import { CoButton } from '../../components/basic/Buttons';
import {
  CreateButtonContainer,
  DropdownContent,
  DropdownItem,
} from '../dropdown1-1';

import { McpConfigModal } from '../../components/modals/McpConfigModal';
import { AgentConfigModal } from '../../components/modals/AgentConfigModal';
import { AgentList } from './AgentList';
import { McpList } from './McpList';
import { AgentConfig, McpConfig } from '@repo/common';

interface AgentPageProps {
  DocumentIcon: React.ComponentType;
  loading: boolean;
  title: string;
  slogan: string;
  agents?: AgentConfig[];
  mcpConfigs?: McpConfig[];
  onMcpSave?: (data: any) => Promise<boolean>;
  onDeleteMcp?: () => Promise<void>;
  onAgentSave?: (data: any) => Promise<boolean>;
  onFetchLLMConnects?: () => Promise<any[]>;
  onFetchMcpConfigs?: () => Promise<any[]>;
  onFetchAgents?: () => Promise<AgentConfig[]>;
  onDeleteAgent?: (agentId: string) => Promise<boolean>;
  onEditAgent?: (agent: AgentConfig) => void;
  // 连接相关回调函数
  onFetchConnects?: () => Promise<any[]>; // 获取连接定义列表
  onFetchConnectDetails?: (connectId: string) => Promise<any>; // 获取连接详情
  onConnectSave?: (data: any) => Promise<boolean>; // 保存连接
  onConnectTest?: (config: Record<string, any>, message?: string) => Promise<any>; // 测试连接
  // Toast相关
  toastHook?: any; // Toast hook实例
}

export const AgentPage: React.FC<AgentPageProps> = ({
  DocumentIcon,
  title,
  slogan,
  agents = [],
  mcpConfigs = [],
  onMcpSave,
  onDeleteMcp,
  onAgentSave,
  onFetchLLMConnects,
  onFetchMcpConfigs,
  onFetchAgents,
  onDeleteAgent,
  onEditAgent,
  onFetchConnects,
  onFetchConnectDetails,
  onConnectSave,
  onConnectTest,
  toastHook,
}) => {
  const [activeTab, setActiveTab] = useState('agent');
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [isMcpModalOpen, setIsMcpModalOpen] = useState(false);
  const [isAgentModalOpen, setIsAgentModalOpen] = useState(false);
  const [editingAgent, setEditingAgent] = useState<AgentConfig | null>(null);
  const [editingMcp, setEditingMcp] = useState<McpConfig | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => {
    setDropdownOpen(!dropdownOpen);
  };

  const [mcpModalMode, setMcpModalMode] = useState<'quick' | 'json'>('quick');

  const handleCreateMcp = () => {
    setDropdownOpen(false);
    setEditingMcp(null);
    setMcpModalMode('quick');
    setIsMcpModalOpen(true);
  };

  const handleImportMcp = () => {
    setDropdownOpen(false);
    setEditingMcp(null);
    setMcpModalMode('json');
    setIsMcpModalOpen(true);
  };

  const handleMcpModalClose = () => {
    setIsMcpModalOpen(false);
    setEditingMcp(null);
  };

  const handleCreateAgent = () => {
    setDropdownOpen(false);
    setEditingAgent(null);
    setIsAgentModalOpen(true);
  };

  const handleAgentModalClose = () => {
    setIsAgentModalOpen(false);
    setEditingAgent(null);
  };

  const handleAgentClick = (agentId: string) => {
    // 可以实现智能体详情查看逻辑
  };

  const handleDeleteAgent = async (agentId: string): Promise<boolean> => {
    if (onDeleteAgent) {
      try {
        const success = await onDeleteAgent(agentId);
        if (success) {
          // 刷新列表
          if (onFetchAgents) {
            await onFetchAgents();
          }
        }
        return success;
      } catch (error) {
        console.error('删除智能体失败:', error);
        return false;
      }
    }
    return false;
  };

  const handleEditAgentFromList = async (agent: AgentConfig) => {
    try {
      // 获取包含完整数据（包括prompt）的智能体信息
      const response = await fetch(`/api/agents/${agent.id}`);
      const result = await response.json();
      
      if (result.success) {
        // 使用完整的数据设置编辑状态
        setEditingAgent(result.data);
        setIsAgentModalOpen(true);
        if (onEditAgent) {
          onEditAgent(result.data);
        }
      } else {
        // 如果获取失败，使用原有数据
        console.error('获取智能体详情失败:', result.error);
        setEditingAgent(agent);
        setIsAgentModalOpen(true);
        if (onEditAgent) {
          onEditAgent(agent);
        }
      }
    } catch (error) {
      // 如果请求失败，使用原有数据
      console.error('获取智能体详情异常:', error);
      setEditingAgent(agent);
      setIsAgentModalOpen(true);
      if (onEditAgent) {
        onEditAgent(agent);
      }
    }
  };

  const handleDebugAgent = (agent: AgentConfig) => {
    // 这里可以实现调试功能
    alert(`正在调试智能体: ${agent.name}\nID: ${agent.id}`);
  };

  // MCP 相关处理函数
  const handleMcpClick = (mcpId: string) => {
    // 可以实现MCP详情查看逻辑
  };

  const handleDeleteMcp = async (mcpId: string) => {
    console.log('🔍 [AgentPage] 收到删除MCP回调:', mcpId);
    // 调用传入的删除回调函数
    if (onDeleteMcp) {
      console.log('🔍 [AgentPage] 调用onDeleteMcp回调');
      await onDeleteMcp();
      console.log('🔍 [AgentPage] onDeleteMcp回调完成');
    } else {
      console.log('🔍 [AgentPage] onDeleteMcp 不存在');
    }
  };

  const handleEditMcp = (mcp: McpConfig) => {
    setEditingMcp(mcp);
    setMcpModalMode('quick');
    setIsMcpModalOpen(true);
  };

  const handleDebugMcp = (mcp: McpConfig) => {
    alert(`正在调试MCP配置: ${mcp.name}`);
  };

  const handleAgentSaveSuccess = async () => {
    setIsAgentModalOpen(false);
    setEditingAgent(null);
    // 切换到智能体列表
    setActiveTab('agent');
    // 刷新智能体列表
    if (onFetchAgents) {
      await onFetchAgents();
    }
  };

  const handleMcpSave = async (data: any): Promise<boolean> => {
    try {
      let success = false;

      if (editingMcp) {
        // 编辑模式 - 调用更新 API
        const response = await fetch(`/api/mcp-configs`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            id: editingMcp.id,
            ...data
          }),
        });

        const result = await response.json();
        success = result.success;

        if (!success) {
          alert(result.error || '更新MCP配置失败');
        }
      } else {
        // 创建模式 - 使用原有的保存逻辑
        if (onMcpSave) {
          success = await onMcpSave(data);
        }
      }

      if (success) {
        setIsMcpModalOpen(false);
        setEditingMcp(null);
        // 切换到MCP列表
        setActiveTab('mcp');
        // 刷新MCP配置列表
        if (onFetchMcpConfigs) {
          await onFetchMcpConfigs();
        }
      }
      return success;
    } catch (error) {
      console.error('保存MCP配置失败:', error);
      alert('保存失败，请重试');
      return false;
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <GlassContainer>
      {/* <LiquidBackground /> */}
      <GlassMain>
        <GlassHeader>
          <GlassDescription>
            <HeaderContainer>
              <TitleContainer>
                <h3>{title}</h3>
              </TitleContainer>
              <CreateButtonContainer ref={dropdownRef}>
                <ButtonGroup>
                  <CoButton variant='liquid' radian="left" onClick={handleCreateAgent}>
                    <MdGroupAdd />
                    <span> 创建智能体</span>
                  </CoButton>
                  <CoButton variant='liquid' radian="right"
                    onClick={toggleDropdown}
                  >
                    MCP +
                  </CoButton>
                  <DropdownContent $isOpen={dropdownOpen}>
                    <DropdownItem onClick={handleCreateMcp}>快速创建 <RiHealthBookLine />
                    </DropdownItem>
                    <DropdownItem onClick={handleImportMcp}>JSON导入 <RiHealthBookLine />
                    </DropdownItem>
                  </DropdownContent>
                </ButtonGroup>
              </CreateButtonContainer>
            </HeaderContainer>
            <GlassDescInfo>
              {slogan}
            </GlassDescInfo>
          </GlassDescription>

          <GlassTabNav>
            <GlassTab $active={activeTab === 'agent'} onClick={() => setActiveTab('agent')}>
              智能体
            </GlassTab>
            <GlassTab $active={activeTab === 'mcp'} onClick={() => setActiveTab('mcp')}>
              MCP
            </GlassTab>
          </GlassTabNav>
        </GlassHeader>
        {(() => {
          if (activeTab === 'agent') {
            if (agents.length === 0) {
              return (
                <WelcomeContainer>
                  <WelcomeContent>
                    <IconContainer>
                      <DocumentIcon />
                    </IconContainer>
                    <WelcomeTitle>
                      还没有任何智能体
                      <p>点击上方的"创建智能体"按钮开始配置你的第一个智能体</p>
                    </WelcomeTitle>
                    <CoButton onClick={handleCreateAgent}>
                      快速创建智能体
                    </CoButton>
                    <PlaceholderContainer />
                  </WelcomeContent>
                </WelcomeContainer>
              );
            } else {
              return (
                <div style={{ flex: 1, display: 'flex', flexDirection: 'column', minHeight: 0, marginTop: '4px' }}>
                  <AgentList
                    agents={agents}
                    activeTab={activeTab}
                    onAgentClick={handleAgentClick}
                    onDeleteAgent={handleDeleteAgent}
                    onEditAgent={handleEditAgentFromList}
                    onDebugAgent={handleDebugAgent}
                  />
                </div>
              );
            }
          } else if (activeTab === 'mcp') {
            // MCP标签页内容
            if (mcpConfigs.length === 0) {
              return (
                <WelcomeContainer>
                  <WelcomeContent>
                    <IconContainer>
                      <DocumentIcon />
                    </IconContainer>
                    <WelcomeTitle>
                      还没有任何MCP配置
                      <p>点击上方的"MCP +"按钮开始配置你的第一个MCP工具</p>
                    </WelcomeTitle>
                    <CoButton onClick={handleCreateMcp}>
                      快速创建MCP
                    </CoButton>
                    <PlaceholderContainer />
                  </WelcomeContent>
                </WelcomeContainer>
              );
            } else {
              return (
                <div style={{ flex: 1, display: 'flex', flexDirection: 'column', minHeight: 0, marginTop: '4px' }}>
                  <McpList
                    mcpConfigs={mcpConfigs}
                    activeTab={activeTab}
                    onMcpClick={handleMcpClick}
                    onDeleteMcp={handleDeleteMcp}
                    onEditMcp={handleEditMcp}
                    onDebugMcp={handleDebugMcp}
                    toastHook={toastHook}
                  />
                </div>
              );
            }
          }
          return <EmptyContainer />;
        })()}
      </GlassMain>

      {/* 智能体配置弹窗 */}
      <AgentConfigModal
        isOpen={isAgentModalOpen}
        onClose={handleAgentModalClose}
        onSaveSuccess={handleAgentSaveSuccess}
        onFetchLLMConnects={onFetchLLMConnects}
        onFetchMcpConfigs={onFetchMcpConfigs}
        editMode={!!editingAgent}
        editData={editingAgent}
        onFetchConnects={onFetchConnects}
        onFetchConnectDetails={onFetchConnectDetails}
        onSaveConnect={onConnectSave}
        onTestConnect={onConnectTest}
      />

      {/* MCP配置弹窗 */}
      <McpConfigModal
        isOpen={isMcpModalOpen}
        onClose={handleMcpModalClose}
        onSave={handleMcpSave}
        editMode={!!editingMcp}
        editData={editingMcp}
        mode={mcpModalMode}
      />
    </GlassContainer>
  )
};