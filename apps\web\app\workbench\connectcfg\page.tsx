 "use client";

// import React, { useState, useEffect } from 'react';
// import { AgentPage } from '@repo/ui/main/connection';
// import { FaCoffee } from "react-icons/fa";


// export default function AgentPageContainer() {
//   const [loading, setLoading] = useState(true);

//   return (
//     <ConnectPage
//       title='连接配置'
//       slogan='为您统一管理所有连接资源，包括数据库、API接口、大语言模型、Embedding服务、邮箱等，并提供连接测试、参数配置、资源删除等管理操作.'
//       loading={loading}
//       DocumentIcon={FaCoffee}
//     />
//   );
// }


import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import styled from 'styled-components';
import {
  Header,
  Title,
  SubTitle,
  ContentArea
} from '@repo/ui';

import { ConnectService, ConnectBasicInfo, ConnectDetailInfo } from '@/services/connectService';
import { ConnectDetailsView } from '@repo/ui';

const Container = styled.div`
  padding: 24px;
`;

const Section = styled.div`
  margin-bottom: 32px;
`;

const SectionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const SectionTitle = styled.h2`
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
`;

const AddButton = styled.button`
  background: #33C2EE;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #2AA8CC;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 40px;
  color: #666;
`;

const LoadingState = styled.div`
  text-align: center;
  padding: 40px;
  color: #999;
`;

const ErrorState = styled.div`
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  padding: 16px;
  color: #ff4d4f;
  margin-bottom: 16px;
`;

const FactoryGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
`;

const ConnectGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
`;

const SearchInput = styled.input`
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  width: 300px;

  &:focus {
    outline: none;
    border-color: #33C2EE;
  }
`;

const FilterTabs = styled.div`
  display: flex;
  gap: 2px;
  margin-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
`;

const FilterTab = styled.button<{ $active: boolean }>`
  background: ${props => props.$active ? '#33C2EE' : 'transparent'};
  color: ${props => props.$active ? 'white' : '#666'};
  border: none;
  padding: 8px 16px;
  border-radius: 4px 4px 0 0;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.$active ? '#2AA8CC' : '#f5f5f5'};
  }
`;

// 连接卡片组件（改为网格布局）
const ConnectCard = styled.div`
background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
  height: 100%;

  &:hover {
    border-color: #33C2EE;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
`;

const ConnectHeader = styled.div`
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  min-height: 40px;
`;

const ConnectIcon = styled.div`
   font-size: 42px;
  margin-bottom: 16px;
`;

const ConnectName = styled.h3`
 margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
`;

const ConnectDescription = styled.p`
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  flex: 1;
`;

const ConnectMeta = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
`;

const ConnectTag = styled.span`
  background: #f0f0f0;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  color: #666;
  font-weight: 500;
`;

const ConnectActions = styled.div`
  display: flex;
  gap: 8px;
  margin-top: auto;
`;

const ActionButton = styled.button`
  flex: 1;
  background: transparent;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #33C2EE;
    color: #33C2EE;
    background: rgba(51, 194, 238, 0.05);
  }

  &:first-child {
    border-color: #33C2EE;
    color: #33C2EE;
  }
`;

type FilterType = 'all' | 'database' | 'http' | 'llm';

export default function ConnectConfigPage() {
  const [connects, setConnects] = useState<ConnectBasicInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<FilterType>('all');
  
  // 连接配置弹窗相关状态
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [selectedConnect, setSelectedConnect] = useState<ConnectDetailInfo | null>(null);
  const [connectLoading, setConnectLoading] = useState(false);

  // 编辑模式相关状态
  const [editMode, setEditMode] = useState(false);
  const [editData, setEditData] = useState<{
    id: string;
    connectId: string;
    name: string;
    config: Record<string, any>;
  } | undefined>(undefined);

  // 保留原有的模态窗口状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);

  const router = useRouter();

  /**
   * 处理URL参数，检查是否为编辑模式
   */
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const mode = urlParams.get('mode');
    
    if (mode === 'edit') {
      const id = urlParams.get('id');
      const connectId = urlParams.get('connectId');
      const name = urlParams.get('name');
      const configStr = urlParams.get('config');
      
      if (id && connectId && name && configStr) {
        try {
          const config = JSON.parse(configStr);
          setEditData({ id, connectId, name, config });
          setEditMode(true);
          
          // 自动加载对应的连接详情并打开编辑对话框
          const loadEditConnect = async () => {
            try {
              setConnectLoading(true);
              const result = await ConnectService.getConnectDetail(connectId);
              setSelectedConnect(result.data);
              setConfigModalVisible(true);
            } catch (err) {
              console.error('Error loading connect for edit:', err);
              setError('加载编辑连接失败');
            } finally {
              setConnectLoading(false);
            }
          };
          
          loadEditConnect();
        } catch (err) {
          console.error('Error parsing edit config:', err);
          setError('解析编辑参数失败');
        }
      }
    }
  }, []);

  /**
   * 获取连接列表
   */
  const fetchConnects = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await ConnectService.getConnectList();

      if (result.success) {
        setConnects(result.data || []);
      } else {
        setError(result.error || '获取连接列表失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '网络错误，请重试');
      console.error('Error fetching connects:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchConnects();
  }, []);

  /**
   * 处理连接配置
   */
  const handleTestConnect = async (connect: ConnectBasicInfo) => {
    try {
      setConnectLoading(true);
      const result = await ConnectService.getConnectDetail(connect.id);
      setSelectedConnect(result.data);
      setConfigModalVisible(true);
    } catch (err) {
      console.error('Error fetching connect detail:', err);
      setError(err instanceof Error ? err.message : '获取连接详情失败');
    } finally {
      setConnectLoading(false);
    }
  };

  /**
   * 处理连接测试
   */
  const handleConnectTest = async (config: Record<string, any>, message?: string) => {
    if (!selectedConnect) return;
    
    try {
      const result = await ConnectService.testConnection(selectedConnect.id, config, message);
      return result;
    } catch (error) {
      throw error;
    }
  };

  /**
   * 处理流式连接测试
   */
  const handleConnectStreamTest = async (config: Record<string, any>, message: string, onChunk: (chunk: string) => void) => {
    if (!selectedConnect) return;
    
    try {
      // 检查连接是否支持流式输出
      if (selectedConnect.type === 'llm' && config.stream) {
        console.log('🚀 启动流式测试:', { connectId: selectedConnect.id, stream: config.stream });
        
        // 直接导入特定的连接器，而不是加载整个注册表
        let connect;
        
        switch (selectedConnect.id) {
          case 'siliconflow':
            const { SiliconFlowConnect } = await import('@repo/connect-set/dist/connects/siliconflow');
            connect = SiliconFlowConnect;
            break;
          case 'openai':
            const { OpenAIConnect } = await import('@repo/connect-set/dist/connects/openai');
            connect = OpenAIConnect;
            break;
          default:
            throw new Error(`不支持的连接器: ${selectedConnect.id}`);
        }
        
        if (!connect) {
          throw new Error('连接器未找到');
        }
        
        // 检查是否支持流式对话
        if ('streamChat' in connect && typeof connect.streamChat === 'function') {
          console.log(`✅ 使用 ${selectedConnect.id} 连接器进行流式对话`);
          // 直接调用连接器的 streamChat 方法
          const result = await connect.streamChat(config, message, onChunk);
          return result;
        } else {
          throw new Error('该连接器不支持流式对话');
        }
      } else {
        // 非流式，使用普通测试
        return await ConnectService.testConnection(selectedConnect.id, config, message);
      }
    } catch (error) {
      console.error('流式测试失败:', error);
      throw error;
    }
  };

  /**
   * 处理连接配置保存
   */
  const handleConnectSave = async (connectData: any) => {
    console.log('🏠 ConnectConfigPage.handleConnectSave 开始');
    console.log('📥 接收到的连接数据:', connectData);
    console.log('🔧 当前模式:', { editMode, hasEditData: !!editData });
    
    try {
      if (editMode && editData) {
        console.log('✏️ 编辑模式：更新现有配置');
        console.log('🆔 编辑数据 ID:', editData.id);
        
        const updatePayload = {
          name: connectData.name,
          configinfo: JSON.stringify(connectData.config), // 将由后端加密
        };
        console.log('📤 更新载荷:', updatePayload);
        
        // 编辑模式：更新现有配置
        const updateResponse = await fetch(`/api/connect-configs/${editData.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updatePayload),
        });
        
        console.log('📡 更新响应状态:', updateResponse.status);
        const updateResult = await updateResponse.json();
        console.log('📥 更新响应数据:', updateResult);
        
        if (updateResult.success) {
          console.log('✅ 连接配置更新成功:', updateResult.data);
          alert('连接配置更新成功！');
          // 返回到连接列表页面
          router.push('/workbench/connections');
        } else {
          console.error('❌ 更新失败:', updateResult.error);
          throw new Error(updateResult.error || '更新连接配置失败');
        }
      } else {
        console.log('➕ 新建模式：保存新配置');
        
        const savePayload = {
          connectId: connectData.connectId,
          name: connectData.name,
          mtype: connectData.mtype,
          config: connectData.config,
          creator: 'current-user' // 这里应该从用户上下文获取
        };
        console.log('📤 保存载荷:', savePayload);
        
        console.log('🔄 调用 ConnectService.saveConnectConfig...');
        // 新建模式：保存新配置
        const result = await ConnectService.saveConnectConfig(savePayload);
        console.log('📥 ConnectService 返回结果:', result);
        
        if (result.success) {
          console.log('✅ 连接配置保存成功:', result.data);
          alert('连接配置保存成功！');
          // 可以选择留在当前页面或跳转
        } else {
          console.error('❌ 保存失败:', result.error);
          throw new Error(result.error || '保存连接配置失败');
        }
      }
      console.log('🎉 handleConnectSave 完成');
    } catch (error) {
      console.error('❌ ConnectConfigPage 错误:', error);
      console.error('🔍 错误详情:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error; // 重新抛出错误，让ConnectSettings组件处理
    }
  };

  /**
   * 关闭连接配置弹窗
   */
  const handleCloseConfigModal = () => {
    setConfigModalVisible(false);
    setSelectedConnect(null);
    
    // 如果是编辑模式，关闭时清除编辑状态并返回连接列表
    if (editMode) {
      setEditMode(false);
      setEditData(undefined);
      router.push('/workbench/connections');
    }
  };

  const handleDeleteLLM = async (id: string) => {
    // 暂时保留原有逻辑
    console.log('Delete connect:', id);
  };

  const handleToggleLLM = async (id: string, isActive: boolean) => {
    // 暂时保留原有逻辑
    console.log('Toggle connect:', id, isActive);
  };

  /**
   * 过滤和搜索连接
   */
  const filteredConnects = connects.filter(connect => {
    // 类型过滤
    if (filterType !== 'all' && connect.type !== filterType) return false;

    // 搜索过滤
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        connect.name.toLowerCase().includes(query) ||
        connect.provider.toLowerCase().includes(query) ||
        connect.description.toLowerCase().includes(query)
      );
    }

    return true;
  });

  const TitleDesc = () => (
    <div style={{color:'#8e8e8e',display:'flex',flexDirection:'column',gap: '8px',height:'59px'}}>
      <h3>{editMode ? '编辑连接配置' : '连接配置'}</h3>
      <p>为您统一管理所有连接资源，包括数据库、API接口、大语言模型、Embedding服务、邮箱等，并提供连接测试、参数配置、资源删除等管理操作.</p>
    </div>
  );

  return (
    <>
      <Header>
        <TitleDesc />
      </Header>
      <ContentArea>
        <Container>
          {error && (
            <ErrorState>
              {error}
            </ErrorState>
          )}

          {/* 如果不是编辑模式，显示连接列表 */}
          {!editMode && (
            <>
              {/* 已配置的连接 */}
              <Section>
                <SectionHeader>
                  <SectionTitle>已配置的连接</SectionTitle>
                  <SearchInput
                    type="text"
                    placeholder="搜索连接配置..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </SectionHeader>

                <FilterTabs>
                  <FilterTab
                    $active={filterType === 'all'}
                    onClick={() => setFilterType('all')}
                  >
                    全部 ({connects.length})
                  </FilterTab>
                  {/* <FilterTab
                    $active={filterType === 'database'}
                    onClick={() => setFilterType('database')}
                  >
                    数据库 ({connects.filter(c => c.type === 'database').length})
                  </FilterTab> */}
                  <FilterTab
                    $active={filterType === 'http'}
                    onClick={() => setFilterType('http')}
                  >
                    HTTP ({connects.filter(c => c.type === 'http').length})
                  </FilterTab>
                  <FilterTab
                    $active={filterType === 'llm'}
                    onClick={() => setFilterType('llm')}
                  >
                    LLM ({connects.filter(c => c.type === 'llm').length})
                  </FilterTab>
                </FilterTabs>

                {loading ? (
                  <LoadingState>正在加载连接...</LoadingState>
                ) : filteredConnects.length === 0 ? (
                  <EmptyState>
                    {searchQuery ? '没有找到匹配的连接' : '还没有配置任何连接'}
                  </EmptyState>
                ) : (
                  <ConnectGrid>
                    {filteredConnects.map(connect => (
                      <ConnectCard key={connect.id} onClick={() => handleTestConnect(connect)}>
                        <ConnectHeader>
                          <ConnectIcon>{connect.icon}</ConnectIcon>
                          <ConnectName>{connect.name}</ConnectName>
                        </ConnectHeader>
                        <ConnectDescription>{connect.description}</ConnectDescription>
                        <ConnectMeta>
                          <ConnectTag>{connect.type.toUpperCase()}</ConnectTag>
                          <ConnectTag>{connect.provider}</ConnectTag>
                          {/* <span>{connect.category}</span> */}
                        </ConnectMeta>
                        <ConnectActions>
                          <ActionButton onClick={(e) => {
                            e.stopPropagation();
                            handleTestConnect(connect);
                          }}>
                             连接配置
                          </ActionButton>
                        </ConnectActions>
                      </ConnectCard>
                    ))}
                  </ConnectGrid>
                )}
              </Section>

              {/* 可添加的模型（保留以兼容现有UI） */}
              <Section>
                <SectionHeader>
                  <SectionTitle>添加新连接</SectionTitle>
                </SectionHeader>
              </Section>
            </>
          )}
        </Container>
      </ContentArea>

      {/* 连接配置模态窗口 */}
      {/* {configModalVisible && selectedConnect && (
        <ConnectDetailsView
          // connect={selectedConnect}
          onClose={handleCloseConfigModal}
          onSave={handleConnectSave}
          onTest={handleConnectTest}
          onStreamTest={handleConnectStreamTest}
          editMode={editMode}
          editData={editData}
        />
      )} */}
    </>
  );
} 