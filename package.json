{"name": "cofly", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "dev:docs": "turbo run dev --filter=docs", "dev:web": "turbo run dev --filter=web", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md,json}\"", "db:generate": "turbo run db:generate", "db:push": "turbo run db:push", "db:studio": "pnpm --filter @repo/db db:studio", "prepare": "husky install", "start": "turbo run start", "start:web": "pnpm --filter web start", "clean": "turbo run clean && powershell -Command \"Remove-Item -Path 'node_modules' -Recurse -Force -ErrorAction SilentlyContinue\"", "start:docs": "pnpm --filter docs start"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.20", "@types/react-syntax-highlighter": "^15.5.13", "husky": "^9.1.7", "lint-staged": "^15.5.2", "prettier": "^3.5.3", "turbo": "^2.5.3", "typescript": "5.8.2"}, "packageManager": "pnpm@10.13.1", "engines": {"node": ">=18"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "dependencies": {"@codemirror/lang-javascript": "^6.2.4", "@codemirror/lang-json": "^6.0.1", "@codemirror/legacy-modes": "^6.5.1", "@dagrejs/dagre": "^1.1.4", "@shikijs/markdown-it": "^3.7.0", "@tanstack/react-virtual": "^3.13.12", "@uiw/codemirror-themes-all": "^4.24.0", "@uiw/react-codemirror": "^4.23.12", "dayjs": "^1.11.13", "iconv-lite": "^0.6.3", "katex": "^0.16.22", "lodash": "^4.17.21", "lru-cache": "^11.1.0", "lucide-react": "^0.400.0", "pnpm": "^10.11.0", "react-ace": "^14.0.1", "react-icons": "^5.5.0", "react-markdown": "^9.1.0", "react-syntax-highlighter": "^15.6.1", "reactflow": "^11.11.4", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "shiki": "^3.7.0"}}