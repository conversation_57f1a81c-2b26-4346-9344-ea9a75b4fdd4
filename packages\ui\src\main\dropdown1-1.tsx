"use client";

import styled from 'styled-components';

export const CreateButtonContainer = styled.div`
  position: relative;
  display: flex;
  z-index: 10000;
`;

export const DropdownContent = styled.div<{ $isOpen: boolean }>`
  display: ${props => props.$isOpen ? 'block' : 'none'};
  position: absolute;
  top: 100%;
  left: 80px;
  background: ${({ theme }) => theme.mode === 'dark'
    ? `
      radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.15) 0%, transparent 25%),
      radial-gradient(circle at 80% 70%, rgba(168, 85, 247, 0.12) 0%, transparent 30%),
      radial-gradient(circle at 40% 80%, rgba(14, 165, 233, 0.1) 0%, transparent 35%),
      radial-gradient(circle at 90% 20%, rgba(139, 92, 246, 0.08) 0%, transparent 25%)
    `
    : `
      radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.08) 0%, transparent 25%),
      radial-gradient(circle at 80% 70%, rgba(168, 85, 247, 0.06) 0%, transparent 30%),
      radial-gradient(circle at 40% 80%, rgba(14, 165, 233, 0.05) 0%, transparent 35%),
      radial-gradient(circle at 90% 20%, rgba(139, 92, 246, 0.04) 0%, transparent 25%)
    `
  };
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  /*border: 1px solid ${({ theme }) => theme.colors.primary};*/
  z-index: 9999;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 4px;
`;

export const DropdownItem = styled.div`
  padding: 12px 16px;
  text-decoration: none;
  display: block;
  font-size: 13px;
  color: ${({ theme }) => theme.colors.textPrimary};
  cursor: pointer;

  &:hover {
    background-color: ${({ theme }) => theme.colors.tertiary};
  }
  svg {
    margin-left: 8px;
    margin-bottom: -2px;
  }
`;