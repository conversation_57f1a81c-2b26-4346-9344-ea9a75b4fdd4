/**
 * 工作流测试服务
 * 用于调用工作流测试接口
 */

export interface WorkflowTestResponse {
  ids: string[];
  [key: string]: any;
}

/**
 * 调用工作流测试接口
 * @param workflowId 工作流ID
 * @returns Promise<WorkflowTestResponse>
 */
export const testWorkflow = async (workflowId: string): Promise<WorkflowTestResponse> => {
  try {
    console.log('🚀 Starting workflow test for ID:', workflowId);
    
    const response = await fetch(`/api/workflow?id=${encodeURIComponent(workflowId)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ Workflow test completed successfully:', result);
    
    return result;
  } catch (error) {
    console.error('❌ Workflow test failed:', error);
    throw error;
  }
}; 