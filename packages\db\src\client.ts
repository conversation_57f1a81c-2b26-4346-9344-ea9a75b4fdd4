import { PrismaClient } from "@prisma/client";
import { join } from "path";

// 获取数据库文件的绝对路径
const getDatabaseUrl = () => {
  if (process.env.DATABASE_URL) {
    return process.env.DATABASE_URL;
  }
  
  // 从当前包目录构建数据库文件路径
  const dbPath = join(__dirname, "..", "prisma", "database.db");
  return `file:${dbPath}`;
};

// Create a single instance of Prisma Client
const prisma = new PrismaClient({
  log: process.env.NODE_ENV === "development" ? ["query", "error", "warn"] : ["error"],
  datasources: {
    db: {
      url: getDatabaseUrl()
    }
  }
});

prisma.$extends({
    name: 'getTableName',
    model: {
        // 为所有模型添加 getTableName 方法
        $allModels: {
            getTableName<T>(this: T) {
                // 获取当前模型名（如 "User"）
                const modelName = (this as any).$name as string;
                // 访问 DMMF 元数据
                const dmmf = (prisma as any)._dmmf;
                // 查找模型定义
                const model = dmmf.modelMap[modelName];
                // 返回映射的表名（若未定义 @@map，则返回模型名的小写形式）
                return model?.dbName || modelName.toLowerCase();
            },
        },
    },
});

export { prisma };
