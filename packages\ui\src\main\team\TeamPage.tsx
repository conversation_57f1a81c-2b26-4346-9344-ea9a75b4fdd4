import React, { useState, useRef, useEffect, useCallback } from 'react';
import { MdManageAccounts } from "react-icons/md";
import { PageHeader } from '../../components/basic/PageHeader';
import ChatDisplay from '../chat/ChatDisplay';
import {
  TeamContainer,
  TeamContentContainer,
  ChatSidebar,
  ResizeHandle,
  WidthIndicator,
  TeamMainContent,
  TopSection,
  TeamGrid,
  TeamMemberCard,
  MemberAvatar,
  MemberName,
  ManageIcon,
  AddMemberCard,
  AddIcon,
  SaveButton,
  TeamTabsContainer,
  TeamTab
} from './styles';

interface TeamMember {
  id: string;
  name: string;
  role: string;
  avatar: string;
  status: 'online' | 'busy' | 'away' | 'offline';
  lastSeen: string;
}



interface TeamPageProps {
  title: string;
  slogan: string;
  DocumentIcon: React.ComponentType;
  loading: boolean;
  teamMembers?: TeamMember[];
  agentId?: string | null;
  userId?: string;
  agentName?: string;
  agentAvatar?: string;
}

export const TeamPage: React.FC<TeamPageProps> = ({
  title,
  slogan,
  DocumentIcon,
  loading,
  teamMembers = [],
  agentId = null,
  userId = "admin",
  agentName = "团队智能体",
  agentAvatar = "👥"
}) => {
  const [sidebarWidth, setSidebarWidth] = useState(320);
  const [isResizing, setIsResizing] = useState(false);
  const [activeTab, setActiveTab] = useState('collaboration');
  const [dragStartX, setDragStartX] = useState(0);
  const [dragStartWidth, setDragStartWidth] = useState(0);
  const [selectedMemberId, setSelectedMemberId] = useState<string | null>(null);

  // 拖拽调整宽度功能
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
    setDragStartX(e.clientX);
    setDragStartWidth(sidebarWidth);
  }, [sidebarWidth]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing) return;
    
    const deltaX = e.clientX - dragStartX;
    const newWidth = dragStartWidth + deltaX;
    
    if (newWidth >= 280 && newWidth <= 600) {
      setSidebarWidth(newWidth);
    }
  }, [isResizing, dragStartX, dragStartWidth]);

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
    setDragStartX(0);
    setDragStartWidth(0);
  }, []);

  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  // Tab 切换处理函数
  const handleCollaborationTabClick = () => {
    setActiveTab('collaboration');
  };

  const handleRecordsTabClick = () => {
    setActiveTab('records');
  };

  // 保存处理函数
  const handleSave = () => {
    console.log('保存团队配置');
    // 这里可以添加保存逻辑
  };

  // 处理团队成员选中
  const handleMemberSelect = (memberId: string) => {
    setSelectedMemberId(selectedMemberId === memberId ? null : memberId);
  };

  if (loading) {
    return (
      <TeamContainer>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          width: '100%', 
          color: 'white' 
        }}>
          加载中...
        </div>
      </TeamContainer>
    );
  }

  return (
    <TeamContainer>
      {/* 页面头部 */}
      <PageHeader 
        title={title} 
        subtitle={slogan}
        centerContent={
          <TeamTabsContainer>
            <TeamTab $active={activeTab === 'collaboration'} onClick={handleCollaborationTabClick}>
              我的团队
            </TeamTab>
            <TeamTab $active={activeTab === 'contract'} onClick={() => setActiveTab('contract')}>
              合同检查团队
            </TeamTab>
            <TeamTab $active={activeTab === 'daily'} onClick={() => setActiveTab('daily')}>
              日报审批团队
            </TeamTab>
            <TeamTab $active={activeTab === 'work'} onClick={() => setActiveTab('work')}>
              日常报工团队
            </TeamTab>
          </TeamTabsContainer>
        }
        actions={
          <SaveButton onClick={handleSave}>
            保存
          </SaveButton>
        }
      />
      
      <TeamContentContainer>
        {/* 左侧聊天区域 - 使用Chart组件 */}
        <ChatSidebar $width={sidebarWidth} $isResizing={isResizing}>        
          <ChatDisplay
            agentId={agentId}
            userId={userId}
            agentName={agentName}
            agentAvatar={agentAvatar}
            showHistory={false}
          />
          
          {/* 拖拽调整器 */}
          <ResizeHandle onMouseDown={handleMouseDown} />
          
          {/* 宽度指示器 */}
          <WidthIndicator 
            $isVisible={isResizing} 
            $width={Math.round(sidebarWidth)} 
          />
        </ChatSidebar>

        {/* 主内容区域 */}
        <TeamMainContent>
          <TopSection>
            {/* 团队成员网格 */}
            <TeamGrid>
              {teamMembers.map((member, index) => {
                const isFirst = index === 0;
                const isSelected = selectedMemberId === member.id;
                // 如果有选中的成员，显示在选中的成员上；如果没有选中的成员，显示在第一个成员上
                const showManageIcon = selectedMemberId ? isSelected : isFirst;
                
                return (
                  <TeamMemberCard 
                    key={member.id}
                    $isSelected={isSelected}
                    $isFirst={isFirst}
                    onClick={() => handleMemberSelect(member.id)}
                  >
                    <MemberAvatar>
                      {member.avatar}
                    </MemberAvatar>
                    <MemberName>{member.name}</MemberName>
                    {showManageIcon && (
                      <ManageIcon title="团队负责人">
                        <MdManageAccounts />
                      </ManageIcon>
                    )}
                  </TeamMemberCard>
                );
              })}
              {/* 添加成员按钮 */}
              <AddMemberCard onClick={() => console.log('添加成员')}>
                <AddIcon>+</AddIcon>
              </AddMemberCard>
            </TeamGrid>
          </TopSection>

          {/* 可以在这里添加更多内容，比如项目进度、任务列表等 */}
          <div style={{ 
            flex: 1, 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            opacity: 0.5
          }}>
            <div style={{ textAlign: 'center', color: 'white' }}>
              <div style={{ fontSize: '80px', marginBottom: '30px' }}>
                <DocumentIcon />
              </div>
              <p style={{ 
                marginTop: '0px', 
                fontSize: '16px',
                fontWeight: '600',
                letterSpacing: '1px'
              }}>
                开始创建您的虚拟智能团队...
              </p>
            </div>
          </div>
        </TeamMainContent>
      </TeamContentContainer>
    </TeamContainer>
  );
};