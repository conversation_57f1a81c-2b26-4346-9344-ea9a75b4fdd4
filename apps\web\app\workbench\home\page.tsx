"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../../src/hooks/useAuth';
import { useWorkflow } from '@/contexts/WorkflowContext';

import { HomePage, WorkflowConfig } from '@repo/ui/main/home';
import { FaCoffee } from "react-icons/fa";


export default function HomePageContainer() {
  const router = useRouter();
  const { user, logout } = useAuth();
  const { createNewWorkflow } = useWorkflow();
  const [workflows, setWorkflows] = useState<WorkflowConfig[]>([]);
  const [loading, setLoading] = useState(true);

  /**
   * 从后端API获取工作流列表数据
   */
  const fetchWorkflows = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/workflow-config?user=default_user');
      if (response.ok) {
        const result = await response.json();
        setWorkflows(result.data || []);
      } else {
        console.error('Failed to fetch workflows');
        setWorkflows([]);
      }
    } catch (error) {
      console.error('Error fetching workflows:', error);
      setWorkflows([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWorkflows();
  }, []);

  /**
   * 处理创建新工作流
   */
  const handleCreateWorkflow = () => {
    const workflowId = createNewWorkflow();
    router.push(`/workbench/flow?workflowID=${workflowId}`);
  };

  /**
   * 处理点击工作流卡片，跳转到工作流编辑页面
   * @param workflowId 工作流ID
   */
  const handleWorkflowClick = (workflowId: string) => {
    router.push(`/workbench/flow?workflowID=${workflowId}`);
  };

  /**
   * 处理切换工作流激活状态
   * @param workflowId 工作流ID
   * @param currentStatus 当前状态
   */
  const handleToggleWorkflow = async (workflowId: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/workflow-config/${workflowId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'toggle'
        }),
      });
      if (response.ok) {
        // 刷新工作流列表
        fetchWorkflows();
      }
    } catch (error) {
      console.error('Error toggling workflow status:', error);
    }
  };

  /**
   * 处理导入工作流（暂未实现）
   */
  // const handleImportWorkflow = () => {
  //   console.log('导入业务流程');
  // };
  const slogn=` Cofly将「Coffee」的闲适与「flow」的丝滑注入自动化基因——让自动化成为您最惬意的数字咖啡师！从此，繁琐复杂、灵嗅嗅觉.`;
  return (
    <HomePage
      title='欢迎使用现代化流程编排工具'
      slogan={slogn}
      user={user || undefined}
      workflows={workflows}
      loading={loading}
      onWorkflowClick={handleWorkflowClick}
      onToggleWorkflow={handleToggleWorkflow}
      onCreateWorkflow={handleCreateWorkflow}
      //onImportWorkflow={handleImportWorkflow}
      onLogout={logout}
      DocumentIcon={FaCoffee}
    />
  );
}