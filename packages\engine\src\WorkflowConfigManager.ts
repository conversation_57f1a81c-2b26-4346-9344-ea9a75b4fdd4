import { WorkflowLoadAdapter } from "@/WorkflowInterfaces";
import { Workflow } from "@/workflow";

class WorkflowConfigManager {
    #workflowLoader: WorkflowLoadAdapter | undefined;

    setAdapters(param: { workflowLoader?: WorkflowLoadAdapter }) {
        this.#workflowLoader = param.workflowLoader;
    }

    async list(limit?: number | undefined) : Promise<Workflow[]> {
        if(this.#workflowLoader) {
            return await this.#workflowLoader.list(limit);
        }

        return [];
    }

    async get(id: string) : Promise<Workflow | undefined | null> {
        if(this.#workflowLoader) {
            return await this.#workflowLoader.get(id);
        }

        return undefined;
    }
}

export const workflowConfigManager = new WorkflowConfigManager();