import { NextRequest, NextResponse } from "next/server";
import { agent<PERSON>anager, mcpManager, AiAgent, ChatModel, ModelSeries } from "@repo/engine";
import { prisma } from "@repo/db";

export async function POST(req: NextRequest) {
    try {
        console.log('🔄 开始重新加载agents...');

        // 清空现有的agents
        const oldAgentCount = agentManager.agents.length;
        console.log(`📊 当前agents数量: ${oldAgentCount}`);

        // 重新加载agents
        const dbAgents = await prisma.aiAgent.findMany() ?? [];
        console.log(`📋 从数据库加载到${dbAgents.length}个agents`);

        // 加载每个agent
        let loadedCount = 0;
        const errors: string[] = [];

        for (const dbAgent of dbAgents) {
            try {
                const agent = await loadAgent(dbAgent);
                agentManager.add(agent);
                loadedCount++;
                console.log(`✅ 成功加载agent: ${agent.name}`);
            } catch (error) {
                const errorMsg = `❌ 加载agent ${dbAgent.name} 失败: ${error instanceof Error ? error.message : 'Unknown error'}`;
                console.error(errorMsg);
                errors.push(errorMsg);
            }
        }

        console.log(`🎉 重新加载完成. 成功: ${loadedCount}, 失败: ${errors.length}`);

        return NextResponse.json({
            success: true,
            message: `成功重新加载 ${loadedCount} 个agents`,
            data: {
                oldCount: oldAgentCount,
                newCount: agentManager.agents.length,
                loadedCount,
                errorCount: errors.length,
                errors: errors.length > 0 ? errors : undefined
            }
        });

    } catch (error) {
        console.error('❌ 重新加载agents失败:', error);
        return NextResponse.json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined
        }, { status: 500 });
    }
}

async function loadAgent(dbAgent: any): Promise<AiAgent> {
    const connConfig = await prisma.connectConfig.findUnique({
        where: {
            id: dbAgent.connectid
        }
    });

    const chatModel: ChatModel = {};

    if (connConfig) {
        const configData = JSON.parse(connConfig.configinfo);

        if (connConfig.mtype === "llm") {
            chatModel.series = configData.driver as ModelSeries;
            chatModel.model = dbAgent.modelId;
            chatModel.apiKey = configData.apiKey;
            chatModel.baseUrl = configData.baseUrl;
        }
    }

    const agent: AiAgent = {
        id: dbAgent.id,
        name: dbAgent.name,
        description: dbAgent.description,
        systemMessage: dbAgent.prompt ?? "",
        chatModel: chatModel,
        mcpServers: [],
    };

    const mcps = await prisma.agentMcp.findMany({
        where: {
            agentId: agent.id
        }
    }) ?? [];

    for (const mcp of mcps) {
        const mcpConfig = mcpManager.get(mcp.mcpId);
        if (mcpConfig) {
            agent.mcpServers?.push(mcpConfig);
        }
    }

    console.debug(`agent #${agent.name} loaded, data: ${JSON.stringify(agent)}`);

    return agent;
} 