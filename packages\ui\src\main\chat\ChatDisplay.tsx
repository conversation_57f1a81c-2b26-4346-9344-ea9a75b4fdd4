import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import ReactMarkdown, { type Components } from 'react-markdown'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import rehypeRaw from 'rehype-raw'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { oneDark, oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { Copy, Download, Play, ChevronDown, ChevronUp, WrapText, Type } from 'lucide-react'
import styled, { createGlobalStyle } from 'styled-components'
//import { useChatStream, ChatMessage } from './useChatStream1-1'
import { MessageInput } from './MessageInput'
import { ChatHistory } from './ChatHistory'
import { getAvatarIcon } from '../../utils/avatarUtils'

// 引入KaTeX样式
import 'katex/dist/katex.min.css'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: number
  isStreaming?: boolean
}

interface ChatDisplayProps {
  // 原有的 props
  messages?: Message[]
  isLoading?: boolean
  theme?: 'light' | 'dark'
  onSendMessage?: (message: string) => void
  className?: string
  
  // 新增的大模型接口相关 props
  agentId?: string | null
  userId?: string
  agentName?: string
  agentAvatar?: string
  avatar?: React.ReactNode
  showNewChat?: boolean
  showHistory?: boolean
  showAttachment?: boolean
  showAgent?: boolean
  onHistoryToggle?: (isVisible: boolean, width: number) => void
}

interface CodeBlockProps {
  children: string
  className?: string
  language?: string
  inline?: boolean
}

const CodeBlock: React.FC<CodeBlockProps> = ({ children, className, language, inline }) => {
  const [copied, setCopied] = useState(false)
  const [collapsed, setCollapsed] = useState(false)
  const [wrapped, setWrapped] = useState(false)
  const [isRunning, setIsRunning] = useState(false)
  
  const match = /language-(\w+)/.exec(className || '')
  const lang = language || match?.[1] || 'text'
  
  const handleCopy = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(children)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }, [children])
  
  const handleDownload = useCallback(() => {
    const element = document.createElement('a')
    const file = new Blob([children], { type: 'text/plain' })
    element.href = URL.createObjectURL(file)
    element.download = `code.${lang}`
    document.body.appendChild(element)
    element.click()
    document.body.removeChild(element)
  }, [children, lang])
  
  const handleRun = useCallback(() => {
    if (lang === 'javascript' || lang === 'python') {
      setIsRunning(true)
      // 模拟代码执行
      setTimeout(() => {
        setIsRunning(false)
        console.log('Code executed:', children)
      }, 1000)
    }
  }, [lang, children])
  
  const shouldCollapse = useMemo(() => {
    return children.split('\n').length > 20
  }, [children])
  
  const displayedCode = useMemo(() => {
    if (collapsed && shouldCollapse) {
      return children.split('\n').slice(0, 10).join('\n') + '\n...'
    }
    return children
  }, [children, collapsed, shouldCollapse])
  
  if (inline) {
    return (
      <InlineCode>{children}</InlineCode>
    )
  }
  
  return (
    <CodeBlockContainer>
      <CodeBlockHeader>
        <LanguageLabel>{lang.toUpperCase()}</LanguageLabel>
        <ToolbarContainer>
          <ToolButton onClick={handleCopy} title="Copy code">
            <Copy size={14} />
            {copied && <span>Copied!</span>}
          </ToolButton>
          <ToolButton onClick={handleDownload} title="Download">
            <Download size={14} />
          </ToolButton>
          {(lang === 'javascript' || lang === 'python') && (
            <ToolButton onClick={handleRun} disabled={isRunning} title="Run code">
              <Play size={14} />
              {isRunning && <span>Running...</span>}
            </ToolButton>
          )}
          <ToolButton onClick={() => setWrapped(!wrapped)} title="Toggle wrap">
            <WrapText size={14} />
          </ToolButton>
          {shouldCollapse && (
            <ToolButton onClick={() => setCollapsed(!collapsed)} title="Toggle collapse">
              {collapsed ? <ChevronDown size={14} /> : <ChevronUp size={14} />}
            </ToolButton>
          )}
        </ToolbarContainer>
      </CodeBlockHeader>
      <CodeBlockContent $wrapped={wrapped}>
        <SyntaxHighlighter
          style={oneDark}
          language={lang}
          PreTag="div"
          wrapLines={wrapped}
          wrapLongLines={wrapped}
        >
          {displayedCode}
        </SyntaxHighlighter>
      </CodeBlockContent>
    </CodeBlockContainer>
  )
}

const ImageComponent: React.FC<{ src?: string; alt?: string; [key: string]: any }> = ({ src, alt, ...props }) => {
  if (!src) return null
  
  return (
    <ImageContainer>
      <img src={src} alt={alt} {...props} style={{ maxWidth: '100%', height: 'auto' }} />
    </ImageContainer>
  )
}

const ChatDisplay: React.FC<ChatDisplayProps> = ({ 
  // 原有的 props
  messages: propMessages,
  isLoading: propIsLoading = false,
  theme = 'dark',
  onSendMessage: propOnSendMessage,
  className,
  
  // 新增的大模型接口相关 props
  agentId = null,
  userId = "admin",
  agentName = "智能体",
  agentAvatar = "🤖",
  avatar,
  showNewChat = true,
  showHistory = true,
  showAttachment = true,
  showAgent = true,
  onHistoryToggle
}) => {
  const [inputMessage, setInputMessage] = useState('')
  const [isHistoryVisible, setIsHistoryVisible] = useState(false)
  const [historyPanelWidth, setHistoryPanelWidth] = useState(300)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)
  
  // 如果传入了 agentId，使用 useChatStream
  const {
    messages: streamMessages,
    isLoading: streamIsLoading,
    threadId,
    agentId: currentAgentId,
    isLoadingThread,
    sendMessage: sendChatMessage,
    loadThread,
    setAgent,
    startNewChat,
  } = useChatStream({
    endpoint: "/api/agentic",
    userId,
    agentId,
    agentName,
    agentAvatar,
  })
  
  // 决定使用哪个数据源
  const useStreamData = agentId !== null
  const messages = useStreamData ? streamMessages : (propMessages || [])
  const isLoading = useStreamData ? streamIsLoading : propIsLoading
  const onSendMessage = useStreamData ? sendChatMessage : propOnSendMessage
  
  // 当agentId改变时，设置新的agent
  useEffect(() => {
    if (agentId && useStreamData) {
      setAgent(agentId)
    }
  }, [agentId, setAgent, useStreamData])
  
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [])
  
  useEffect(() => {
    scrollToBottom()
  }, [messages, scrollToBottom])
  
  // 处理历史会话按钮点击
  const handleHistoryClick = () => {
    const newIsVisible = !isHistoryVisible
    setIsHistoryVisible(newIsVisible)
    onHistoryToggle?.(newIsVisible, historyPanelWidth)
  }
  
  // 处理历史会话选择
  const handleThreadSelect = async (selectedThreadId: string) => {
    if (selectedThreadId && useStreamData) {
      try {
        await loadThread(selectedThreadId)
        scrollToBottom()
      } catch (error) {
        console.error('Failed to load thread:', error)
      }
    }
  }
  
  const handleSendMessage = async (message: string) => {
    if (useStreamData) {
      try {
        await sendChatMessage(message)
        setInputMessage('')
      } catch (error) {
        console.error('Failed to send message:', error)
      }
    } else if (propOnSendMessage) {
      propOnSendMessage(message)
    }
  }
  
  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault()
    if (inputMessage.trim()) {
      handleSendMessage(inputMessage.trim())
      setInputMessage('')
    }
  }, [inputMessage, handleSendMessage])
  
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }, [handleSubmit])
  
  const components: Partial<Components> = useMemo(() => ({
    code: ({ node, inline, className, children, ...props }: any) => {
      return (
        <CodeBlock
          className={className}
          inline={inline}
          {...props}
        >
          {String(children).replace(/\n$/, '')}
        </CodeBlock>
      )
    },
    img: ImageComponent,
    blockquote: ({ children, ...props }: any) => (
      <Blockquote {...props}>{children}</Blockquote>
    ),
    table: ({ children, ...props }: any) => (
      <TableContainer>
        <table {...props}>{children}</table>
      </TableContainer>
    ),
    pre: ({ children, ...props }: any) => (
      <div {...props}>{children}</div>
    ),
    // 处理 <think> 标签，将其渲染为带样式的思考块
    think: ({ children, ...props }: any) => (
      <ThinkingBlock {...props}>{children}</ThinkingBlock>
    ),
  }), [])
  
  // 转换消息格式
  const formattedMessages = useMemo(() => {
    if (useStreamData) {
      return streamMessages.map(msg => ({
        id: msg.id,
        role: msg.role || (msg.sender === '我' ? 'user' : 'assistant'),
        content: msg.content,
        timestamp: new Date(msg.timestamp).getTime(),
        isStreaming: false
      }))
    } else {
      return propMessages || []
    }
  }, [useStreamData, streamMessages, propMessages])
  
  // 如果使用流式数据，返回带历史记录的完整界面
  if (useStreamData) {
    return (
      <ChatDisplayContainer>
        {/* 左侧历史区域 */}
        <HistoryPanel $isVisible={isHistoryVisible} $width={historyPanelWidth}>
          <ChatHistory 
            agentId={agentId || undefined} 
            activeThreadId={threadId || undefined}
            onThreadSelect={handleThreadSelect}
          />
        </HistoryPanel>
        
        {/* 右侧聊天区域 */}
        <ChatPanel>
          <ChatContainer className={className} $theme={theme}>
            <MessagesContainer>
              {formattedMessages.map((message) => (
                <MessageContainer key={message.id} $role={message.role}>
                  <MessageHeader>
                    <RoleBadge $role={message.role}>
                      {message.role === 'user' ? 'You' : 'Assistant'}
                    </RoleBadge>
                    <Timestamp>
                      {new Date(message.timestamp).toLocaleTimeString()}
                    </Timestamp>
                  </MessageHeader>
                  <MessageContent $theme={theme}>
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm, remarkMath]}
                      rehypePlugins={[rehypeKatex, rehypeRaw]}
                      components={components}
                    >
                      {message.content}
                    </ReactMarkdown>
                  </MessageContent>
                </MessageContainer>
              ))}
              {isLoading && (
                <MessageContainer $role="assistant">
                  <MessageHeader>
                    <RoleBadge $role="assistant">Assistant</RoleBadge>
                  </MessageHeader>
                  <MessageContent $theme={theme}>
                    <TypingIndicator>
                      <span></span>
                      <span></span>
                      <span></span>
                    </TypingIndicator>
                  </MessageContent>
                </MessageContainer>
              )}
              <div ref={messagesEndRef} />
            </MessagesContainer>
            
            <MessageInput
              value={inputMessage}
              onChange={setInputMessage}
              onSend={handleSendMessage}
              onNewChat={startNewChat}
              onHistoryClick={handleHistoryClick}
              isLoading={isLoading}
              placeholder="在这里输入消息..."
              showNewChat={showNewChat}
              showHistory={showHistory}
              showAttachment={showAttachment}
              showAgent={showAgent}
            />
          </ChatContainer>
        </ChatPanel>
      </ChatDisplayContainer>
    )
  }
  
  // 如果不使用流式数据，返回原有的简单界面
  return (
    <>
      <GlobalThinkStyles />
      <ChatContainer className={className} $theme={theme}>
      <MessagesContainer>
        {formattedMessages.map((message) => (
          <MessageContainer key={message.id} $role={message.role}>
            <MessageHeader>
              <RoleBadge $role={message.role}>
                {message.role === 'user' ? 'You' : 'Assistant'}
              </RoleBadge>
              <Timestamp>
                {new Date(message.timestamp).toLocaleTimeString()}
              </Timestamp>
            </MessageHeader>
            <MessageContent $theme={theme}>
              <ReactMarkdown
                remarkPlugins={[remarkGfm, remarkMath]}
                rehypePlugins={[rehypeKatex, rehypeRaw]}
                components={components}
              >
                {message.content}
              </ReactMarkdown>
            </MessageContent>
          </MessageContainer>
        ))}
        {isLoading && (
          <MessageContainer $role="assistant">
            <MessageHeader>
              <RoleBadge $role="assistant">Assistant</RoleBadge>
            </MessageHeader>
            <MessageContent $theme={theme}>
              <TypingIndicator>
                <span></span>
                <span></span>
                <span></span>
              </TypingIndicator>
            </MessageContent>
          </MessageContainer>
        )}
        <div ref={messagesEndRef} />
      </MessagesContainer>
      
      {onSendMessage && (
        <InputContainer>
          <InputForm onSubmit={handleSubmit}>
            <TextArea
              ref={inputRef}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Type your message..."
              rows={1}
            />
            <SendButton type="submit" disabled={!inputMessage.trim()}>
              Send
            </SendButton>
          </InputForm>
        </InputContainer>
      )}
      </ChatContainer>
    </>
  )
}

// 主容器 - 左右布局
const ChatDisplayContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  background-color: #0f1b3a;
`;

// 左侧历史区域
const HistoryPanel = styled.div<{ $isVisible: boolean; $width: number }>`
  width: ${props => props.$isVisible ? props.$width : 0}px;
  height: 100%;
  background-color: #0a1428;
  border-right: ${props => props.$isVisible ? '1px solid rgba(255, 255, 255, 0.1)' : 'none'};
  overflow: hidden;
  transition: width 0.3s ease;
  flex-shrink: 0;
`;

// 右侧聊天区域
const ChatPanel = styled.div`
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-width: 0;
`;

// Styled Components
const ChatContainer = styled.div<{ $theme: string }>`
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: ${props => props.$theme === 'light' ? '#333' : '#fff'};
  background-color: ${props => props.$theme === 'light' ? '#fff' : '#0f1b3a'};
`

const MessagesContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
`

const MessageContainer = styled.div<{ $role: string }>`
  display: flex;
  flex-direction: column;
  align-items: ${props => props.$role === 'user' ? 'flex-end' : 'flex-start'};
  max-width: 85%;
  ${props => props.$role === 'user' ? 'margin-left: auto;' : 'margin-right: auto;'}
`

const MessageHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
`

const RoleBadge = styled.span<{ $role: string }>`
  background: ${props => props.$role === 'user' ? '#007bff' : '#6c757d'};
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
`

const Timestamp = styled.span`
  font-size: 12px;
  color: #6c757d;
`

const MessageContent = styled.div<{ $theme?: string }>`
  background: ${props => props.$theme === 'light' ? '#f8f9fa' : 'rgba(255, 255, 255, 0.05)'};
  padding: 12px 16px;
  border-radius: 12px;
  max-width: 100%;
  
  p, main, section  {
    margin: 0 0 8px 0;
    &:last-child {
      margin-bottom: 0;
    };
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    font-size: 13px;
    font-weight: 200;
  }
  
  ul, ol {
    margin: 8px 0;
    padding-left: 20px;
    font-size:13px;
  }
  
  h1, h2, h3, h4, h5, h6 {
    margin: 16px 0 8px 0;
    &:first-child {
      margin-top: 0;
    }
  }
`

const CodeBlockContainer = styled.div`
  margin: 16px 0;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e1e5e9;
  background: #f8f9fa;
`

const CodeBlockHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #e9ecef;
  border-bottom: 1px solid #dee2e6;
`

const LanguageLabel = styled.span`
  font-size: 12px;
  font-weight: 600;
  color: #495057;
`

const ToolbarContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`

const ToolButton = styled.button`
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  color: #6c757d;
  font-size: 12px;
  transition: all 0.2s;
  
  &:hover {
    background: #dee2e6;
    color: #495057;
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`

const CodeBlockContent = styled.div<{ $wrapped: boolean }>`
  max-height: ${props => props.$wrapped ? 'none' : '400px'};
  overflow: auto;
`

const InlineCode = styled.code`
  background: #f1f3f4;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: Monaco, Consolas, 'Courier New', monospace;
  font-size: 0.9em;
  color: #d63384;
`

const ImageContainer = styled.div`
  margin: 16px 0;
  text-align: center;
`

const Blockquote = styled.blockquote`
  border-left: 4px solid #007bff;
  margin: 16px 0;
  padding-left: 16px;
  color: #6c757d;
  font-style: italic;
`

const TableContainer = styled.div`
  overflow-x: auto;
  margin: 16px 0;
  
  table {
    width: 100%;
    border-collapse: collapse;
    
    th, td {
      border: 1px solid #dee2e6;
      padding: 8px 12px;
      text-align: left;
    }
    
    th {
      background: #f8f9fa;
      font-weight: 600;
    }
  }
`

const InputContainer = styled.div`
  padding: 16px;
  border-top: 1px solid #e1e5e9;
  background: #f8f9fa;
`

const InputForm = styled.form`
  display: flex;
  gap: 8px;
  align-items: flex-end;
`

const TextArea = styled.textarea`
  flex: 1;
  min-height: 44px;
  max-height: 120px;
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 8px;
  font-family: inherit;
  font-size: 13px;
  resize: vertical;
  outline: none;
  transition: border-color 0.2s;
  
  &:focus {
    border-color: #007bff;
  }
  
  &::placeholder {
    color: #6c757d;
  }
`

const SendButton = styled.button`
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover:not(:disabled) {
    background: #0056b3;
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`

const TypingIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  
  span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6c757d;
    animation: typing 1.4s infinite ease-in-out;
    
    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.4s; }
  }
  
  @keyframes typing {
    0%, 80%, 100% { opacity: 0.5; }
    40% { opacity: 1; }
  }
`

const ThinkingBlock = styled.div`
  font-size: 12px;
  color: #bfbfbf;
  margin: 8px 0;
  padding: 8px 0;
  
  * {
    font-size: 12px !important;
    color: #bfbfbf !important;
  }
`

// 为原始 HTML think 标签添加全局样式
const GlobalThinkStyles = createGlobalStyle`
  think {
    display: block;
    font-size: 12px;
    color: #bfbfbf;
    margin: 8px 0;
    padding: 8px 0;
  }
  
  think * {
    font-size: 12px !important;
    color: #bfbfbf !important;
  }
`

export default ChatDisplay
export type { ChatDisplayProps, Message }