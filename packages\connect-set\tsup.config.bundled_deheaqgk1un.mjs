// tsup.WorkflowTrigger.json
import { defineConfig } from "tsup";
var tsup_config_default = defineConfig({
  entry: {
    index: "src/index.ts",
    // 分别构建每个连接器
    "connects/mysql": "src/connects/database/mysql/mysql.connect.ts",
    "connects/postgresql": "src/connects/database/postgresql/postgresql.connect.ts",
    "connects/rest-api": "src/connects/http/rest-api/rest-api.connect.ts",
    "connects/openai": "src/connects/llm/openai/openai.connect.ts",
    "connects/siliconflow": "src/connects/llm/siliconFlow/siliconflow.connect.ts"
  },
  format: ["cjs", "esm"],
  dts: true,
  splitting: false,
  sourcemap: true,
  clean: true,
  external: [
    "@repo/common",
    // Node.js 内置模块
    "fs",
    "path",
    "os",
    "module",
    "url",
    "util",
    "crypto",
    "stream",
    "events",
    "child_process",
    "readline",
    // 第三方 Node.js 模块
    "fast-glob",
    "inversify",
    "reflect-metadata",
    // glob 相关依赖
    "@nodelib/fs.scandir",
    "@nodelib/fs.stat",
    "@nodelib/fs.walk",
    "micromatch",
    "glob-parent",
    "merge2",
    "picomatch"
  ],
  platform: "browser",
  // 明确指定为浏览器平台
  target: "es2020",
  outDir: "dist",
  // 保持目录结构
  outExtension({ format }) {
    return {
      js: format === "cjs" ? ".js" : ".mjs"
    };
  },
  // 添加 NoEmitOnError 选项
  noExternal: [],
  // 完全排除某些包
  treeshake: true
});
export {
  tsup_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
