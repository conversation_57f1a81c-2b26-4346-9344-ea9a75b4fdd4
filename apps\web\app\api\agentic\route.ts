import { NextRequest, NextResponse } from "next/server";
import { subscribe } from "@inngest/realtime";
import { agent<PERSON>anager, inngest, AgentRunOptions } from "@repo/engine";
import { prisma } from "@repo/db";

interface AgentChatOptions extends AgentRunOptions {
    agentId: string;
}

//开启流式对话，body中包含 agentId，userId，threadId，input，state，waitOutput
//waitOutput为true，所有对话完成再输出，为false时，每一步对话输出都返回
export async function POST(req: NextRequest ) {
    try {
        console.log('🤖 POST /api/agentic 开始');
        
        const opts = await req.json() as unknown as AgentChatOptions;
        
        console.log('📝 接收到的请求参数:', opts);

        // 检查agentManager中可用的agents
        const availableAgents = agentManager.agents.map(agent => agent.id);
        console.log('📋 可用的agents:', availableAgents);

        const agent = agentManager.get(opts.agentId);
        if(agent === undefined){
            console.error(`❌ 未找到agent "${opts.agentId}"`);
            return NextResponse.json({
                error: `Unable to find agent "${opts.agentId}"`,
                availableAgents: availableAgents
            }, { status: 404 });
        }

        console.log('✅ 找到agent:', opts.agentId);

        if(opts.threadId && (await prisma.agentThread.count({
            where: { id: opts.threadId }
        })) === 0){
            console.log('💾 创建新线程:', opts.threadId);
            await prisma.agentThread.create({
                data: {
                    id: opts.threadId,
                    agentId: opts.agentId,
                    userId: opts.userId,
                    metadata: JSON.stringify({userId: opts.userId})
                }
            });
        }

        console.log('🚀 执行agent.run...');
        const result = await agent.run(opts as AgentRunOptions);
        console.log('✅ agent.run完成:', result);

        if(opts.waitOutput) {
            return NextResponse.json(result);
        } else {
            const stream = await subscribe({
                app: inngest,
                channel: `chat/${opts.agentId}/${opts.userId}`,
                topics: ["messages"],
            });

            return new Response(stream.getEncodedStream(), {
                headers: {
                    "Content-Type": "text/event-stream",
                    "Cache-Control": "no-cache",
                    Connection: "keep-alive",
                },
            });
        }
    } catch (error) {
        console.error('❌ /api/agentic 异常:', error);
        return NextResponse.json({
            error: error instanceof Error ? error.message : '内部服务器错误',
            stack: error instanceof Error ? error.stack : undefined
        }, { status: 500 });
    }
}